import axios from 'axios'
import logger from '../utils/logger.js'

const API_BASE_URL = 'http://localhost:5000/api'

/**
 * Test all major API endpoints
 */
const testAllEndpoints = async () => {
  try {
    logger.info('🧪 Testing all major API endpoints...')
    
    // Login as admin and voter
    const adminAuth = await login('admin', 'socmob123')
    const voterAuth = await login('balatan', 'bala#767')
    
    const tests = [
      // Public endpoints
      { name: 'Health Check', method: 'GET', url: '/health', auth: null },
      { name: 'Public Results', method: 'GET', url: '/results', auth: null },
      
      // Auth endpoints
      { name: 'Admin Login', method: 'POST', url: '/auth/login', auth: null, data: { username: 'admin', password: 'socmob123' } },
      
      // System endpoints (authenticated)
      { name: 'Public Settings (Voter)', method: 'GET', url: '/system/public-settings', auth: voterAuth.token },
      { name: 'Public Settings (Admin)', method: 'GET', url: '/system/public-settings', auth: adminAuth.token },
      
      // Voting endpoints
      { name: 'Voting Status', method: 'GET', url: '/voting/status', auth: voterAuth.token },
      { name: 'Candidates List', method: 'GET', url: '/candidates', auth: voterAuth.token },
      
      // Admin endpoints
      { name: 'Admin Dashboard', method: 'GET', url: '/admin/dashboard', auth: adminAuth.token },
      { name: 'Admin Users List', method: 'GET', url: '/admin/users', auth: adminAuth.token },
      { name: 'Admin Results', method: 'GET', url: '/admin/results', auth: adminAuth.token },
    ]
    
    let passedTests = 0
    let totalTests = tests.length
    
    for (const test of tests) {
      try {
        logger.info(`Testing: ${test.name}...`)
        
        const config = {
          method: test.method,
          url: `${API_BASE_URL}${test.url}`,
          data: test.data || undefined,
          headers: test.auth ? { 'Authorization': `Bearer ${test.auth}` } : {}
        }
        
        const response = await axios(config)
        
        if (response.status >= 200 && response.status < 300) {
          logger.info(`✅ ${test.name}: ${response.status} - SUCCESS`)
          passedTests++
        } else {
          logger.warn(`⚠️  ${test.name}: ${response.status} - UNEXPECTED STATUS`)
        }
        
      } catch (error) {
        const status = error.response?.status || 'NO_RESPONSE'
        const message = error.response?.data?.error || error.message
        
        if (status === 404) {
          logger.error(`❌ ${test.name}: 404 - ENDPOINT NOT FOUND`)
        } else if (status === 401) {
          logger.error(`❌ ${test.name}: 401 - UNAUTHORIZED`)
        } else if (status === 500) {
          logger.error(`❌ ${test.name}: 500 - SERVER ERROR: ${message}`)
        } else {
          logger.error(`❌ ${test.name}: ${status} - ${message}`)
        }
      }
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 200))
    }
    
    logger.info('📊 API Endpoint Test Results:')
    logger.info(`   ✅ Passed: ${passedTests}/${totalTests}`)
    logger.info(`   ❌ Failed: ${totalTests - passedTests}/${totalTests}`)
    
    if (passedTests === totalTests) {
      logger.info('🎉 All API endpoints are working correctly!')
      return true
    } else {
      logger.error('💥 Some API endpoints have issues!')
      return false
    }
    
  } catch (error) {
    logger.error('API endpoint testing failed:', error)
    return false
  }
}

/**
 * Login helper
 */
const login = async (username, password) => {
  const response = await axios.post(`${API_BASE_URL}/auth/login`, {
    username,
    password
  })

  if (response.data.success) {
    return {
      token: response.data.data.token,
      user: response.data.data.user
    }
  } else {
    throw new Error(response.data.error)
  }
}

// Run tests
testAllEndpoints()
  .then(success => {
    if (success) {
      logger.info('🎉 All API endpoints are functioning correctly!')
      process.exit(0)
    } else {
      logger.error('💥 Some API endpoints need attention!')
      process.exit(1)
    }
  })
  .catch(error => {
    logger.error('Test execution failed:', error)
    process.exit(1)
  })
