import { toast } from 'react-hot-toast'
import { useMutation, useQuery, useQueryClient } from 'react-query'
import {
    adminService,
    BulkOperationData,
    CreateUserData,
    UserFilters
} from '../services/adminService'

// Dashboard hooks
export function useAdminDashboard() {
  return useQuery(['admin', 'dashboard'], () => adminService.getDashboardStats(), {
    refetchInterval: 30000, // Refresh every 30 seconds
    staleTime: 10000, // Consider data stale after 10 seconds
  })
}

// Admin Results hooks
export function useAdminResults(showNames: boolean = false, sortBy: string = 'votes') {
  return useQuery(
    ['admin', 'results', showNames, sortBy],
    () => adminService.getAdminResults(showNames, sortBy),
    {
      refetchInterval: 15000, // Refresh every 15 seconds for real-time updates
      staleTime: 5000, // Consider data stale after 5 seconds
    }
  )
}

// User management hooks
export function useUsers(filters: UserFilters = {}) {
  return useQuery(['admin', 'users', filters], () => adminService.getUsers(filters), {
    keepPreviousData: true,
    staleTime: 5000,
  })
}

// useUser hook removed - view user functionality not needed

export function useCreateUser() {
  const queryClient = useQueryClient()

  return useMutation((userData: CreateUserData) => adminService.createUser(userData), {
    onSuccess: newUser => {
      // Invalidate and refetch users list
      queryClient.invalidateQueries(['admin', 'users'])
      queryClient.invalidateQueries(['admin', 'dashboard'])

      toast.success(`User ${newUser.municipality} created successfully`)
    },
    onError: (error: any) => {
      const message = error.response?.data?.error || 'Failed to create user'
      toast.error(message)
    },
  })
}

export function useToggleUserStatus() {
  const queryClient = useQueryClient()

  return useMutation(
    ({ id, isActive }: { id: string; isActive: boolean }) =>
      adminService.toggleUserStatus(id, isActive),
    {
      onSuccess: updatedUser => {
        // Update the user in the cache
        queryClient.setQueryData(['admin', 'users', updatedUser.id], updatedUser)

        // Invalidate users list to refresh
        queryClient.invalidateQueries(['admin', 'users'])
        queryClient.invalidateQueries(['admin', 'dashboard'])

        toast.success(`User ${updatedUser.municipality} ${updatedUser.isActive ? 'activated' : 'deactivated'} successfully`)
      },
      onError: (error: any) => {
        const message = error.response?.data?.error || 'Failed to update user status'
        toast.error(message)
      },
    }
  )
}

export function useDeleteUser() {
  const queryClient = useQueryClient()

  return useMutation((id: string) => adminService.deleteUser(id), {
    onSuccess: () => {
      // Invalidate and refetch users list
      queryClient.invalidateQueries(['admin', 'users'])
      queryClient.invalidateQueries(['admin', 'dashboard'])

      toast.success('User deleted successfully')
    },
    onError: (error: any) => {
      const message = error.response?.data?.error || 'Failed to delete user'
      toast.error(message)
    },
  })
}

export function useResetUserVote() {
  const queryClient = useQueryClient()

  return useMutation((id: string) => adminService.resetUserVote(id), {
    onSuccess: () => {
      // Invalidate and refetch users list and dashboard
      queryClient.invalidateQueries(['admin', 'users'])
      queryClient.invalidateQueries(['admin', 'dashboard'])

      toast.success('User vote reset successfully')
    },
    onError: (error: any) => {
      const message = error.response?.data?.error || 'Failed to reset user vote'
      toast.error(message)
    },
  })
}

export function useBulkUserOperations() {
  const queryClient = useQueryClient()

  return useMutation((data: BulkOperationData) => adminService.bulkUserOperations(data), {
    onSuccess: (result, variables) => {
      // Invalidate and refetch users list and dashboard
      queryClient.invalidateQueries(['admin', 'users'])
      queryClient.invalidateQueries(['admin', 'dashboard'])

      const actionMessages = {
        activate: 'activated',
        deactivate: 'deactivated',
        delete: 'deleted',
        'reset-votes': 'votes reset for',
      }

      const actionMessage = actionMessages[variables.action] || 'processed'
      toast.success(`${result.modifiedCount} users ${actionMessage} successfully`)
    },
    onError: (error: any) => {
      const message = error.response?.data?.error || 'Failed to perform bulk operation'
      toast.error(message)
    },
  })
}

// Export/Import hooks
export function useExportUsers() {
  return useMutation((format: 'csv' | 'xlsx' = 'csv') => adminService.exportUsers(format), {
    onSuccess: (blob, format) => {
      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `users.${format}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      toast.success('Users exported successfully')
    },
    onError: (error: any) => {
      const message = error.response?.data?.error || 'Failed to export users'
      toast.error(message)
    },
  })
}

export function useImportUsers() {
  const queryClient = useQueryClient()

  return useMutation((file: File) => adminService.importUsers(file), {
    onSuccess: result => {
      // Invalidate and refetch users list and dashboard
      queryClient.invalidateQueries(['admin', 'users'])
      queryClient.invalidateQueries(['admin', 'dashboard'])

      if (result.errors.length > 0) {
        toast.error(`Imported ${result.imported} users with ${result.errors.length} errors`)
      } else {
        toast.success(`${result.imported} users imported successfully`)
      }
    },
    onError: (error: any) => {
      const message = error.response?.data?.error || 'Failed to import users'
      toast.error(message)
    },
  })
}
