import { ERROR_MESSAGES } from '@/constants/errors'
import axios, { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import toast from 'react-hot-toast'

// Use proxy in development, direct URL in production
const API_BASE_URL = (import.meta as any).env?.DEV
  ? '/api' // This will use the Vite proxy
  : (import.meta as any).env?.VITE_API_URL || 'http://localhost:5000/api'

// Create axios instance
export const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  // Add withCredentials for CORS
  withCredentials: true,
})

// Request interceptor to add auth token and security headers
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = localStorage.getItem('accessToken')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
      config.headers['X-Requested-With'] = 'XMLHttpRequest'
      config.headers['X-Client-Version'] = '2.0.0'
    }

    // Add CORS headers for development
    if ((import.meta as any).env?.DEV) {
      config.headers.set('Access-Control-Allow-Origin', '*')
      config.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
      config.headers.set(
        'Access-Control-Allow-Headers',
        'Content-Type, Authorization, X-Requested-With'
      )
    }

    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling and token refresh
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  async error => {
    const originalRequest = error.config

    // Handle CORS and network errors
    if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {
      toast.error(ERROR_MESSAGES.NETWORK_ERROR, {
        duration: 5000,
      })
      return Promise.reject(new Error(ERROR_MESSAGES.NETWORK_ERROR))
    }

    // Handle CORS errors
    if (error.response?.status === 0 || error.message.includes('CORS')) {
      toast.error(ERROR_MESSAGES.CORS_ERROR, {
        duration: 5000,
      })
      return Promise.reject(new Error(ERROR_MESSAGES.CORS_ERROR))
    }

    // Handle rate limiting
    if (error.response?.status === 429) {
      toast.error(ERROR_MESSAGES.RATE_LIMITED, {
        duration: 3000,
      })
      return Promise.reject(new Error(ERROR_MESSAGES.RATE_LIMITED))
    }

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        const refreshToken = localStorage.getItem('refreshToken')
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refreshToken,
          })

          const { token, refreshToken: newRefreshToken } = response.data.data
          localStorage.setItem('accessToken', token)
          localStorage.setItem('refreshToken', newRefreshToken)

          // Retry the original request with new token
          originalRequest.headers.Authorization = `Bearer ${token}`
          return apiClient(originalRequest)
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('accessToken')
        localStorage.removeItem('refreshToken')
        localStorage.removeItem('userData')
        window.location.href = '/login'
        return Promise.reject(refreshError)
      }
    }

    // Handle other errors
    const message = error.response?.data?.message || error.message || 'An error occurred'

    // Don't show toast for certain errors
    const silentErrors = [401, 403]
    if (!silentErrors.includes(error.response?.status)) {
      toast.error(message, {
        duration: 4000,
      })
    }

    return Promise.reject(error)
  }
)
