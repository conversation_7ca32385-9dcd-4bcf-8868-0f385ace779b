import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { logger } from '@/utils/logger'
import { AlertTriangle, Eye, EyeOff, Settings } from 'lucide-react'
import { useState } from 'react'

export interface MunicipalityNamesControlProps {
  showMunicipalityNames: boolean
  onToggleNames: (show: boolean) => void
  isLoading?: boolean
}

export function MunicipalityNamesControl({
  showMunicipalityNames,
  onToggleNames,
  isLoading = false
}: MunicipalityNamesControlProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [pendingState, setPendingState] = useState<boolean | null>(null)

  const handleToggleClick = (checked: boolean) => {
    setPendingState(checked)
    setShowConfirmDialog(true)
  }

  const handleConfirm = () => {
    if (pendingState !== null) {
      onToggleNames(pendingState)
      logger.info('Admin toggled municipality names visibility', {
        metadata: {
          newState: pendingState ? 'visible' : 'hidden',
          timestamp: new Date().toISOString()
        }
      })
    }
    setShowConfirmDialog(false)
    setPendingState(null)
  }

  const handleCancel = () => {
    setShowConfirmDialog(false)
    setPendingState(null)
  }

  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Settings className="h-5 w-5 text-blue-500" />
            <CardTitle className="text-lg">Municipality Names Display</CardTitle>
          </div>
          <Badge
            variant={showMunicipalityNames ? "default" : "secondary"}
            className="flex items-center gap-1"
          >
            {showMunicipalityNames ? (
              <>
                <Eye className="h-3 w-3" />
                Visible
              </>
            ) : (
              <>
                <EyeOff className="h-3 w-3" />
                Hidden
              </>
            )}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor="municipality-names" className="text-sm font-medium">
              Show Municipality Names in Public Results
            </Label>
            <p className="text-xs text-muted-foreground">
              Control whether actual municipality names are shown in public results or kept anonymous
            </p>
          </div>

          <Switch
            id="municipality-names"
            checked={showMunicipalityNames}
            onCheckedChange={handleToggleClick}
            disabled={isLoading}
          />
        </div>

        <div className="rounded-lg bg-muted/50 p-3">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0" />
            <div className="text-xs text-muted-foreground">
              <p className="font-medium text-foreground mb-1">Important:</p>
              <ul className="space-y-1">
                <li>• When <strong>enabled</strong>: Municipality names are visible in public results</li>
                <li>• When <strong>disabled</strong>: Results show "Candidate 1", "Candidate 2", etc.</li>
                <li>• Changes take effect immediately on the public results page</li>
                <li>• This setting only affects public results, not admin results</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="text-xs text-muted-foreground">
          <p>
            <strong>Current Status:</strong> Municipality names are{' '}
            <span className={showMunicipalityNames ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
              {showMunicipalityNames ? 'visible to public' : 'hidden from public'}
            </span>
          </p>
        </div>

        <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {pendingState ? 'Show' : 'Hide'} Municipality Names in Public Results?
              </AlertDialogTitle>
              <AlertDialogDescription>
                {pendingState ? (
                  <>
                    This will make <strong>municipality names visible</strong> in public results.
                    The public will see actual municipality names instead of anonymous candidate numbers.
                  </>
                ) : (
                  <>
                    This will <strong>hide municipality names</strong> from public results.
                    The public will see "Candidate 1", "Candidate 2", etc. instead of municipality names.
                  </>
                )}
                <br /><br />
                This change will take effect immediately on the public results page. Are you sure you want to continue?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={handleCancel}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleConfirm}
                className={pendingState ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
              >
                {pendingState ? 'Show Municipality Names' : 'Hide Municipality Names'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  )
}
