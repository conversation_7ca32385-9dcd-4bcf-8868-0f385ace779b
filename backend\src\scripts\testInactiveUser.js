import mongoose from 'mongoose'
import dotenv from 'dotenv'
import axios from 'axios'
import logger from '../utils/logger.js'

// Load environment variables
dotenv.config()

const API_BASE_URL = 'http://localhost:5000/api'

/**
 * Test inactive user access control
 */
const testInactiveUserAccess = async () => {
  try {
    logger.info('🔒 Testing inactive user access control...')
    
    // Connect to database
    const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URI
    await mongoose.connect(mongoUri)
    
    const User = (await import('../models/User.js')).default
    
    // Find a test user to temporarily deactivate
    const testUser = await User.findOne({ username: 'cabusao' })
    if (!testUser) {
      throw new Error('Test user not found')
    }
    
    const originalStatus = testUser.isActive
    logger.info(`📋 Test user: ${testUser.username} (${testUser.municipality})`)
    logger.info(`📋 Original status: ${originalStatus ? 'ACTIVE' : 'INACTIVE'}`)
    
    try {
      // Step 1: Test active user first
      logger.info('1️⃣ Testing active user access...')
      testUser.isActive = true
      await testUser.save()
      
      const activeAuth = await login('cabusao', 'cabu=538')
      const activeVotingStatus = await getVotingStatus(activeAuth.token)
      
      logger.info(`🗳️ Active user can vote: ${activeVotingStatus.canVote}`)
      logger.info(`📝 Active user status: ${activeVotingStatus.votingStatus}`)
      logger.info(`💬 Active user message: ${activeVotingStatus.message}`)
      
      // Step 2: Deactivate user and test
      logger.info('2️⃣ Deactivating user and testing access...')
      testUser.isActive = false
      await testUser.save()
      
      // Login should still work (inactive users can still log in)
      const inactiveAuth = await login('cabusao', 'cabu=538')
      logger.info('✅ Inactive user can still log in')
      
      // But voting should be blocked
      const inactiveVotingStatus = await getVotingStatus(inactiveAuth.token)
      
      logger.info(`🗳️ Inactive user can vote: ${inactiveVotingStatus.canVote}`)
      logger.info(`📝 Inactive user status: ${inactiveVotingStatus.votingStatus}`)
      logger.info(`💬 Inactive user message: ${inactiveVotingStatus.message}`)
      
      // Verify inactive user is properly blocked
      if (!inactiveVotingStatus.canVote && inactiveVotingStatus.votingStatus === 'user-disabled') {
        logger.info('✅ SUCCESS: Inactive user properly blocked from voting')
      } else {
        logger.error('❌ FAILURE: Inactive user was not properly blocked from voting')
        return false
      }
      
      // Step 3: Test that inactive users are excluded from candidate lists
      logger.info('3️⃣ Testing candidate list exclusion...')
      const candidatesResponse = await axios.get(`${API_BASE_URL}/candidates`, {
        headers: { 'Authorization': `Bearer ${inactiveAuth.token}` }
      })
      
      if (candidatesResponse.data.success) {
        const candidates = candidatesResponse.data.data
        const inactiveUserCandidate = candidates.find(c => c.municipalityName === testUser.municipality)
        
        if (!inactiveUserCandidate) {
          logger.info('✅ SUCCESS: Inactive user\'s municipality not in candidate list')
        } else {
          logger.info('ℹ️  NOTE: Inactive user\'s municipality still in candidate list (this may be expected)')
        }
      }
      
      logger.info('📋 Inactive User Test Results:')
      logger.info('✅ Inactive user can log in: PASSED')
      logger.info('✅ Inactive user blocked from voting: PASSED')
      logger.info('✅ Proper error messages shown: PASSED')
      
      return true
      
    } finally {
      // Restore original status
      testUser.isActive = originalStatus
      await testUser.save()
      logger.info(`🔓 Restored user status: ${testUser.username} -> ${originalStatus ? 'ACTIVE' : 'INACTIVE'}`)
    }
    
  } catch (error) {
    logger.error('💥 Inactive user test failed:', error)
    return false
  } finally {
    await mongoose.connection.close()
  }
}

/**
 * Login helper
 */
const login = async (username, password) => {
  const response = await axios.post(`${API_BASE_URL}/auth/login`, {
    username,
    password
  })

  if (response.data.success) {
    return {
      token: response.data.data.token,
      user: response.data.data.user
    }
  } else {
    throw new Error(response.data.error)
  }
}

/**
 * Get voting status helper
 */
const getVotingStatus = async (token) => {
  const response = await axios.get(`${API_BASE_URL}/voting/status`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })

  if (response.data.success) {
    return response.data.data
  } else {
    throw new Error(response.data.error)
  }
}

// Run test
testInactiveUserAccess()
  .then(success => {
    if (success) {
      logger.info('🎉 Inactive user access control is working correctly!')
      process.exit(0)
    } else {
      logger.error('💥 Inactive user access control has issues!')
      process.exit(1)
    }
  })
  .catch(error => {
    logger.error('Test execution failed:', error)
    process.exit(1)
  })
