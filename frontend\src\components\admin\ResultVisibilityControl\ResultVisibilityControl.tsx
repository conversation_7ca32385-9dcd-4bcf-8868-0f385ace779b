import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, Eye, EyeOff, Settings } from 'lucide-react'
import { useState } from 'react'

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { logger } from '@/utils/logger'

export interface ResultVisibilityControlProps {
  isResultsVisible: boolean
  onToggleVisibility: (visible: boolean) => void
  isLoading?: boolean
}

export function ResultVisibilityControl({
  isResultsVisible,
  onToggleVisibility,
  isLoading = false
}: ResultVisibilityControlProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [pendingState, setPendingState] = useState<boolean | null>(null)

  const handleToggleClick = (checked: boolean) => {
    setPendingState(checked)
    setShowConfirmDialog(true)
  }

  const handleConfirm = () => {
    if (pendingState !== null) {
      onToggleVisibility(pendingState)
      logger.info('Admin toggled result visibility', {
        metadata: {
          newState: pendingState ? 'visible' : 'hidden',
          timestamp: new Date().toISOString()
        }
      })
    }
    setShowConfirmDialog(false)
    setPendingState(null)
  }

  const handleCancel = () => {
    setShowConfirmDialog(false)
    setPendingState(null)
  }

  return (
    <Card className="border-l-4 border-l-primary">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Settings className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">Public Results Visibility</CardTitle>
          </div>
          <Badge
            variant={isResultsVisible ? "default" : "secondary"}
            className="flex items-center gap-1"
          >
            {isResultsVisible ? (
              <>
                <Eye className="h-3 w-3" />
                Public
              </>
            ) : (
              <>
                <EyeOff className="h-3 w-3" />
                Hidden
              </>
            )}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor="results-visibility" className="text-sm font-medium">
              Enable Public Results Viewing
            </Label>
            <p className="text-xs text-muted-foreground">
              Control whether election results are visible to voters and the public
            </p>
          </div>

          <Switch
            id="results-visibility"
            checked={isResultsVisible}
            onCheckedChange={handleToggleClick}
            disabled={isLoading}
          />
        </div>

        <div className="rounded-lg bg-muted/50 p-3">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0" />
            <div className="text-xs text-muted-foreground">
              <p className="font-medium text-foreground mb-1">Important:</p>
              <ul className="space-y-1">
                <li>• When <strong>enabled</strong>: Results are visible to all users at <code>/results</code></li>
                <li>• When <strong>disabled</strong>: Only admins can view results</li>
                <li>• Changes take effect immediately</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="text-xs text-muted-foreground">
          <p>
            <strong>Current Status:</strong> Results are{' '}
            <span className={isResultsVisible ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
              {isResultsVisible ? 'publicly visible' : 'hidden from public'}
            </span>
          </p>
        </div>

        <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {pendingState ? 'Enable' : 'Disable'} Public Results Visibility?
              </AlertDialogTitle>
              <AlertDialogDescription>
                {pendingState ? (
                  <>
                    This will make election results <strong>publicly visible</strong> to all users.
                    Voters and the public will be able to view results at the <code>/results</code> page.
                  </>
                ) : (
                  <>
                    This will <strong>hide election results</strong> from public view.
                    Only administrators will be able to access results.
                  </>
                )}
                <br /><br />
                This change will take effect immediately. Are you sure you want to continue?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={handleCancel}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleConfirm}
                className={pendingState ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
              >
                {pendingState ? 'Enable Public Access' : 'Hide from Public'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  )
}
