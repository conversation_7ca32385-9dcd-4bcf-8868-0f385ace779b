import { validationResult } from 'express-validator'
import mongoose from 'mongoose'
import ActivityLog from '../models/ActivityLog.js'
import Candidate from '../models/Candidate.js'
import ElectionArchive from '../models/ElectionArchive.js'
import SystemSettings from '../models/SystemSettings.js'
import User from '../models/User.js'
import Vote from '../models/Vote.js'
import VoteBackup from '../models/VoteBackup.js'
import VoteReset from '../models/VoteReset.js'
import ActivityLogService from '../services/activityLogService.js'
import { generateElectionResultsDOCX } from '../services/docxExportService.js'
import { generateElectionResultsPDF } from '../services/pdfExportService.js'
import logger from '../utils/logger.js'

/**
 * @desc    Get all users with pagination and filtering
 * @route   GET /api/admin/users
 * @access  Private/Admin
 */
export const getUsers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      sort = 'createdAt',
      order = 'desc',
      search,
      role,
      municipality,
      hasVoted,
    } = req.query

    // Build filter object - exclude admin users from user management
    const filter = { role: { $ne: 'admin' } }

    if (search) {
      filter.$or = [
        { username: { $regex: search, $options: 'i' } },
        { municipality: { $regex: search, $options: 'i' } },
      ]
    }

    if (role && role !== 'admin') filter.role = role // Prevent admin role filtering
    if (municipality) filter.municipality = municipality
    if (hasVoted !== undefined) filter.hasVoted = hasVoted === 'true'

    // Build sort object
    const sortObj = {}
    sortObj[sort] = order === 'desc' ? -1 : 1

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit)

    // Execute queries
    const [users, total] = await Promise.all([
      User.find(filter).sort(sortObj).skip(skip).limit(parseInt(limit)).select('-refreshTokens'),
      User.countDocuments(filter),
    ])

    // Calculate pagination info
    const totalPages = Math.ceil(total / parseInt(limit))
    const hasNextPage = page < totalPages
    const hasPrevPage = page > 1

    res.status(200).json({
      success: true,
      data: {
        users,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalUsers: total,
          hasNextPage,
          hasPrevPage,
          limit: parseInt(limit),
        },
      },
    })
  } catch (error) {
    logger.error('Get users error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error retrieving users',
    })
  }
}

// getUserById function removed - view user functionality not needed

/**
 * @desc    Create new user
 * @route   POST /api/admin/users
 * @access  Private/Admin
 */
export const createUser = async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array(),
      })
    }

    const { username, municipality, password, role } = req.body
    const userRole = role || 'voter'

    // Check account limits before creating user
    try {
      await User.checkAccountLimits(userRole)
    } catch (error) {
      return res.status(400).json({
        success: false,
        error: error.message,
      })
    }

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ username }, { municipality }],
    })

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error:
          existingUser.username === username
            ? 'Username already exists'
            : 'Municipality already has a user',
      })
    }

    // Create user
    const user = new User({
      username,
      municipality,
      password,
      role: userRole,
    })

    await user.save()

    logger.info(`Admin ${req.user.username} created user ${user.username}`)

    res.status(201).json({
      success: true,
      data: { user },
      message: 'User created successfully',
    })
  } catch (error) {
    logger.error('Create user error:', error)

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        error: 'User with this username or municipality already exists',
      })
    }

    res.status(500).json({
      success: false,
      error: 'Server error creating user',
    })
  }
}

/**
 * @desc    Toggle user active status
 * @route   PUT /api/admin/users/:id/status
 * @access  Private/Admin
 */
export const toggleUserStatus = async (req, res) => {
  try {
    const { isActive } = req.body

    if (typeof isActive !== 'boolean') {
      return res.status(400).json({
        success: false,
        error: 'isActive must be a boolean value',
      })
    }

    const user = await User.findById(req.params.id)
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      })
    }

    // Prevent admin from deactivating themselves
    if (user._id.toString() === req.user.id && isActive === false) {
      return res.status(400).json({
        success: false,
        error: 'Cannot deactivate your own account',
      })
    }

    // Update user status
    user.isActive = isActive
    await user.save()

    logger.info(`Admin ${req.user.username} ${isActive ? 'activated' : 'deactivated'} user ${user.username}`)

    res.status(200).json({
      success: true,
      data: { user },
      message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
    })
  } catch (error) {
    logger.error('Toggle user status error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error updating user status',
    })
  }
}



/**
 * @desc    Get dashboard statistics
 * @route   GET /api/admin/dashboard
 * @access  Private/Admin
 */
export const getDashboardStats = async (_req, res) => {
  try {
    const [
      totalUsers,
      totalVoters,
      totalActiveVoters,
      votedUsers,
      activeUsers,
      usersByRole,
      recentUsers
    ] = await Promise.all([
        User.countDocuments(), // Total Users (should be 36: 35 candidates + 1 admin)
        User.countDocuments({ role: 'voter' }), // Voters/Candidates Registered (35 municipalities)
        User.countDocuments({ role: 'voter', isActive: true }), // Active voters only
        User.countDocuments({ role: 'voter', hasVoted: true, isActive: true }), // Votes cast by active voters
        User.countDocuments({ isActive: true }), // All active users
        User.aggregate([{ $group: { _id: '$role', count: { $sum: 1 } } }]),
        User.find({ isActive: true })
          .sort({ createdAt: -1 })
          .limit(5)
          .select('username municipality role createdAt'),
      ])

    // Calculate statistics according to requirements
    const pendingVoters = totalActiveVoters - votedUsers // Active voters who haven't voted yet
    const votingProgress = totalVoters > 0 ? (votedUsers / totalVoters) * 100 : 0 // Based on all 35 registered

    res.status(200).json({
      success: true,
      data: {
        stats: {
          totalUsers, // 36 (35 candidates + 1 admin)
          totalVoters, // 35 municipalities (Voters/Candidates Registered)
          totalActiveVoters, // Active voters/candidates
          votedUsers, // Total votes cast
          pendingVoters, // Pending voters (eligible to vote)
          activeUsers, // All active users
          votingProgress: Math.round(votingProgress * 100) / 100, // (votes cast / 35 registered) × 100%
        },
        usersByRole: usersByRole.reduce((acc, item) => {
          acc[item._id] = item.count
          return acc
        }, {}),
        recentUsers,
      },
    })
  } catch (error) {
    logger.error('Get dashboard stats error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error retrieving dashboard statistics',
    })
  }
}

/**
 * @desc    Reset user's vote
 * @route   POST /api/admin/users/:id/reset-vote
 * @access  Private/Admin
 */
export const resetUserVote = async (req, res) => {
  try {
    const { reason = 'Admin reset' } = req.body
    const user = await User.findById(req.params.id)

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      })
    }

    // Find all votes by this user to backup before reset
    const userVotes = await Vote.find({ voterId: user._id })

    // Check if user has voted at all
    if (!user.hasVoted) {
      return res.status(400).json({
        success: false,
        error: 'User has not voted yet, nothing to reset',
      })
    }

    if (userVotes.length === 0) {
      // User is marked as voted but no votes found - this is a data inconsistency
      // Handle this gracefully by fixing the inconsistency and treating it as a successful reset
      logger.info(`Fixing data inconsistency for user ${user.username}: marked as voted but no votes found`)

      // Reset the hasVoted flag to fix the inconsistency
      user.hasVoted = false
      await user.save()

      logger.info(`Admin ${req.user.username} reset vote status for user ${user.username} (data inconsistency corrected)`)

      return res.status(200).json({
        success: true,
        message: 'User voting status has been successfully reset',
        data: {
          username: user.username,
          municipality: user.municipality,
          hasVoted: false,
          votesRemoved: 0,
          inconsistencyFixed: true
        }
      })
    }

    // Create backups for all votes before deletion
    const backupPromises = userVotes.map(vote =>
      VoteBackup.createBackup(
        vote,
        'reset',
        `Vote reset by admin: ${reason}`,
        req.user.id,
        req.ip,
        req.get('User-Agent')
      )
    )

    await Promise.all(backupPromises)

    // Get candidates that will have their vote counts reduced
    const candidateIds = [...new Set(userVotes.map(vote => vote.candidateId))]

    // Use transaction to ensure data consistency
    const session = await mongoose.startSession()

    try {
      await session.withTransaction(async () => {
        const candidates = await Candidate.find({ _id: { $in: candidateIds } }).session(session)

        // Reduce candidate vote counts
        for (const vote of userVotes) {
          const candidate = candidates.find(c => c._id.toString() === vote.candidateId.toString())
          if (candidate) {
            candidate.totalVotes = Math.max(0, candidate.totalVotes - 1)
            await candidate.save({ session })
            logger.info(`Decremented vote count for ${candidate.municipalityName}: ${candidate.totalVotes + 1} -> ${candidate.totalVotes}`)
          }
        }

        // Delete the votes
        await Vote.deleteMany({ voterId: user._id }, { session })

        // Reset user voting status
        user.hasVoted = false
        await user.save({ session })
      })
    } finally {
      await session.endSession()
    }

    // Log activity
    await ActivityLog.logActivity({
      activityType: 'vote_reset',
      performedBy: req.user.id,
      targetUser: user._id,
      description: `Admin ${req.user.username} reset ${userVotes.length} votes for user ${user.username}`,
      metadata: {
        resetReason: reason,
        votesReset: userVotes.length,
        candidatesAffected: candidateIds.length,
        adminMunicipality: req.user.municipality,
        targetMunicipality: user.municipality,
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      requestMethod: req.method,
      requestUrl: req.originalUrl,
      responseStatus: 200,
      severity: 'high',
      category: 'administration',
    })

    logger.info(
      `Admin ${req.user.username} reset ${userVotes.length} votes for user ${user.username}`
    )

    res.status(200).json({
      success: true,
      message: 'User votes reset successfully',
      data: {
        votesReset: userVotes.length,
        candidatesAffected: candidateIds.length,
        backupsCreated: userVotes.length,
      },
    })
  } catch (error) {
    logger.error('Reset user vote error:', error)

    // Handle specific errors
    if (error.name === 'CastError') {
      return res.status(400).json({
        success: false,
        error: 'Invalid user ID format',
      })
    }

    res.status(500).json({
      success: false,
      error: 'Server error resetting user vote',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

/**
 * @desc    Archive current election results
 * @route   POST /api/admin/election/archive
 * @access  Private/Admin
 */
export const archiveElectionResults = async (req, res) => {
  try {
    const { title, year, description = '' } = req.body

    // Require title field - no longer support year-based naming
    if (!title) {
      return res.status(400).json({
        success: false,
        error: 'Title is required',
      })
    }

    const archiveTitle = title.trim()

    if (typeof archiveTitle !== 'string' || archiveTitle.trim().length < 3) {
      return res.status(400).json({
        success: false,
        error: 'Title must be at least 3 characters long',
      })
    }

    if (archiveTitle.trim().length > 100) {
      return res.status(400).json({
        success: false,
        error: 'Title must be 100 characters or less',
      })
    }

    // Allow unlimited archive creation with custom titles - no uniqueness restriction
    logger.info(`Creating archive with title: "${archiveTitle}"`)

    // Get comprehensive election data
    const [candidates, allCandidates, voters, systemSettings] = await Promise.all([
      Candidate.find({ isActive: true })
        .select('municipalityName district totalVotes')
        .sort({ totalVotes: -1, municipalityName: 1 }),
      Candidate.find({})
        .select('municipalityName district totalVotes isActive'),
      User.find({ role: 'voter' })
        .select('username municipality district hasVoted isActive'),
      SystemSettings.find({})
    ])

    const totalVoters = voters.length
    const totalVotesCast = voters.filter(v => v.hasVoted).length
    const participationRate = totalVoters > 0 ? (totalVotesCast / totalVoters) * 100 : 0

    // Get system settings at time of archiving
    const settingsSnapshot = {}
    systemSettings.forEach(setting => {
      settingsSnapshot[setting.key] = setting.value
    })

    // Generate filename in format "Month DD, YYYY"
    const archiveDate = new Date()
    const filename = archiveDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: '2-digit'
    })

    // Create comprehensive archive with all election data
    const archive = new ElectionArchive({
      title: archiveTitle.trim(),
      year: new Date().getFullYear(), // Add year for backward compatibility
      description,
      filename,
      results: candidates.map((candidate, index) => ({
        rank: index + 1,
        municipalityName: candidate.municipalityName,
        district: candidate.district,
        voteCount: candidate.totalVotes
      })),
      statistics: {
        totalVoters,
        totalVotesCast,
        participationRate: Math.round(participationRate * 100) / 100,
        totalCandidates: candidates.length,
        totalActiveCandidates: allCandidates.filter(c => c.isActive).length,
        totalInactiveCandidates: allCandidates.filter(c => !c.isActive).length,
        electionStartTime: new Date(), // This should be tracked from when voting started
        electionEndTime: new Date(), // This should be tracked from when voting ended
      },
      // Store anonymized vote records for audit purposes
      voteRecords: voters.filter(v => v.hasVoted).map(voter => ({
        voterDistrict: voter.district,
        voterMunicipality: voter.municipality,
        votedAt: new Date(), // This should be the actual vote timestamp
        // Note: Individual vote choices are not stored for privacy
      })),
      // Store complete candidate list at time of archiving
      candidateList: allCandidates.map(candidate => ({
        municipalityName: candidate.municipalityName,
        district: candidate.district,
        totalVotes: candidate.totalVotes,
        isActive: candidate.isActive
      })),
      // Store system settings at time of election
      systemSettings: settingsSnapshot,
      // Archive metadata
      archivedBy: req.user.id,
      archivedAt: new Date()
    })

    // Add validation before saving
    logger.info('Attempting to save archive with data:', {
      title: archiveTitle.trim(),
      year: new Date().getFullYear(),
      description,
      filename,
      resultsCount: candidates.length,
      votersCount: voters.length,
      votedUsersCount: voters.filter(v => v.hasVoted).length,
      statisticsKeys: Object.keys({
        totalVoters,
        totalVotesCast,
        participationRate: Math.round(participationRate * 100) / 100,
        totalCandidates: candidates.length,
        totalActiveCandidates: allCandidates.filter(c => c.isActive).length,
        totalInactiveCandidates: allCandidates.filter(c => !c.isActive).length,
      })
    })

    // Validate archive data before saving
    try {
      await archive.validate()
      logger.info('Archive validation passed')
    } catch (validationError) {
      logger.error('Archive validation failed:', validationError)
      return res.status(400).json({
        success: false,
        error: 'Archive validation failed',
        details: validationError.message,
        validationErrors: validationError.errors
      })
    }

    await archive.save()

    logger.info(`Admin ${req.user.username} archived election results: "${archiveTitle}"`, {
      title: archiveTitle,
      year: year || new Date().getFullYear(),
      description,
      totalCandidates: candidates.length,
      totalVotesCast,
      participationRate
    })

    res.status(201).json({
      success: true,
      message: `Election results archived successfully: "${archiveTitle}"`,
      data: { archive }
    })
  } catch (error) {
    logger.error('Archive election results error:', error)

    // Handle specific MongoDB errors
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        error: 'An archive with this title already exists'
      })
    }

    // Handle validation errors
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message)
      return res.status(400).json({
        success: false,
        error: `Validation error: ${validationErrors.join(', ')}`
      })
    }

    res.status(500).json({
      success: false,
      error: 'Server error archiving election results',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

/**
 * @desc    Reset all votes and prepare for new election
 * @route   POST /api/admin/election/reset
 * @access  Private/Admin
 */
export const resetElectionSystem = async (req, res) => {
  try {
    const { confirmationText, reason = 'New election preparation' } = req.body

    // Require confirmation text for safety
    if (confirmationText !== 'RESET ELECTION SYSTEM') {
      return res.status(400).json({
        success: false,
        error: 'Invalid confirmation text. Please type "RESET ELECTION SYSTEM" exactly.'
      })
    }

    // Get current statistics before reset
    const [allVotes, allUsers, allCandidates] = await Promise.all([
      Vote.find({}),
      User.find({ role: 'voter' }),
      Candidate.find({}) // Get ALL candidates for complete backup
    ])

    // Create comprehensive backup before reset
    const resetBackup = {
      resetDate: new Date(),
      resetBy: req.user.id,
      reason,
      statistics: {
        totalVotes: allVotes.length,
        totalVoters: allUsers.length,
        totalCandidates: allCandidates.length,
        votersWhoVoted: allUsers.filter(u => u.hasVoted).length
      },
      votes: allVotes,
      voterStatus: allUsers.map(u => ({
        userId: u._id,
        username: u.username,
        municipality: u.municipality,
        hasVoted: u.hasVoted
      })),
      candidateVotes: allCandidates.map(c => ({
        candidateId: c._id,
        municipalityName: c.municipalityName,
        district: c.district,
        totalVotes: c.totalVotes
      }))
    }

    // Log the backup data instead of creating a VoteBackup record
    // (VoteBackup is designed for individual vote backups, not system-wide resets)
    logger.info('System reset backup data:', JSON.stringify(resetBackup, null, 2))

    // Reset ALL candidate vote counts (both active and inactive)
    await Candidate.updateMany(
      {}, // No filter - reset all candidates
      { $set: { totalVotes: 0 } }
    )

    // Reset all user voting status
    await User.updateMany(
      { role: 'voter' },
      { $set: { hasVoted: false } }
    )

    // Delete all votes
    await Vote.deleteMany({})

    // Log activity
    await ActivityLog.logActivity({
      activityType: 'election_reset',
      performedBy: req.user.id,
      description: `Admin ${req.user.username} performed system reset`,
      metadata: {
        reason,
        votesDeleted: allVotes.length,
        votersReset: allUsers.length,
        candidatesReset: allCandidates.length,
        backupLogged: true
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      requestMethod: req.method,
      requestUrl: req.originalUrl,
      responseStatus: 200,
      severity: 'critical',
      category: 'administration'
    })

    logger.info(`Admin ${req.user.username} performed system reset`)

    res.status(200).json({
      success: true,
      message: 'Election system reset successfully',
      data: {
        votesDeleted: allVotes.length,
        votersReset: allUsers.length,
        candidatesReset: allCandidates.length,
        resetTimestamp: new Date()
      }
    })
  } catch (error) {
    logger.error('Reset election system error:', error)

    // Handle specific errors
    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        error: `Validation error: ${error.message}`
      })
    }

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        error: 'Database constraint error during reset'
      })
    }

    res.status(500).json({
      success: false,
      error: 'Server error resetting election system',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

/**
 * @desc    Bulk operations on users
 * @route   POST /api/admin/users/bulk
 * @access  Private/Admin
 */
export const bulkUserOperations = async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array(),
      })
    }

    const { userIds, action } = req.body

    // Prevent admin from performing bulk operations on themselves
    if (userIds.includes(req.user.id)) {
      return res.status(400).json({
        success: false,
        error: 'Cannot perform bulk operations on your own account',
      })
    }

    let updateQuery = {}
    let message = ''

    switch (action) {
      case 'activate':
        updateQuery = { isActive: true }
        message = 'Users activated successfully'
        break
      case 'deactivate':
        updateQuery = { isActive: false }
        message = 'Users deactivated successfully'
        break
      case 'reset-votes':
        updateQuery = { hasVoted: false }
        message = 'User votes reset successfully'
        break

      default:
        return res.status(400).json({
          success: false,
          error: 'Invalid bulk action',
        })
    }

    const result = await User.updateMany({ _id: { $in: userIds } }, updateQuery)

    logger.info(
      `Admin ${req.user.username} performed bulk ${action} on ${result.modifiedCount} users`
    )

    res.status(200).json({
      success: true,
      message,
      data: {
        modifiedCount: result.modifiedCount,
      },
    })
  } catch (error) {
    logger.error('Bulk user operations error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error performing bulk operations',
    })
  }
}

/**
 * @desc    Get admin voting results with unified ranking and toggle functionality
 * @route   GET /api/admin/results
 * @access  Private/Admin
 */
export const getAdminResults = async (req, res) => {
  try {
    const { showNames = 'false', sortBy = 'votes', viewMode = 'unified' } = req.query
    const showCandidateNames = showNames === 'true'

    // Get all candidates with their vote counts (including inactive ones)
    const candidates = await Candidate.find({})
      .select('municipalityName district totalVotes isActive')
      .sort({ totalVotes: -1, municipalityName: 1 })

    // Get inactive users to determine actual user status
    const inactiveUsers = await User.find({
      role: 'voter',
      isActive: false
    }).select('municipality')

    const inactiveMunicipalities = new Set(inactiveUsers.map(user => user.municipality))

    // Create unified ranking (1-35 based on total votes, including inactive candidates)
    const unifiedResults = candidates.map((candidate, index) => {
      const isUserActive = !inactiveMunicipalities.has(candidate.municipalityName)
      return {
        municipalityName: candidate.municipalityName,
        district: candidate.district,
        voteCount: candidate.totalVotes,
        rank: index + 1, // Overall rank from 1-35
        candidateName: showCandidateNames ? candidate.municipalityName : undefined,
        isActive: isUserActive, // Use User model isActive status, not Candidate model
        status: isUserActive ? 'Active' : 'Absent'
      }
    })

    // Group results by district (for legacy compatibility)
    const resultsByDistrict = candidates.reduce((acc, candidate) => {
      const district = candidate.district
      if (!acc[district]) {
        acc[district] = []
      }

      const result = {
        municipalityName: candidate.municipalityName,
        district: candidate.district,
        voteCount: candidate.totalVotes,
        rank: 0, // Will be calculated per district
      }

      // Add candidate name if toggle is enabled
      if (showCandidateNames) {
        result.candidateName = candidate.municipalityName
      }

      acc[district].push(result)
      return acc
    }, {})

    // Calculate ranks within each district and sort
    Object.keys(resultsByDistrict).forEach(district => {
      resultsByDistrict[district] = resultsByDistrict[district]
        .sort((a, b) => {
          if (sortBy === 'votes') {
            return b.voteCount - a.voteCount
          }
          return a.municipalityName.localeCompare(b.municipalityName)
        })
        .map((candidate, index) => ({
          ...candidate,
          rank: index + 1,
        }))
    })

    // Get overall statistics with proper inactive user handling
    const totalRegisteredVoters = await User.countDocuments({ role: 'voter' }) // All 35 registered voters
    const totalActiveVoters = await User.countDocuments({ role: 'voter', isActive: true }) // Active participants
    const totalInactiveVoters = await User.countDocuments({ role: 'voter', isActive: false }) // Absent participants
    const totalVotesCast = await User.countDocuments({
      role: 'voter',
      hasVoted: true,
      isActive: true,
    })
    const totalVotes = candidates.reduce((sum, candidate) => sum + candidate.totalVotes, 0)
    const participationRate = totalRegisteredVoters > 0 ? (totalVotesCast / totalRegisteredVoters) * 100 : 0

    const activeCandidates = candidates.filter(c => c.isActive !== false)
    const totalStats = {
      totalVotes,
      totalRegisteredVoters, // All 35 registered voters
      totalActiveVoters, // Active participants only
      totalInactiveVoters, // Absent participants
      totalVotesCast,
      participationRate: Math.round(participationRate * 100) / 100,
      totalCandidates: candidates.length,
      totalActiveCandidates: activeCandidates.length,
      message: `${totalVotesCast} of 35 registered voters (${Math.round(participationRate * 100) / 100}%)`
    }

    res.status(200).json({
      success: true,
      data: {
        resultsByDistrict,
        unifiedResults, // New unified ranking (1-35)
        totalStats,
        showCandidateNames,
        sortBy,
        viewMode,
      },
    })
  } catch (error) {
    logger.error('Get admin results error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error retrieving voting results',
    })
  }
}

/**
 * @desc    Get activity logs with filtering and pagination
 * @route   GET /api/admin/activity-logs
 * @access  Private/Admin
 */
export const getActivityLogs = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      activityType,
      category,
      severity,
      isSecurityEvent,
      startDate,
      endDate,
      userId,
    } = req.query

    const filters = {}
    if (activityType) filters.activityType = activityType
    if (category) filters.category = category
    if (severity) filters.severity = severity
    if (isSecurityEvent !== undefined) filters.isSecurityEvent = isSecurityEvent === 'true'
    if (userId) filters.performedBy = userId
    if (startDate || endDate) {
      filters.startDate = startDate
      filters.endDate = endDate
    }

    // Use ActivityLogService for consistent data format
    const skip = (parseInt(page) - 1) * parseInt(limit)
    const activities = await ActivityLogService.getRecentActivities(parseInt(limit), skip)
    const totalCount = await ActivityLogService.getActivitiesCount()
    const stats = await ActivityLogService.getActivityStats(7)

    res.status(200).json({
      success: true,
      data: {
        activities,
        stats,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalCount,
        },
      },
    })
  } catch (error) {
    logger.error('Get activity logs error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error fetching activity logs',
    })
  }
}

/**
 * @desc    Get vote backups with filtering and pagination
 * @route   GET /api/admin/vote-backups
 * @access  Private/Admin
 */
export const getVoteBackups = async (req, res) => {
  try {
    const { page = 1, limit = 50, backupType, userId, startDate, endDate } = req.query

    const query = {}
    if (backupType) query.backupType = backupType
    if (userId) query['originalVoteData.voterId'] = userId
    if (startDate || endDate) {
      query.backupTimestamp = {}
      if (startDate) query.backupTimestamp.$gte = new Date(startDate)
      if (endDate) query.backupTimestamp.$lte = new Date(endDate)
    }

    const backups = await VoteBackup.find(query)
      .populate('performedBy', 'username municipality role')
      .populate('originalVoteData.voterId', 'username municipality')
      .populate('originalVoteData.candidateId', 'municipalityName district')
      .sort({ backupTimestamp: -1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit))

    const totalBackups = await VoteBackup.countDocuments(query)
    const stats = await VoteBackup.getBackupStats()

    res.status(200).json({
      success: true,
      data: {
        backups,
        stats,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalBackups,
          pages: Math.ceil(totalBackups / parseInt(limit)),
        },
      },
    })
  } catch (error) {
    logger.error('Get vote backups error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error fetching vote backups',
    })
  }
}

/**
 * @desc    Restore vote from backup
 * @route   POST /api/admin/vote-backups/:id/restore
 * @access  Private/Admin
 */
export const restoreVoteBackup = async (req, res) => {
  try {
    const { restorationNotes = '' } = req.body
    const backup = await VoteBackup.findById(req.params.id)

    if (!backup) {
      return res.status(404).json({
        success: false,
        error: 'Vote backup not found',
      })
    }

    if (backup.isRestored) {
      return res.status(400).json({
        success: false,
        error: 'Vote backup has already been restored',
      })
    }

    await backup.restore(req.user.id, restorationNotes)

    // Log activity
    await ActivityLog.logActivity({
      activityType: 'admin_action',
      performedBy: req.user.id,
      description: `Admin ${req.user.username} restored vote backup`,
      metadata: {
        backupId: backup._id,
        originalVoteId: backup.originalVoteId,
        backupType: backup.backupType,
        restorationNotes,
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      requestMethod: req.method,
      requestUrl: req.originalUrl,
      responseStatus: 200,
      severity: 'high',
      category: 'administration',
    })

    logger.info(`Admin ${req.user.username} restored vote backup ${backup._id}`)

    res.status(200).json({
      success: true,
      message: 'Vote backup restored successfully',
      data: {
        backup,
      },
    })
  } catch (error) {
    logger.error('Restore vote backup error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error restoring vote backup',
    })
  }
}

/**
 * @desc    Get account statistics and limits
 * @route   GET /api/admin/account-stats
 * @access  Private/Admin
 */
export const getAccountStats = async (_req, res) => {
  try {
    const stats = await User.getAccountStats()

    res.status(200).json({
      success: true,
      data: stats,
    })
  } catch (error) {
    logger.error('Get account stats error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error fetching account statistics',
    })
  }
}

/**
 * @desc    Reset all votes in the system
 * @route   POST /api/admin/reset-votes
 * @access  Private/Admin
 */
export const resetAllVotes = async (req, res) => {
  const session = await mongoose.startSession()

  try {
    await session.withTransaction(async () => {
      const { reason } = req.body
      const adminId = req.user.id
      const adminUsername = req.user.username
      const ipAddress = req.ip || req.connection.remoteAddress
      const userAgent = req.get('User-Agent') || 'Unknown'

      // Get pre-reset statistics
      const preResetStats = {
        totalVotes: await Vote.countDocuments(),
        totalVoters: await User.countDocuments({ role: 'voter', isActive: true }),
        votedUsersCount: await User.countDocuments({ hasVoted: true, isActive: true }),
      }

      // Create vote reset record
      const voteReset = new VoteReset({
        adminId,
        adminUsername,
        resetType: 'all-votes',
        affectedUsersCount: preResetStats.votedUsersCount,
        affectedVotesCount: preResetStats.totalVotes,
        reason,
        ipAddress,
        userAgent,
        preResetStats,
        status: 'pending',
      })

      await voteReset.save({ session })

      try {
        // Reset all votes
        await Vote.deleteMany({}, { session })

        // Reset user voting status
        await User.updateMany(
          { hasVoted: true },
          {
            hasVoted: false,
            lastVotedAt: null,
          },
          { session }
        )

        // Reset all candidate vote counts
        await Candidate.updateMany(
          {},
          { $set: { totalVotes: 0 } },
          { session }
        )

        // Get post-reset statistics
        const postResetStats = {
          totalVotes: await Vote.countDocuments(),
          totalVoters: await User.countDocuments({ role: 'voter', isActive: true }),
          votedUsersCount: await User.countDocuments({ hasVoted: true, isActive: true }),
        }

        // Mark reset as completed
        await voteReset.markCompleted(postResetStats)

        logger.info(
          `Admin ${adminUsername} reset all votes. Affected: ${preResetStats.totalVotes} votes, ${preResetStats.votedUsersCount} users`
        )

        res.status(200).json({
          success: true,
          message: 'All votes have been reset successfully',
          data: {
            resetId: voteReset._id,
            affectedVotes: preResetStats.totalVotes,
            affectedUsers: preResetStats.votedUsersCount,
            resetAt: new Date(),
          },
        })
      } catch (resetError) {
        // Mark reset as failed
        await voteReset.markFailed(resetError.message)
        throw resetError
      }
    })
  } catch (error) {
    logger.error('Reset all votes error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error resetting votes',
    })
  } finally {
    await session.endSession()
  }
}

/**
 * @desc    Get vote reset audit logs
 * @route   GET /api/admin/vote-resets
 * @access  Private/Admin
 */
export const getVoteResets = async (req, res) => {
  try {
    const { page = 1, limit = 20, resetType, status, adminId, startDate, endDate } = req.query

    // Build filter conditions
    const filterConditions = {}

    if (resetType) {
      filterConditions.resetType = resetType
    }

    if (status) {
      filterConditions.status = status
    }

    if (adminId) {
      filterConditions.adminId = adminId
    }

    if (startDate || endDate) {
      filterConditions.createdAt = {}
      if (startDate) {
        filterConditions.createdAt.$gte = new Date(startDate)
      }
      if (endDate) {
        filterConditions.createdAt.$lte = new Date(endDate)
      }
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit)

    // Get vote resets with pagination
    const voteResets = await VoteReset.find(filterConditions)
      .populate('adminId', 'username municipality')
      .populate('targetUserId', 'username municipality')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))

    // Get total count for pagination
    const totalCount = await VoteReset.countDocuments(filterConditions)

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / parseInt(limit))
    const hasNextPage = parseInt(page) < totalPages
    const hasPrevPage = parseInt(page) > 1

    res.status(200).json({
      success: true,
      data: {
        voteResets,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCount,
          hasNextPage,
          hasPrevPage,
          limit: parseInt(limit),
        },
      },
    })
  } catch (error) {
    logger.error('Get vote resets error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error fetching vote reset logs',
    })
  }
}

/**
 * @desc    Get vote reset statistics
 * @route   GET /api/admin/vote-reset-stats
 * @access  Private/Admin
 */
export const getVoteResetStats = async (req, res) => {
  try {
    const { startDate, endDate } = req.query

    const stats = await VoteReset.getResetStatistics(startDate, endDate)

    res.status(200).json({
      success: true,
      data: stats[0] || {
        totalResets: 0,
        totalVotesReset: 0,
        totalUsersAffected: 0,
        resetsByType: [],
        resetsByStatus: [],
        adminActivity: [],
      },
    })
  } catch (error) {
    logger.error('Get vote reset stats error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error fetching vote reset statistics',
    })
  }
}

// Removed toggleUserPresence function - using only isActive field from User Management

// Removed bulkUpdatePresence function - using only isActive field from User Management

/**
 * @desc    Get system settings
 * @route   GET /api/admin/settings
 * @access  Private/Admin
 */
export const getSystemSettings = async (req, res) => {
  try {
    const { category } = req.query

    let settings
    if (category) {
      settings = await SystemSettings.getSettingsByCategory(category)
    } else {
      settings = await SystemSettings.getEditableSettings()
    }

    res.status(200).json({
      success: true,
      data: settings,
    })
  } catch (error) {
    logger.error('Get system settings error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error fetching system settings',
    })
  }
}

/**
 * @desc    Update system setting
 * @route   PATCH /api/admin/settings/:key
 * @access  Private/Admin
 */
export const updateSystemSetting = async (req, res) => {
  try {
    const { key } = req.params
    const { value } = req.body
    const adminId = req.user.id

    const setting = await SystemSettings.setSetting(key, value, adminId)

    logger.info(`Admin ${req.user.username} updated setting ${key} to ${value}`)

    res.status(200).json({
      success: true,
      message: `Setting ${key} updated successfully`,
      data: setting,
    })
  } catch (error) {
    logger.error('Update system setting error:', error)

    if (error.message.includes('not found')) {
      return res.status(404).json({
        success: false,
        error: error.message,
      })
    }

    res.status(500).json({
      success: false,
      error: 'Server error updating system setting',
    })
  }
}

/**
 * @desc    Toggle public results visibility
 * @route   PATCH /api/admin/toggle-public-results
 * @access  Private/Admin
 */
export const togglePublicResults = async (req, res) => {
  try {
    const { enabled } = req.body
    const adminId = req.user.id

    const setting = await SystemSettings.setSetting('public_results_enabled', enabled, adminId)

    logger.info(`Admin ${req.user.username} ${enabled ? 'enabled' : 'disabled'} public results`)

    res.status(200).json({
      success: true,
      message: `Public results ${enabled ? 'enabled' : 'disabled'} successfully`,
      data: {
        publicResultsEnabled: setting.value,
        lastModifiedBy: req.user.username,
        lastModifiedAt: setting.lastModifiedAt,
      },
    })
  } catch (error) {
    logger.error('Toggle public results error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error toggling public results',
    })
  }
}

/**
 * @desc    Get all election archives
 * @route   GET /api/admin/election/archives
 * @access  Private/Admin
 */
export const getElectionArchives = async (req, res) => {
  try {
    const archives = await ElectionArchive.find({})
      .populate('archivedBy', 'username municipality')
      .sort({ year: -1 })

    res.status(200).json({
      success: true,
      data: archives
    })
  } catch (error) {
    logger.error('Get election archives error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error retrieving election archives'
    })
  }
}

/**
 * @desc    Export election results as PDF
 * @route   GET /api/admin/results/export/pdf
 * @access  Private/Admin
 */
export const exportResultsPDF = async (req, res) => {
  try {
    // Get current election results
    const candidates = await Candidate.find({ isActive: true })
      .select('municipalityName district totalVotes')
      .sort({ totalVotes: -1, municipalityName: 1 })

    const [totalVoters, totalVotesCast] = await Promise.all([
      User.countDocuments({ role: 'voter' }),
      User.countDocuments({ role: 'voter', hasVoted: true })
    ])

    const participationRate = totalVoters > 0 ? (totalVotesCast / totalVoters) * 100 : 0

    // Prepare results data
    const results = candidates.map((candidate, index) => ({
      rank: index + 1,
      municipalityName: candidate.municipalityName,
      district: candidate.district,
      voteCount: candidate.totalVotes
    }))

    const statistics = {
      totalVoters,
      totalVotesCast,
      participationRate: Math.round(participationRate * 100) / 100,
      totalCandidates: candidates.length
    }

    // Generate PDF
    const pdfBuffer = await generateElectionResultsPDF(results, statistics)

    // Set response headers for PDF download
    const filename = `DFPTA_Election_Results_${new Date().toISOString().split('T')[0]}.pdf`

    res.setHeader('Content-Type', 'application/pdf')
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
    res.setHeader('Content-Length', pdfBuffer.length)

    // Send PDF buffer
    res.send(pdfBuffer)

    logger.info(`Admin ${req.user.username} exported election results as PDF`, {
      totalCandidates: candidates.length,
      totalVotesCast,
      participationRate
    })

  } catch (error) {
    logger.error('Export results PDF error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error generating PDF export'
    })
  }
}

/**
 * @desc    Export specific archive as PDF
 * @route   GET /api/admin/archives/:id/export/pdf
 * @access  Private/Admin
 */
export const exportArchivePDF = async (req, res) => {
  try {
    const { id } = req.params

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid archive ID'
      })
    }

    // Get archive data
    const archive = await ElectionArchive.findById(id)
      .populate('archivedBy', 'username municipality')

    if (!archive) {
      return res.status(404).json({
        success: false,
        error: 'Archive not found'
      })
    }

    // Generate PDF with archive data
    const pdfBuffer = await generateElectionResultsPDF(archive.results, archive.statistics)

    // Set response headers for PDF download
    const filename = `DFPTA_Election_Archive_${archive.year}.pdf`

    res.setHeader('Content-Type', 'application/pdf')
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
    res.setHeader('Content-Length', pdfBuffer.length)

    // Send PDF buffer
    res.send(pdfBuffer)

    logger.info(`Admin ${req.user.username} exported archive ${archive.year} as PDF`, {
      archiveId: id,
      year: archive.year
    })

  } catch (error) {
    logger.error('Export archive PDF error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error generating archive PDF export'
    })
  }
}

/**
 * @desc    Export election results as DOCX
 * @route   GET /api/admin/results/export/docx
 * @access  Private/Admin
 */
export const exportResultsDOCX = async (req, res) => {
  try {
    // Get current election results
    const candidates = await Candidate.find({ isActive: true })
      .select('municipalityName district totalVotes')
      .sort({ totalVotes: -1, municipalityName: 1 })

    const [totalVoters, totalVotesCast] = await Promise.all([
      User.countDocuments({ role: 'voter' }),
      User.countDocuments({ role: 'voter', hasVoted: true })
    ])

    const participationRate = totalVoters > 0 ? (totalVotesCast / totalVoters) * 100 : 0

    // Prepare results data
    const results = candidates.map((candidate, index) => ({
      rank: index + 1,
      municipalityName: candidate.municipalityName,
      district: candidate.district,
      voteCount: candidate.totalVotes
    }))

    const statistics = {
      totalVoters,
      totalVotesCast,
      participationRate: Math.round(participationRate * 100) / 100,
      totalCandidates: candidates.length
    }

    // Generate DOCX
    const docxBuffer = await generateElectionResultsDOCX(results, statistics)

    // Set response headers for DOCX download
    const filename = `DFPTA_Election_Results_${new Date().toISOString().split('T')[0]}.docx`

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
    res.setHeader('Content-Length', docxBuffer.length)

    // Send DOCX buffer
    res.send(docxBuffer)

    logger.info(`Admin ${req.user.username} exported election results as DOCX`, {
      totalCandidates: candidates.length,
      totalVotesCast,
      participationRate
    })

  } catch (error) {
    logger.error('Export results DOCX error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error generating DOCX export'
    })
  }
}

/**
 * @desc    Export specific archive as DOCX
 * @route   GET /api/admin/archives/:id/export/docx
 * @access  Private/Admin
 */
export const exportArchiveDOCX = async (req, res) => {
  try {
    const { id } = req.params

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid archive ID'
      })
    }

    // Get archive data
    const archive = await ElectionArchive.findById(id)
      .populate('archivedBy', 'username municipality')

    if (!archive) {
      return res.status(404).json({
        success: false,
        error: 'Archive not found'
      })
    }

    // Generate DOCX with archive data
    const docxBuffer = await generateElectionResultsDOCX(archive.results, archive.statistics)

    // Set response headers for DOCX download
    const filename = `DFPTA_Election_Archive_${archive.filename || archive.year}.docx`

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
    res.setHeader('Content-Length', docxBuffer.length)

    // Send DOCX buffer
    res.send(docxBuffer)

    logger.info(`Admin ${req.user.username} exported archive ${archive.filename || archive.year} as DOCX`, {
      archiveId: id,
      filename: archive.filename || archive.year
    })

  } catch (error) {
    logger.error('Export archive DOCX error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error generating archive DOCX export'
    })
  }
}

/**
 * @desc    Get specific election archive by ID
 * @route   GET /api/admin/election/archives/:id
 * @access  Private/Admin
 */
export const getElectionArchiveById = async (req, res) => {
  try {
    const { id } = req.params

    const archive = await ElectionArchive.findById(id)
      .populate('archivedBy', 'username municipality')

    if (!archive) {
      return res.status(404).json({
        success: false,
        error: 'Election archive not found'
      })
    }

    res.status(200).json({
      success: true,
      data: archive
    })
  } catch (error) {
    logger.error('Get election archive by ID error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error retrieving election archive'
    })
  }
}
