// User types
export interface User {
  id: string
  username: string
  municipality: string
  district: District
  role: UserRole
  email?: string
  hasVoted: boolean
  isActive: boolean
  lastLogin?: string
  createdAt: string
  updatedAt: string
  // Voting rounds removed - simplified voting system
}

export type UserRole = 'voter' | 'admin'

// District type moved to dynamic section below

// Authentication types
export interface LoginCredentials {
  username: string
  password: string
}

export interface AuthResponse {
  success: boolean
  user: User
  token: string
  refreshToken: string
}

// Candidate types (Municipality-only system)
export interface Candidate {
  id: string
  municipalityName: string
  district: District
  totalVotes: number
  currentRank?: number
  finalPosition?: number
  isWinner: boolean
  isEliminated: boolean
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// Vote types
export interface Vote {
  id: string
  voterId: string
  candidateId: string
  voteType: VoteType
  createdAt: string
}

export type VoteType = 'regular'

export interface VoteRequest {
  candidateIds: string[]
  voteType?: VoteType
}

// Results types
export interface VotingResults {
  totalVoters: number
  totalVotesCast: number
  turnoutPercentage: number
  candidates: Candidate[]
  // Executive committee and officer positions removed - simplified voting system
  lastUpdated: string
}

// Admin types
export interface AdminStats {
  totalUsers: number
  totalCandidates: number
  totalVotes: number
  votingStatus: VotingStatus
  systemHealth: SystemHealth
}

export type VotingStatus = 'not-started' | 'active' | 'paused' | 'completed'

export interface SystemHealth {
  database: 'healthy' | 'warning' | 'error'
  api: 'healthy' | 'warning' | 'error'
  lastCheck: string
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  success: boolean
  data: {
    users: T[]
    pagination: {
      currentPage: number
      totalPages: number
      totalUsers: number
      hasNextPage: boolean
      hasPrevPage: boolean
      limit: number
    }
  }
  message?: string
  error?: string
}

// Form types
export interface FormError {
  field: string
  message: string
}

// Utility types
export interface SelectOption {
  value: string
  label: string
  disabled?: boolean
}

export interface TableColumn<T = any> {
  key: keyof T
  label: string
  sortable?: boolean
  render?: (value: any, row: T) => React.ReactNode
}

// Component prop types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface LoadingState {
  isLoading: boolean
  error?: string | null
}

// Dynamic types - data will be loaded from API
// Note: MUNICIPALITIES constant is deprecated - use systemService instead
export type Municipality = string

// Dynamic types - data will be loaded from API
// Note: DISTRICT_MAPPING, DISTRICTS constants and helper functions are deprecated
// Use systemService and useSystem hooks instead for dynamic data loading
export type District = string
