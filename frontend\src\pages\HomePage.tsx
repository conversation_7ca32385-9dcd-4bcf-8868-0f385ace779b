import { Bar<PERSON>hart<PERSON>, Shield, Users, Vote } from 'lucide-react'
import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from '../components/atoms/Button'
import { Card, CardContent } from '../components/ui/card'
import { useAuth } from '../hooks/useAuth'

const HomePage = () => {
  const { user, isLoading } = useAuth()
  const navigate = useNavigate()

  // Redirect authenticated users to their role-specific dashboards
  useEffect(() => {
    if (!isLoading && user) {
      switch (user.role) {
        case 'admin':
          navigate('/admin', { replace: true })
          break
        case 'voter':
          navigate('/vote', { replace: true })
          break
        default:
          // Unknown role, stay on homepage
          break
      }
    }
  }, [user, isLoading, navigate])

  if (isLoading) {
    return (
      <div className='flex min-h-screen items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50'>
        <div className='flex flex-col items-center space-y-4'>
          <div className='h-12 w-12 animate-spin rounded-full border-4 border-primary-200 border-t-primary-600'></div>
          <p className='text-muted-foreground text-sm'>Loading DFPTA E-Voting System...</p>
        </div>
      </div>
    )
  }

  return (
    <div className='flex min-h-screen items-center justify-center bg-gradient-to-br from-primary-50 via-white to-secondary-50 px-4 py-8'>
      {/* Main Content - Single Viewport */}
      <main className='flex flex-1 items-center justify-center px-4 py-8'>
        <div className='mx-auto w-full max-w-4xl'>
          <div className='mb-12 text-center'>
            <h2 className='mb-6 font-heading text-4xl font-bold text-gray-900 sm:text-5xl'>
              Welcome to DFPTA E-Voting
            </h2>
            <p className='mx-auto mb-8 max-w-2xl text-xl text-gray-600'>
              Secure electronic voting platform for the Department of Education Federation of
              Parents-Teachers Association in Camarines Sur.
            </p>

            <div className='mb-12 flex flex-col justify-center gap-4 sm:flex-row'>
              <Button
                size='lg'
                leftIcon={<Vote className='h-5 w-5' />}
                onClick={() => navigate('/login')}
                className='w-full sm:w-auto'
              >
                Access Voting Portal
              </Button>
              <Button
                variant='outline'
                size='lg'
                leftIcon={<BarChart3 className='h-5 w-5' />}
                onClick={() => navigate('/results')}
                className='w-full sm:w-auto'
              >
                View Election Results
              </Button>
            </div>
          </div>

          {/* Quick Stats */}
          <div className='grid grid-cols-1 gap-6 md:grid-cols-3'>
            <Card className='border-0 bg-white/60 text-center shadow-lg backdrop-blur-sm'>
              <CardContent className='p-6'>
                <div className='mb-4 flex justify-center'>
                  <div className='flex h-12 w-12 items-center justify-center rounded-full bg-primary-100'>
                    <Users className='h-6 w-6 text-primary-600' />
                  </div>
                </div>
                <div className='text-2xl font-bold text-gray-900'>35</div>
                <div className='text-sm text-gray-600'>Municipalities</div>
              </CardContent>
            </Card>

            <Card className='border-0 bg-white/60 text-center shadow-lg backdrop-blur-sm'>
              <CardContent className='p-6'>
                <div className='mb-4 flex justify-center'>
                  <div className='flex h-12 w-12 items-center justify-center rounded-full bg-primary-100'>
                    <Shield className='h-6 w-6 text-primary-600' />
                  </div>
                </div>
                <div className='text-2xl font-bold text-gray-900'>100%</div>
                <div className='text-sm text-gray-600'>Secure</div>
              </CardContent>
            </Card>

            <Card className='border-0 bg-white/60 text-center shadow-lg backdrop-blur-sm'>
              <CardContent className='p-6'>
                <div className='mb-4 flex justify-center'>
                  <div className='flex h-12 w-12 items-center justify-center rounded-full bg-primary-100'>
                    <BarChart3 className='h-6 w-6 text-primary-600' />
                  </div>
                </div>
                <div className='text-2xl font-bold text-gray-900'>Live</div>
                <div className='text-sm text-gray-600'>Results</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}

export default HomePage
