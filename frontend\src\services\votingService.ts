import { apiClient } from './apiClient'

export interface Candidate {
  _id: string
  municipalityName: string
  district: string
  totalVotes: number
  currentRank?: number
  finalPosition?: number
  isWinner: boolean
  isEliminated: boolean
  isActive: boolean
  isDisabled?: boolean
  disabledReason?: string
  createdAt: string
  updatedAt: string
}

export interface VotingStatus {
  canVote: boolean
  hasVoted: boolean
  votingStatus:
    | 'no-active-session'
    | 'not-started'
    | 'active'
    | 'completed'
    | 'cancelled'
    | 'paused'
    | 'closed-by-admin'
    | 'user-disabled'
  sessionId?: string
  maxCandidatesPerVote: number
  user: {
    municipality: string
    role: string
    lastVotedAt?: string
  }
  message?: string
}

export interface VoteSubmissionResponse {
  message: string
  votesCount: number
  batchId: string
  submittedAt: string
}

export interface VotingHistory {
  sessionId: string
  sessionName: string
  voteType: 'regular'
  batchId: string
  timestamp: string
  candidates: Array<{
    id: string
    name: string
    municipality: string
  }>
}

export interface VotingResults {
  results: Array<{
    rank: number
    candidateId?: string
    candidateName: string
    municipality: string
    voteCount: number
    percentage: string
  }>
  statistics: {
    totalVotes: number
    uniqueVoters: number
    participatingMunicipalities: number
    participationRate: number
  }
  session: {
    id: string
    name: string
    status: string
    maxCandidatesPerVote: number
  }
  participationByMunicipality: Array<{
    municipality: string
    voteCount: number
    candidateCount: number
    firstVote: string
    lastVote: string
  }>
}

export const votingService = {
  // Get all active candidates
  async getCandidates(): Promise<{ candidates: Candidate[]; count: number }> {
    const response = await apiClient.get('/candidates')
    return response.data.data
  },

  // Get voting status for current user
  async getVotingStatus(): Promise<VotingStatus> {
    const response = await apiClient.get('/voting/status')
    return response.data.data
  },

  // Submit votes for selected candidates
  async submitVotes(candidateIds: string[]): Promise<VoteSubmissionResponse> {
    const response = await apiClient.post('/voting/vote', { candidateIds })
    return response.data.data
  },

  // Get public voting results
  async getPublicResults(): Promise<any> {
    const response = await apiClient.get('/results')
    return response.data
  },

  // Check if public results are enabled
  async getPublicResultsStatus(): Promise<{ enabled: boolean }> {
    const response = await apiClient.get('/results/status')
    return response.data.data
  },

  // Get user's voting history
  async getVotingHistory(): Promise<{ votingHistory: VotingHistory[]; totalVotes: number }> {
    const response = await apiClient.get('/voting/history')
    return response.data.data
  },

  // Get current voting results (public)
  async getResults(detailed: boolean = false): Promise<VotingResults> {
    const response = await apiClient.get(`/voting/results?detailed=${detailed}`)
    return response.data.data
  },



  // Utility functions
  async checkVotingEligibility(): Promise<boolean> {
    try {
      const status = await this.getVotingStatus()
      return status.canVote && !status.hasVoted
    } catch (error) {
      console.error('Error checking voting eligibility:', error)
      return false
    }
  },

  async getRemainingSelections(currentSelections: string[]): Promise<number> {
    try {
      const status = await this.getVotingStatus()
      return Math.max(0, status.maxCandidatesPerVote - currentSelections.length)
    } catch (error) {
      console.error('Error calculating remaining selections:', error)
      return 0
    }
  },

  // Validation helpers
  validateCandidateSelection(
    candidateIds: string[],
    maxSelections: number
  ): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if (!candidateIds || candidateIds.length === 0) {
      errors.push('Please select at least one candidate')
    }

    if (candidateIds.length > maxSelections) {
      errors.push(`You can select a maximum of ${maxSelections} candidates`)
    }

    // Check for duplicates
    const uniqueIds = [...new Set(candidateIds)]
    if (uniqueIds.length !== candidateIds.length) {
      errors.push('Duplicate candidate selections are not allowed')
    }

    // Validate ObjectId format
    const objectIdRegex = /^[0-9a-fA-F]{24}$/
    const invalidIds = candidateIds.filter(id => !objectIdRegex.test(id))
    if (invalidIds.length > 0) {
      errors.push('Invalid candidate ID format detected')
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  },

  // Format helpers
  formatVotingStatus(status: VotingStatus['votingStatus']): string {
    const statusMap = {
      'no-active-session': 'No Active Session',
      'not-started': 'Not Started',
      active: 'Active',
      completed: 'Completed',
      cancelled: 'Cancelled',
      paused: 'Paused',
      'closed-by-admin': 'Closed by Admin',
      'user-disabled': 'Account Disabled',
    }
    return statusMap[status] || 'Unknown Status'
  },

  formatTimeRemaining(deadline: string): string {
    const now = new Date()
    const deadlineDate = new Date(deadline)
    const diff = deadlineDate.getTime() - now.getTime()

    if (diff <= 0) return 'Expired'

    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (hours > 24) {
      const days = Math.floor(hours / 24)
      return `${days} day${days !== 1 ? 's' : ''} remaining`
    }

    if (hours > 0) {
      return `${hours}h ${minutes}m remaining`
    }

    return `${minutes} minute${minutes !== 1 ? 's' : ''} remaining`
  },

  // Local storage helpers for draft votes (user-specific)
  saveDraftVotes(candidateIds: string[], userId?: string): void {
    try {
      const key = this.getDraftVotesKey(userId)
      localStorage.setItem(key, JSON.stringify(candidateIds))
    } catch (error) {
      console.warn('Failed to save draft votes:', error)
    }
  },

  loadDraftVotes(userId?: string): string[] {
    try {
      const key = this.getDraftVotesKey(userId)
      const draft = localStorage.getItem(key)
      return draft ? JSON.parse(draft) : []
    } catch (error) {
      console.warn('Failed to load draft votes:', error)
      return []
    }
  },

  clearDraftVotes(userId?: string): void {
    try {
      const key = this.getDraftVotesKey(userId)
      localStorage.removeItem(key)
    } catch (error) {
      console.warn('Failed to clear draft votes:', error)
    }
  },

  // Clear all draft votes for all users (used on logout)
  clearAllDraftVotes(): void {
    try {
      const keys = Object.keys(localStorage)
      keys.forEach(key => {
        if (key.startsWith('dfpta_draft_votes_')) {
          localStorage.removeItem(key)
        }
      })
      // Also clear the old generic key for backward compatibility
      localStorage.removeItem('dfpta_draft_votes')
    } catch (error) {
      console.warn('Failed to clear all draft votes:', error)
    }
  },

  // Get user-specific key for draft votes
  getDraftVotesKey(userId?: string): string {
    if (userId) {
      return `dfpta_draft_votes_${userId}`
    }
    // Fallback to generic key if no user ID provided
    return 'dfpta_draft_votes'
  },
}
