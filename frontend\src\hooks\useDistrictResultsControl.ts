import { apiClient } from '@/services/apiClient'
import { logger } from '@/utils/logger'
import { useState } from 'react'
import { useSystemSettings } from './useSystemSettings'

export function useDistrictResultsControl() {
  const { getSetting, refreshSettings } = useSystemSettings()
  const [isLoading, setIsLoading] = useState(false)

  const showDistrictResults = getSetting('show_district_results')?.value || true

  const toggleDistrictResults = async (show: boolean): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true)

    try {
      const response = await apiClient.patch('/admin/settings/show_district_results', {
        value: show
      })

      if (response.data.success) {
        // Refresh the cached settings to get the updated value
        await refreshSettings()

        logger.info('District results visibility changed', {
          metadata: {
            newState: show ? 'visible' : 'hidden',
            timestamp: new Date().toISOString(),
            action: 'admin_toggle_district_results'
          }
        })

        return { success: true }
      } else {
        throw new Error(response.data.error || 'Failed to update district results setting')
      }
    } catch (error) {
      logger.error('Failed to update district results visibility', error)
      return { success: false, error: 'Failed to update district results setting' }
    } finally {
      setIsLoading(false)
    }
  }

  return {
    showDistrictResults,
    isLoading,
    toggleDistrictResults,
  }
}
