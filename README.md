# DFPTA E-Voting System

A modern, secure electronic voting system for the Department of Education Camarines Sur - DFPTA.

## 🚀 Quick Start

### Option 1: Run Both Servers Together

```bash
npm run dev
```

This starts both backend and frontend servers automatically.

### Option 2: Run Servers Separately

#### Backend Only

```bash
npm run dev:backend
# or
cd backend && npm run dev
```

#### Frontend Only

```bash
npm run dev:frontend
# or
cd frontend && npm run dev
```

#### Both Servers (Alternative)

```bash
npm run dev:both
```

## 📁 Project Structure

```
dfpta/
├── backend/          # Express.js API server
├── frontend/         # React + TypeScript + Vite
├── scripts/          # Development and setup scripts
└── docs/            # Documentation
```

## 🔧 Development

### Backend (Port 5000)

- **Framework**: Express.js
- **Database**: MongoDB
- **Authentication**: JWT
- **API Documentation**: Swagger UI at `/api/docs`

### Frontend (Port 3000)

- **Framework**: React 18 + TypeScript
- **Build Tool**: Vite
- **UI Library**: Tailwind CSS + shadcn/ui
- **State Management**: React Query

## 🛠️ Available Scripts

### Root Level

- `npm run dev` - Start both servers (recommended)
- `npm run dev:backend` - Start backend only
- `npm run dev:frontend` - Start frontend only
- `npm run dev:both` - Start both servers with concurrently

### Backend

- `npm run dev` - Start development server with nodemon
- `npm start` - Start production server
- `npm test` - Run tests

### Frontend

- `npm run dev` - Start Vite development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

## 🔐 Default Credentials

### Admin

- **Username**: `admin`
- **Password**: `socmob123`

### Municipality Users

- **Username**: `{municipality}` (e.g., `pili`, `nabua`)
- **Password**: Generated secure password (see setup scripts)

## 🌐 Access Points

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **API Docs**: http://localhost:5000/api/docs
- **Health Check**: http://localhost:5000/api/health

## 📋 Features

- ✅ **Secure Authentication** - JWT-based auth with role-based access
- ✅ **Election Control** - Admin can open/close voting
- ✅ **Voting System** - Municipality-based voting with candidate selection
- ✅ **Real-time Results** - Live vote counting and statistics
- ✅ **Admin Dashboard** - Comprehensive admin controls
- ✅ **Rate Limiting** - 200 login attempts per minute
- ✅ **Password Management** - Admin can reset user passwords
- ✅ **Health Monitoring** - Backend status checking

## 🚨 Troubleshooting

### Port Already in Use

```bash
# Kill processes on ports 3000 and 5000
netstat -ano | findstr :3000
netstat -ano | findstr :5000
taskkill /PID <PID> /F
```

### Database Issues

```bash
# Reset database
cd backend && npm run reset-database
```

### Installation Issues

```bash
# Clean install
npm run clean
npm run install:all
```

## 📄 License

MIT License - see LICENSE file for details.
