import { BarChart3, Calendar, History, Info, Lock, Settings, User, UserX, Vote } from 'lucide-react'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'

// New components
import { StatCard } from '@/components/atoms/StatCard'
import { DashboardHeader } from '@/components/organisms/DashboardHeader'
import { VotingStatusCard } from '@/components/organisms/VotingStatusCard'

// UI Components
import { Button } from '@/components/atoms/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

// Hooks
import { useAuth } from '@/hooks/useAuth'
import { useElectionAccess } from '@/hooks/useSystemSettings'
import { useVotingStatus } from '@/hooks/useVoting'

// Utils
import { ChangePasswordForm } from '@/components/forms/ChangePasswordForm'
import { cn } from '@/lib/utils'

export interface VoterDashboardProps {
  className?: string
  onStartVoting?: () => void
}

export function VoterDashboard({ className, onStartVoting }: VoterDashboardProps) {
  const { user } = useAuth()
  const { data: votingStatus, isLoading } = useVotingStatus()
  const { canAccessVoting, electionStatus } = useElectionAccess()
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState('overview')

  const handleStartVoting = () => {
    if (!canAccessVoting) {
      // Show election closed message
      return
    }

    if (onStartVoting) {
      onStartVoting()
    } else {
      // Navigate to voting interface
      navigate('/vote')
    }
  }

  const handleViewResults = () => {
    navigate('/results')
  }

  const handleProfileClick = () => {
    // Profile functionality not implemented in current scope
    // Could be added in future iterations if needed
  }

  const handleSettingsClick = () => {
    // Settings functionality not implemented in current scope
    // Could be added in future iterations if needed
  }

  // Mock data for demonstration - in real app, this would come from API
  const userStats = {
    totalVotingRounds: 3,
    completedRounds: user?.hasVoted ? 1 : 0,
    lastActivity: user?.hasVoted ? 'Voted in Round 1' : 'No voting activity',
    accountCreated: user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Unknown',
  }

  const recentActivity = [
    {
      id: 1,
      action: 'Account created',
      timestamp: user?.createdAt || new Date().toISOString(),
      type: 'info',
    },
    ...(user?.hasVoted
      ? [
          {
            id: 2,
            action: 'Vote submitted successfully',
            timestamp: new Date().toISOString(),
            type: 'success',
          },
        ]
      : []),
  ]

  // Map voting service status to VotingStatusCard status
  const mapVotingStatus = (status: any) => {
    if (!status) return null

    const statusMapping: Record<string, 'active' | 'inactive' | 'completed' | 'pending'> = {
      active: 'active',
      completed: 'completed',
      'no-active-session': 'inactive',
      'not-started': 'pending',
      paused: 'inactive',
      cancelled: 'inactive',
      'closed-by-admin': 'inactive',
      tiebreaker: 'active',
    }

    return {
      votingStatus: statusMapping[status.votingStatus] || 'inactive',
      hasVoted: status.hasVoted,
      votingStartTime: status.votingStartTime,
      votingEndTime: status.votingEndTime,
      maxCandidatesPerVote: status.maxCandidatesPerVote,
      totalCandidates: status.totalCandidates,
      currentRound: status.currentRound,
      lastVotedAt: status.lastVotedAt,
    }
  }

  return (
    <div
      className={cn('min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50', className)}
    >
      <div className='mx-auto max-w-6xl p-6'>
        {/* Enhanced Header */}
        <DashboardHeader
          title='Voter Dashboard'
          subtitle='DFPTA E-Voting System'
          showUserInfo={true}
          onProfileClick={handleProfileClick}
          onSettingsClick={handleSettingsClick}
        />

        {/* Election Status Warning */}
        {!canAccessVoting && (
          <div className='mb-6'>
            <Card className='border-red-200 bg-red-50'>
              <CardContent className='py-4'>
                <div className='flex items-center space-x-3'>
                  <Lock className='h-5 w-5 text-red-600' />
                  <div>
                    <p className='font-medium text-red-800'>Election Currently Closed</p>
                    <p className='text-sm text-red-700'>
                      Voting is currently disabled by the system administrator. You can still view
                      results and manage your account.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* User Status Warning */}
        {!user?.isActive && (
          <div className='mb-6'>
            <Card className='border-amber-200 bg-amber-50'>
              <CardContent className='py-4'>
                <div className='flex items-center space-x-3'>
                  <UserX className='h-5 w-5 text-amber-600' />
                  <div>
                    <p className='font-medium text-amber-800'>Account Inactive</p>
                    <p className='text-sm text-amber-700'>
                      Your account has been deactivated. You cannot participate in voting until your account is reactivated.
                    </p>
                    <p className='text-sm text-amber-700 mt-1'>
                      <strong>What you can do:</strong> View results, change your password, and access account information.
                    </p>
                    <p className='text-sm text-amber-700'>
                      <strong>Need help?</strong> Contact your system administrator or election officials.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Enhanced Voting Status Card */}
        <div className='mb-8'>
          <VotingStatusCard
            status={mapVotingStatus(votingStatus)}
            loading={isLoading}
            onStartVoting={handleStartVoting}
            onViewResults={handleViewResults}
          />
        </div>

        {/* Dashboard Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className='space-y-6'>
          <TabsList className='grid w-full grid-cols-4'>
            <TabsTrigger value='overview' className='flex items-center gap-2'>
              <BarChart3 className='h-4 w-4' />
              Overview
            </TabsTrigger>
            <TabsTrigger value='voting' className='flex items-center gap-2'>
              <Vote className='h-4 w-4' />
              Voting
            </TabsTrigger>
            <TabsTrigger value='history' className='flex items-center gap-2'>
              <History className='h-4 w-4' />
              History
            </TabsTrigger>
            <TabsTrigger value='profile' className='flex items-center gap-2'>
              <User className='h-4 w-4' />
              Profile
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value='overview' className='space-y-6'>
            {/* User Statistics */}
            <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
              <StatCard
                title='Voting Rounds'
                value={`${userStats.completedRounds}/${userStats.totalVotingRounds}`}
                subtitle='Completed rounds'
                icon={<Vote className='h-4 w-4' />}
                variant={userStats.completedRounds > 0 ? 'success' : 'warning'}
              />

              <StatCard
                title='Account Status'
                value={user?.isActive ? 'Active' : 'Inactive'}
                subtitle='Current status'
                icon={<User className='h-4 w-4' />}
                variant={user?.isActive ? 'success' : 'error'}
              />

              <StatCard
                title='District'
                value={user?.district || 'Unknown'}
                subtitle='Your district'
                icon={<Info className='h-4 w-4' />}
                variant='info'
              />

              <StatCard
                title='Member Since'
                value={userStats.accountCreated}
                subtitle='Account created'
                icon={<Calendar className='h-4 w-4' />}
                variant='default'
              />
            </div>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <History className='h-5 w-5' />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-3'>
                  {recentActivity.map(activity => (
                    <div
                      key={activity.id}
                      className='flex items-center gap-3 rounded-lg bg-muted/50 p-3'
                    >
                      <div
                        className={cn(
                          'h-2 w-2 rounded-full',
                          activity.type === 'success'
                            ? 'bg-success'
                            : activity.type === 'info'
                              ? 'bg-primary'
                              : 'bg-muted-foreground'
                        )}
                      />
                      <div className='flex-1'>
                        <p className='text-sm font-medium'>{activity.action}</p>
                        <p className='text-xs text-muted-foreground'>
                          {new Date(activity.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Voting Tab */}
          <TabsContent value='voting' className='space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Vote className='h-5 w-5' />
                  Voting Information
                </CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='grid gap-4 md:grid-cols-2'>
                  <div className='rounded-lg bg-muted/50 p-4'>
                    <h4 className='mb-2 font-medium'>Voting Status</h4>
                    <p className='text-sm text-muted-foreground'>
                      {user?.hasVoted
                        ? 'You have successfully cast your vote.'
                        : 'You have not voted yet.'}
                    </p>
                  </div>

                  <div className='rounded-lg bg-muted/50 p-4'>
                    <h4 className='mb-2 font-medium'>Your Municipality</h4>
                    <p className='text-sm text-muted-foreground'>
                      Representing: {user?.municipality}
                    </p>
                  </div>
                </div>

                {votingStatus?.votingStatus === 'active' && !user?.hasVoted && (
                  <div className='rounded-lg border border-primary/20 bg-primary/10 p-4'>
                    <h4 className='mb-2 font-medium text-primary'>Ready to Vote</h4>
                    <p className='mb-3 text-sm text-muted-foreground'>
                      The voting is currently active. You can cast your vote now.
                    </p>
                    <Button onClick={handleStartVoting} leftIcon={<Vote className='h-4 w-4' />}>
                      Start Voting
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* History Tab */}
          <TabsContent value='history' className='space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <History className='h-5 w-5' />
                  Voting History
                </CardTitle>
              </CardHeader>
              <CardContent>
                {user?.hasVoted ? (
                  <div className='space-y-3'>
                    <div className='bg-success/10 border-success/20 rounded-lg border p-4'>
                      <div className='flex items-center justify-between'>
                        <div>
                          <h4 className='text-success font-medium'>
                            Round 1 - Executive Committee Election
                          </h4>
                          <p className='text-sm text-muted-foreground'>
                            Voted on {new Date().toLocaleDateString()}
                          </p>
                        </div>
                        <Button variant='outline' size='sm' onClick={handleViewResults}>
                          View Results
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className='py-8 text-center'>
                    <History className='mx-auto mb-4 h-12 w-12 text-muted-foreground' />
                    <h3 className='mb-2 text-lg font-medium'>No Voting History</h3>
                    <p className='text-muted-foreground'>
                      You haven't participated in any voting rounds yet.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Profile Tab */}
          <TabsContent value='profile' className='space-y-6'>
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <User className='h-5 w-5' />
                  Profile Information
                </CardTitle>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='grid gap-4 md:grid-cols-2'>
                  <div>
                    <label className='text-sm font-medium text-muted-foreground'>Username</label>
                    <p className='text-sm font-medium'>{user?.username}</p>
                  </div>

                  <div>
                    <label className='text-sm font-medium text-muted-foreground'>
                      Municipality
                    </label>
                    <p className='text-sm font-medium'>{user?.municipality}</p>
                  </div>

                  <div>
                    <label className='text-sm font-medium text-muted-foreground'>District</label>
                    <p className='text-sm font-medium'>{user?.district}</p>
                  </div>

                  <div>
                    <label className='text-sm font-medium text-muted-foreground'>Role</label>
                    <p className='text-sm font-medium capitalize'>{user?.role}</p>
                  </div>

                  {user?.email && (
                    <div>
                      <label className='text-sm font-medium text-muted-foreground'>Email</label>
                      <p className='text-sm font-medium'>{user.email}</p>
                    </div>
                  )}

                  <div>
                    <label className='text-sm font-medium text-muted-foreground'>
                      Account Status
                    </label>
                    <p className='text-sm font-medium'>{user?.isActive ? 'Active' : 'Inactive'}</p>
                  </div>
                </div>

                <div className='border-t pt-4'>
                  <Button variant='outline' leftIcon={<Settings className='h-4 w-4' />}>
                    Account Settings
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Password Change Section */}
            <ChangePasswordForm
              onSuccess={() => {
                // Optional: Show success message or refresh user data
                console.log('Password changed successfully')
              }}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

export default VoterDashboard
