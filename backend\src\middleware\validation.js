import { body, param, query } from 'express-validator'

// Municipality list for validation
const MUNICIPALITIES = [
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  'Calabanga',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>moan',
  'Del Gallego',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  'Goa',
  '<PERSON>gon<PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON>bu<PERSON>',
  '<PERSON>cam<PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  'Pasaca<PERSON>',
  '<PERSON><PERSON>b<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  'San Fernando',
  'San Jose',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>igaon',
  '<PERSON>mbac',
]

const USER_ROLES = ['voter', 'admin', 'execom', 'tie-breaker']

/**
 * Handle validation errors middleware
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    })
  }
  next()
}

/**
 * Login validation
 */
export const validateLogin = [
  body('username')
    .trim()
    .notEmpty()
    .withMessage('Username is required')
    .isLength({ min: 3, max: 50 })
    .withMessage('Username must be between 3 and 50 characters')
    .toLowerCase(),

  body('password')
    .notEmpty()
    .withMessage('Password is required')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters'),
]

/**
 * User creation validation
 */
export const validateCreateUser = [
  body('username')
    .trim()
    .notEmpty()
    .withMessage('Username is required')
    .isLength({ min: 3, max: 50 })
    .withMessage('Username must be between 3 and 50 characters')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Username can only contain letters, numbers, underscores, and hyphens')
    .toLowerCase(),

  body('municipality')
    .trim()
    .notEmpty()
    .withMessage('Municipality is required')
    .isIn(MUNICIPALITIES)
    .withMessage('Invalid municipality'),

  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage(
      'Password must contain at least one lowercase letter, one uppercase letter, and one number'
    ),

  body('role').optional().isIn(USER_ROLES).withMessage('Invalid role'),
]

/**
 * User update validation
 */
export const validateUpdateUser = [
  body('municipality').optional().trim().isIn(MUNICIPALITIES).withMessage('Invalid municipality'),

  body('role').optional().isIn(USER_ROLES).withMessage('Invalid role'),

  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean'),
]

/**
 * Password change validation
 */
export const validateChangePassword = [
  body('currentPassword').notEmpty().withMessage('Current password is required'),

  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('New password must be at least 6 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage(
      'New password must contain at least one lowercase letter, one uppercase letter, and one number'
    ),

  body('confirmPassword').custom((value, { req }) => {
    if (value !== req.body.newPassword) {
      throw new Error('Password confirmation does not match new password')
    }
    return true
  }),
]

/**
 * Admin password reset validation
 */
export const validateAdminPasswordReset = [
  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('New password must be at least 6 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage(
      'New password must contain at least one lowercase letter, one uppercase letter, and one number'
    ),
]

/**
 * MongoDB ObjectId validation
 */
export const validateObjectId = (paramName = 'id') => [
  param(paramName).isMongoId().withMessage(`Invalid ${paramName} format`),
]

/**
 * Pagination validation
 */
export const validatePagination = [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),

  query('sort')
    .optional()
    .isIn(['username', 'municipality', 'role', 'createdAt', 'lastLogin', 'hasVoted'])
    .withMessage('Invalid sort field'),

  query('order').optional().isIn(['asc', 'desc']).withMessage('Order must be asc or desc'),
]

/**
 * Search validation
 */
export const validateSearch = [
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),

  query('role').optional().isIn(USER_ROLES).withMessage('Invalid role filter'),

  query('municipality').optional().isIn(MUNICIPALITIES).withMessage('Invalid municipality filter'),

  query('hasVoted').optional().isBoolean().withMessage('hasVoted must be a boolean'),
]

/**
 * Vote validation
 */
export const validateVoteSubmission = [
  body('candidateIds')
    .isArray({ min: 1, max: 15 })
    .withMessage('Must select between 1 and 15 candidates')
    .custom(candidateIds => {
      // Check for duplicates
      const uniqueIds = [...new Set(candidateIds)]
      if (uniqueIds.length !== candidateIds.length) {
        throw new Error('Duplicate candidate selections are not allowed')
      }
      return true
    }),

  body('candidateIds.*').isMongoId().withMessage('Invalid candidate ID format'),

  handleValidationErrors,
]

/**
 * Tie-breaker vote validation
 */
export const validateTieBreakerVote = [
  body('candidateId').isMongoId().withMessage('Invalid candidate ID format'),

  handleValidationErrors,
]

/**
 * Candidate validation
 */
export const validateCandidate = [
  body('name')
    .trim()
    .notEmpty()
    .withMessage('Candidate name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Candidate name must be between 2 and 100 characters'),

  body('municipality')
    .trim()
    .notEmpty()
    .withMessage('Municipality is required')
    .isIn(MUNICIPALITIES)
    .withMessage('Invalid municipality'),

  body('position')
    .optional()
    .isIn(['chairperson', 'vice-chairperson', 'secretary', 'treasurer'])
    .withMessage('Invalid position'),
]

/**
 * Bulk operations validation
 */
export const validateBulkOperation = [
  body('userIds').isArray({ min: 1 }).withMessage('Must provide at least one user ID'),

  body('userIds.*').isMongoId().withMessage('Invalid user ID format'),

  body('action')
    .isIn(['activate', 'deactivate', 'reset-votes'])
    .withMessage('Invalid bulk action'),
]

/**
 * File upload validation
 */
export const validateFileUpload = [
  body('fileType').optional().isIn(['csv', 'xlsx']).withMessage('Invalid file type'),
]
