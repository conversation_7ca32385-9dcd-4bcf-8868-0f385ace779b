import { apiClient } from '@/services/apiClient'
import { logger } from '@/utils/logger'
import { useState } from 'react'
import { usePublicSystemSettings } from './useSystemSettings'

export function useResultVisibility() {
  const [isLoading, setIsLoading] = useState(false)
  const { settings, refreshSettings } = usePublicSystemSettings()

  // Get visibility status from cached settings
  const isResultsVisible = settings.public_results_enabled?.value || false

  const toggleVisibility = async (visible: boolean) => {
    setIsLoading(true)

    try {
      const response = await apiClient.put('/system/settings/public_results_enabled', {
        value: visible
      })

      if (response.data.success) {
        // Refresh the cached settings to get the updated value
        await refreshSettings()

        logger.info('Result visibility changed', {
          newState: visible ? 'visible' : 'hidden',
          timestamp: new Date().toISOString(),
          action: 'admin_toggle_results'
        })

        return { success: true }
      } else {
        throw new Error(response.data.error || 'Failed to update visibility setting')
      }
    } catch (error) {
      logger.error('Failed to update result visibility', { error })
      return { success: false, error: 'Failed to update visibility setting' }
    } finally {
      setIsLoading(false)
    }
  }

  return {
    isResultsVisible,
    isLoading,
    toggleVisibility
  }
}

// Hook for checking if results should be visible to non-admin users
export function useCanViewResults() {
  const { settings } = usePublicSystemSettings()

  // Get visibility status from cached settings
  return settings.public_results_enabled?.value || false
}
