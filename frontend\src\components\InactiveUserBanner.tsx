import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Setting<PERSON> } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useNavigate } from 'react-router-dom'

interface InactiveUserBannerProps {
  user: any
  onShowDashboard?: () => void
}

export function InactiveUserBanner({ user, onShowDashboard }: InactiveUserBannerProps) {
  const navigate = useNavigate()

  return (
    <div className="min-h-screen bg-background py-8">
      <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <div className="space-y-6">
          {/* Warning Banner */}
          <Card className="border-amber-200 bg-amber-50">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-amber-800">
                <AlertTriangle className="h-5 w-5" />
                <span>Account Inactive</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="text-amber-700">
              <p className="mb-4">
                Your account is currently inactive and you cannot participate in voting at this time.
              </p>
              <p className="text-sm">
                Please contact an administrator if you believe this is an error or if you need to activate your account.
              </p>
            </CardContent>
          </Card>

          {/* User Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Lock className="h-5 w-5 text-muted-foreground" />
                <span>Account Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Municipality</label>
                  <p className="text-lg font-semibold">{user?.municipality || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Role</label>
                  <div className="mt-1">
                    <Badge variant="secondary">{user?.role}</Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Status</label>
                  <div className="mt-1">
                    <Badge variant="destructive">Inactive</Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">District</label>
                  <p className="text-lg font-semibold">{user?.district || 'N/A'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Available Actions */}
          <Card>
            <CardHeader>
              <CardTitle>What You Can Do</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  While your account is inactive, you can still access the following features:
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start space-x-3 p-4 rounded-lg bg-muted/50">
                    <BarChart3 className="h-5 w-5 text-primary mt-0.5" />
                    <div>
                      <h4 className="font-medium">View Election Results</h4>
                      <p className="text-sm text-muted-foreground">
                        Check the current voting results and statistics
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3 p-4 rounded-lg bg-muted/50">
                    <Settings className="h-5 w-5 text-primary mt-0.5" />
                    <div>
                      <h4 className="font-medium">Account Settings</h4>
                      <p className="text-sm text-muted-foreground">
                        Update your password and view account details
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="outline"
              onClick={() => navigate('/results')}
              className="flex items-center space-x-2"
            >
              <BarChart3 className="h-4 w-4" />
              <span>View Results</span>
            </Button>
            
            {onShowDashboard && (
              <Button
                variant="outline"
                onClick={onShowDashboard}
                className="flex items-center space-x-2"
              >
                <Settings className="h-4 w-4" />
                <span>Account Settings</span>
              </Button>
            )}
            
            <Button
              variant="outline"
              onClick={() => navigate('/')}
              className="flex items-center space-x-2"
            >
              <span>Back to Home</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
