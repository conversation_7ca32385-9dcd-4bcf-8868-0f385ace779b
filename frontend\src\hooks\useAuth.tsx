import { createContext, ReactN<PERSON>, useContext, useEffect, useState } from 'react'
import toast from 'react-hot-toast'
import { LoginFormData } from '../schemas/auth'
import { UserResponse } from '../schemas/user'
import { authService } from '../services/authService'
import { logger } from '../utils/logger'

interface AuthContextType {
  user: UserResponse | null
  isLoading: boolean
  login: (credentials: LoginFormData) => Promise<boolean>
  logout: (showConfirmation?: boolean) => Promise<boolean>
  refreshAuth: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<UserResponse | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Check for existing auth on mount
  useEffect(() => {
    const initAuth = () => {
      try {
        logger.debug('Initializing authentication state', {
          component: 'AuthProvider',
          action: 'init_auth',
        })

        const userData = authService.getUserData()
        const isAuthenticated = authService.isAuthenticated()

        if (isAuthenticated && userData) {
          setUser(userData)
          logger.info('User session restored from storage', {
            component: 'AuthProvider',
            action: 'session_restore',
            userId: userData.username,
          })
        } else {
          logger.debug('No valid session found in storage', {
            component: 'AuthProvider',
            action: 'init_auth',
          })
        }
      } catch (error) {
        logger.error('Auth initialization error', error, {
          component: 'AuthProvider',
          action: 'init_auth',
        })
        // Clear invalid auth data
        authService.logout()
      } finally {
        setIsLoading(false)
      }
    }

    initAuth()
  }, [])

  const login = async (credentials: LoginFormData): Promise<boolean> => {
    try {
      setIsLoading(true)
      const response = await authService.login(credentials)

      if (response.success) {
        setUser(response.user)
        const displayName = response.user.role === 'admin'
          ? response.user.username
          : (response.user.municipality || response.user.username)
        toast.success(`Welcome back, ${displayName}!`)
        return true
      } else {
        // Don't show toast error here - let the LoginPage handle it
        return false
      }
    } catch (error: any) {
      logger.error('Login error in AuthProvider', error, {
        component: 'AuthProvider',
        action: 'login',
        userId: credentials.username,
      })
      toast.error(error.message || 'Login failed')
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async (showConfirmation: boolean = false) => {
    if (showConfirmation) {
      const confirmed = window.confirm('Are you sure you want to log out?')
      if (!confirmed) {
        return false
      }
    }

    try {
      await authService.logout()
      setUser(null)
      toast.success('Logged out successfully')

      // Redirect to login page
      window.location.href = '/login'

      return true
    } catch (error) {
      logger.error('Logout error in AuthProvider', error, {
        component: 'AuthProvider',
        action: 'logout',
        userId: user?.username,
      })
      toast.error('Logout failed')
      return false
    }
  }

  const refreshAuth = async () => {
    try {
      const userData = await authService.getCurrentUser()
      setUser(userData)
    } catch (error) {
      logger.error('Auth refresh error in AuthProvider', error, {
        component: 'AuthProvider',
        action: 'refresh_auth',
        userId: user?.username,
      })
      await logout()
    }
  }

  const value: AuthContextType = {
    user,
    isLoading,
    login,
    logout,
    refreshAuth,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
