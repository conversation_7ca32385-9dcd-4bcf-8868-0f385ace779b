import mongoose from 'mongoose'
import dotenv from 'dotenv'
import logger from '../utils/logger.js'
import Candidate from '../models/Candidate.js'
import User from '../models/User.js'
import Vote from '../models/Vote.js'
import ElectionArchive from '../models/ElectionArchive.js'
import bcrypt from 'bcryptjs'

// Load environment variables
dotenv.config({ path: './backend/.env' })

// Sample 2024 election data (this would normally come from MySQL export)
const mysql2024Data = {
  candidates: [
    { id: 1, candidate_name: 'Iriga City', district: '1st District' },
    { id: 2, candidate_name: 'Nabua', district: '1st District' },
    { id: 3, candidate_name: 'Bato', district: '1st District' },
    { id: 4, candidate_name: 'Buhi', district: '1st District' },
    { id: 5, candidate_name: '<PERSON><PERSON><PERSON>', district: '5th District' },
    { id: 6, candidate_name: '<PERSON><PERSON>', district: '5th District' },
    { id: 7, candidate_name: 'Caramoan', district: '4th District' },
    { id: 8, candidate_name: 'Gar<PERSON>torena', district: '4th District' },
    { id: 9, candidate_name: 'Presentacion', district: '4th District' },
    { id: 10, candidate_name: 'San Jose', district: '4th District' },
    { id: 11, candidate_name: 'Tigaon', district: '4th District' },
    { id: 12, candidate_name: 'Sagñay', district: '4th District' },
    { id: 13, candidate_name: 'Lagonoy', district: '4th District' },
    { id: 14, candidate_name: 'Tinambac', district: '4th District' },
    { id: 15, candidate_name: 'Siruma', district: '4th District' },
    { id: 16, candidate_name: 'Goa', district: '4th District' },
    { id: 17, candidate_name: 'San Andres', district: '4th District' },
    { id: 18, candidate_name: 'Cabusao', district: '1st District' },
    { id: 19, candidate_name: 'Libmanan', district: '2nd District' },
    { id: 20, candidate_name: 'Sipocot', district: '2nd District' },
    { id: 21, candidate_name: 'Lupi', district: '2nd District' },
    { id: 22, candidate_name: 'Ragay', district: '2nd District' },
    { id: 23, candidate_name: 'Del Gallego', district: '2nd District' },
    { id: 24, candidate_name: 'Gainza', district: '2nd District' },
    { id: 25, candidate_name: 'Canaman', district: '3rd District' },
    { id: 26, candidate_name: 'Camaligan', district: '3rd District' },
    { id: 27, candidate_name: 'Floridablanca', district: '3rd District' },
    { id: 28, candidate_name: 'Milaor', district: '3rd District' },
    { id: 29, candidate_name: 'Naga City', district: '3rd District' },
    { id: 30, candidate_name: 'Pili', district: '3rd District' },
    { id: 31, candidate_name: 'Ocampo', district: '3rd District' },
    { id: 32, candidate_name: 'Bombon', district: '3rd District' },
    { id: 33, candidate_name: 'Magarao', district: '3rd District' },
    { id: 34, candidate_name: 'Calabanga', district: '3rd District' },
    { id: 35, candidate_name: 'Pasacao', district: '5th District' }
  ],
  voters: [
    { id: 1, candidate_name: 'Iriga City', username: 'iriga', password: 'iriga123' },
    { id: 2, candidate_name: 'Nabua', username: 'nabua', password: 'nabua456' },
    { id: 3, candidate_name: 'Bato', username: 'bato', password: 'bato789' },
    { id: 4, candidate_name: 'Buhi', username: 'buhi', password: 'buhi012' },
    { id: 5, candidate_name: 'Balatan', username: 'balatan', password: 'bala#767' },
    { id: 6, candidate_name: 'Bula', username: 'bula', password: 'bula345' },
    { id: 7, candidate_name: 'Caramoan', username: 'caramoan', password: 'cara+819' },
    { id: 8, candidate_name: 'Garchitorena', username: 'garchitorena', password: 'gar678' },
    { id: 9, candidate_name: 'Presentacion', username: 'presentacion', password: 'pres901' },
    { id: 10, candidate_name: 'San Jose', username: 'sanjose', password: 'sj234' },
    { id: 11, candidate_name: 'Tigaon', username: 'tigaon', password: 'tig567' },
    { id: 12, candidate_name: 'Sagñay', username: 'sagnay', password: 'sag890' },
    { id: 13, candidate_name: 'Lagonoy', username: 'lagonoy', password: 'lag123' },
    { id: 14, candidate_name: 'Tinambac', username: 'tinambac', password: 'tin456' },
    { id: 15, candidate_name: 'Siruma', username: 'siruma', password: 'sir789' },
    { id: 16, candidate_name: 'Goa', username: 'goa', password: 'goa012' },
    { id: 17, candidate_name: 'San Andres', username: 'sanandres', password: 'sa345' },
    { id: 18, candidate_name: 'Cabusao', username: 'cabusao', password: 'cabu=538' },
    { id: 19, candidate_name: 'Libmanan', username: 'libmanan', password: 'lib678' },
    { id: 20, candidate_name: 'Sipocot', username: 'sipocot', password: 'sip901' },
    { id: 21, candidate_name: 'Lupi', username: 'lupi', password: 'lup234' },
    { id: 22, candidate_name: 'Ragay', username: 'ragay', password: 'rag567' },
    { id: 23, candidate_name: 'Del Gallego', username: 'delgallego', password: 'del890' },
    { id: 24, candidate_name: 'Gainza', username: 'gainza', password: 'gain#464' },
    { id: 25, candidate_name: 'Canaman', username: 'canaman', password: 'can123' },
    { id: 26, candidate_name: 'Camaligan', username: 'camaligan', password: 'cam456' },
    { id: 27, candidate_name: 'Floridablanca', username: 'floridablanca', password: 'flo789' },
    { id: 28, candidate_name: 'Milaor', username: 'milaor', password: 'mil012' },
    { id: 29, candidate_name: 'Naga City', username: 'naga', password: 'naga345' },
    { id: 30, candidate_name: 'Pili', username: 'pili', password: 'pil678' },
    { id: 31, candidate_name: 'Ocampo', username: 'ocampo', password: 'oca901' },
    { id: 32, candidate_name: 'Bombon', username: 'bombon', password: 'bomb=387' },
    { id: 33, candidate_name: 'Magarao', username: 'magarao', password: 'mag234' },
    { id: 34, candidate_name: 'Calabanga', username: 'calabanga', password: 'cal567' },
    { id: 35, candidate_name: 'Pasacao', username: 'pasacao', password: 'pas890' }
  ],
  votes: [
    // Sample votes - in real scenario this would be much larger
    { voter_id: 1, candidate_id: 5 }, { voter_id: 1, candidate_id: 7 }, { voter_id: 1, candidate_id: 29 },
    { voter_id: 2, candidate_id: 7 }, { voter_id: 2, candidate_id: 29 }, { voter_id: 2, candidate_id: 5 },
    { voter_id: 3, candidate_id: 29 }, { voter_id: 3, candidate_id: 7 }, { voter_id: 3, candidate_id: 19 },
    { voter_id: 4, candidate_id: 7 }, { voter_id: 4, candidate_id: 29 }, { voter_id: 4, candidate_id: 25 },
    { voter_id: 5, candidate_id: 29 }, { voter_id: 5, candidate_id: 7 }, { voter_id: 5, candidate_id: 1 },
    { voter_id: 7, candidate_id: 29 }, { voter_id: 7, candidate_id: 19 }, { voter_id: 7, candidate_id: 25 },
    { voter_id: 8, candidate_id: 7 }, { voter_id: 8, candidate_id: 29 }, { voter_id: 8, candidate_id: 5 },
    { voter_id: 10, candidate_id: 29 }, { voter_id: 10, candidate_id: 7 }, { voter_id: 10, candidate_id: 19 },
    { voter_id: 12, candidate_id: 7 }, { voter_id: 12, candidate_id: 29 }, { voter_id: 12, candidate_id: 25 },
    { voter_id: 15, candidate_id: 29 }, { voter_id: 15, candidate_id: 7 }, { voter_id: 15, candidate_id: 1 },
    { voter_id: 18, candidate_id: 29 }, { voter_id: 18, candidate_id: 19 }, { voter_id: 18, candidate_id: 25 },
    { voter_id: 20, candidate_id: 7 }, { voter_id: 20, candidate_id: 29 }, { voter_id: 20, candidate_id: 5 },
    { voter_id: 22, candidate_id: 29 }, { voter_id: 22, candidate_id: 7 }, { voter_id: 22, candidate_id: 19 },
    { voter_id: 25, candidate_id: 7 }, { voter_id: 25, candidate_id: 29 }, { voter_id: 25, candidate_id: 25 },
    { voter_id: 28, candidate_id: 29 }, { voter_id: 28, candidate_id: 7 }, { voter_id: 28, candidate_id: 1 },
    { voter_id: 30, candidate_id: 29 }, { voter_id: 30, candidate_id: 19 }, { voter_id: 30, candidate_id: 25 },
    { voter_id: 32, candidate_id: 7 }, { voter_id: 32, candidate_id: 29 }, { voter_id: 32, candidate_id: 5 },
    { voter_id: 34, candidate_id: 29 }, { voter_id: 34, candidate_id: 7 }, { voter_id: 34, candidate_id: 19 }
  ]
}

/**
 * Connect to MongoDB
 */
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI)
    logger.info('MongoDB connected for 2024 data migration')
  } catch (error) {
    logger.error('MongoDB connection error:', error)
    process.exit(1)
  }
}

/**
 * Migrate 2024 election data from MySQL format to MongoDB
 */
const migrate2024ElectionData = async () => {
  try {
    logger.info('🚀 STARTING 2024 ELECTION DATA MIGRATION...')
    
    await connectDB()
    
    // Step 1: Clear existing data (optional - be careful in production)
    logger.info('📝 Clearing existing test data...')
    await Candidate.deleteMany({ municipalityName: { $in: mysql2024Data.candidates.map(c => c.candidate_name) } })
    await User.deleteMany({ municipality: { $in: mysql2024Data.voters.map(v => v.candidate_name) } })
    await Vote.deleteMany({})
    
    // Step 2: Migrate candidates
    logger.info('👥 Migrating candidates...')
    const candidateMap = new Map()
    
    for (const mysqlCandidate of mysql2024Data.candidates) {
      const candidate = new Candidate({
        municipalityName: mysqlCandidate.candidate_name,
        district: mysqlCandidate.district,
        totalVotes: 0, // Will be calculated from votes
        isActive: true
      })
      
      const savedCandidate = await candidate.save()
      candidateMap.set(mysqlCandidate.id, savedCandidate._id)
      logger.info(`✅ Migrated candidate: ${mysqlCandidate.candidate_name} (${mysqlCandidate.district})`)
    }
    
    // Step 3: Migrate voters (as Users)
    logger.info('🗳️ Migrating voters...')
    const voterMap = new Map()
    
    for (const mysqlVoter of mysql2024Data.voters) {
      const hashedPassword = await bcrypt.hash(mysqlVoter.password, 12)
      
      const user = new User({
        username: mysqlVoter.username,
        password: hashedPassword,
        municipality: mysqlVoter.candidate_name, // This is the voter's municipality
        district: mysql2024Data.candidates.find(c => c.candidate_name === mysqlVoter.candidate_name)?.district || 'Unknown',
        role: 'voter',
        isActive: true,
        hasVoted: false // Will be updated when processing votes
      })
      
      const savedUser = await user.save()
      voterMap.set(mysqlVoter.id, savedUser._id)
      logger.info(`✅ Migrated voter: ${mysqlVoter.username} from ${mysqlVoter.candidate_name}`)
    }
    
    // Step 4: Migrate votes and calculate totals
    logger.info('📊 Migrating votes and calculating totals...')
    const voteCounts = new Map()
    const votersWhoVoted = new Set()
    
    for (const mysqlVote of mysql2024Data.votes) {
      const voterId = voterMap.get(mysqlVote.voter_id)
      const candidateId = candidateMap.get(mysqlVote.candidate_id)
      
      if (voterId && candidateId) {
        const vote = new Vote({
          voter: voterId,
          candidate: candidateId,
          votedAt: new Date('2024-11-21T10:00:00Z') // Sample vote date
        })
        
        await vote.save()
        
        // Count votes for each candidate
        const currentCount = voteCounts.get(candidateId.toString()) || 0
        voteCounts.set(candidateId.toString(), currentCount + 1)
        
        // Track voters who voted
        votersWhoVoted.add(voterId.toString())
        
        logger.info(`✅ Migrated vote: Voter ${mysqlVote.voter_id} → Candidate ${mysqlVote.candidate_id}`)
      }
    }
    
    // Step 5: Update candidate vote totals
    logger.info('🔢 Updating candidate vote totals...')
    for (const [candidateId, voteCount] of voteCounts) {
      await Candidate.findByIdAndUpdate(candidateId, { totalVotes: voteCount })
      const candidate = await Candidate.findById(candidateId)
      logger.info(`✅ Updated ${candidate.municipalityName}: ${voteCount} votes`)
    }
    
    // Step 6: Update voter hasVoted status
    logger.info('✅ Updating voter status...')
    for (const voterId of votersWhoVoted) {
      await User.findByIdAndUpdate(voterId, { hasVoted: true })
    }
    
    // Step 7: Create 2024 election archive
    logger.info('📁 Creating 2024 election archive...')
    
    const candidates = await Candidate.find({ isActive: true })
      .select('municipalityName district totalVotes')
      .sort({ totalVotes: -1, municipalityName: 1 })
    
    const totalVoters = await User.countDocuments({ role: 'voter' })
    const totalVotesCast = await User.countDocuments({ role: 'voter', hasVoted: true })
    const participationRate = totalVoters > 0 ? (totalVotesCast / totalVoters) * 100 : 0
    
    const archive = new ElectionArchive({
      year: 2024,
      description: '2024 DFPTA Election Results - Migrated from MySQL',
      filename: 'November 21, 2024',
      results: candidates.map((candidate, index) => ({
        rank: index + 1,
        municipalityName: candidate.municipalityName,
        district: candidate.district,
        voteCount: candidate.totalVotes
      })),
      statistics: {
        totalVoters,
        totalVotesCast,
        participationRate: Math.round(participationRate * 100) / 100,
        totalCandidates: candidates.length
      },
      archivedBy: null, // System migration
      archivedAt: new Date('2024-11-21T15:00:00Z')
    })
    
    await archive.save()
    
    // Final Summary
    logger.info('🎉 2024 ELECTION DATA MIGRATION COMPLETED!')
    logger.info('=' .repeat(60))
    logger.info('📊 MIGRATION SUMMARY:')
    logger.info(`✅ Candidates migrated: ${mysql2024Data.candidates.length}`)
    logger.info(`✅ Voters migrated: ${mysql2024Data.voters.length}`)
    logger.info(`✅ Votes migrated: ${mysql2024Data.votes.length}`)
    logger.info(`✅ Voters who participated: ${votersWhoVoted.size}`)
    logger.info(`✅ Participation rate: ${participationRate.toFixed(1)}%`)
    logger.info(`✅ Archive created: November 21, 2024`)
    logger.info('=' .repeat(60))
    
    // Display top candidates
    logger.info('🏆 TOP 10 CANDIDATES:')
    const topCandidates = candidates.slice(0, 10)
    topCandidates.forEach((candidate, index) => {
      logger.info(`${index + 1}. ${candidate.municipalityName} (${candidate.district}): ${candidate.totalVotes} votes`)
    })
    
    process.exit(0)
    
  } catch (error) {
    logger.error('💥 2024 data migration failed:', error)
    process.exit(1)
  }
}

// Run migration
migrate2024ElectionData()
