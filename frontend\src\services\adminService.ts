import { PaginatedResponse, User } from '../types'
import { apiClient } from './apiClient'

export interface DashboardStats {
  totalUsers: number
  totalVoters: number
  totalActiveVoters: number
  votedUsers: number
  pendingVoters: number
  activeUsers: number
  votingProgress: number
}

export interface DashboardData {
  stats: DashboardStats
  usersByRole: Record<string, number>
  recentUsers: User[]
}

export interface UserFilters {
  page?: number
  limit?: number
  search?: string
  role?: string
  municipality?: string
  district?: string
  hasVoted?: boolean
  sort?: string
  order?: 'asc' | 'desc'
}

export interface CreateUserData {
  username: string
  municipality: string
  password: string
  role?: string
  email?: string
}

// UpdateUserData interface removed - edit user functionality not needed

export interface BulkOperationData {
  userIds: string[]
  action: 'activate' | 'deactivate' | 'delete' | 'reset-votes'
}

export interface CandidateResult {
  municipalityName: string
  district: string
  voteCount: number
  rank: number
  candidateName?: string
}

export interface AdminResultsData {
  resultsByDistrict: Record<string, CandidateResult[]>
  unifiedResults: CandidateResult[]
  totalStats: {
    totalVotes: number
    totalVoters: number
    totalActiveVoters: number
    totalVotesCast: number
    participationRate: number
    totalCandidates: number
    totalActiveCandidates: number
  }
  showCandidateNames: boolean
  sortBy: string
  viewMode?: string
}

export const adminService = {
  // Dashboard
  async getDashboardStats(): Promise<DashboardData> {
    const response = await apiClient.get('/admin/dashboard')
    return response.data.data
  },

  // Admin Results
  async getAdminResults(
    showNames: boolean = false,
    sortBy: string = 'votes'
  ): Promise<AdminResultsData> {
    const params = new URLSearchParams()
    params.append('showNames', showNames.toString())
    params.append('sortBy', sortBy)

    const response = await apiClient.get(`/admin/results?${params.toString()}`)
    return response.data.data
  },

  // Users
  async getUsers(filters: UserFilters = {}): Promise<PaginatedResponse<User>> {
    const params = new URLSearchParams()

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString())
      }
    })

    const response = await apiClient.get(`/admin/users?${params.toString()}`)
    return response.data
  },

  // getUserById function removed - view user functionality not needed

  async createUser(userData: CreateUserData): Promise<User> {
    const response = await apiClient.post('/admin/users', userData)
    return response.data.data.user
  },

  // updateUser function removed - edit user functionality not needed

  async deleteUser(id: string): Promise<void> {
    await apiClient.delete(`/admin/users/${id}`)
  },

  async resetUserVote(id: string): Promise<void> {
    await apiClient.post(`/admin/users/${id}/reset-vote`)
  },

  async toggleUserStatus(id: string, isActive: boolean): Promise<User> {
    const response = await apiClient.put(`/admin/users/${id}/status`, { isActive })
    return response.data.data.user
  },

  async bulkUserOperations(data: BulkOperationData): Promise<{ modifiedCount: number }> {
    const response = await apiClient.post('/admin/users/bulk', data)
    return response.data.data
  },

  // Utility functions
  async exportUsers(format: 'csv' | 'xlsx' = 'csv'): Promise<Blob> {
    const response = await apiClient.get(`/admin/users/export?format=${format}`, {
      responseType: 'blob',
    })
    return response.data
  },

  async importUsers(file: File): Promise<{ imported: number; errors: string[] }> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await apiClient.post('/admin/users/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data.data
  },

  // District-based methods
  async getVotingStatsByDistrict(): Promise<any> {
    const response = await apiClient.get('/admin/voting/stats-by-district')
    return response.data.data
  },

  async getUsersByDistrict(district: string): Promise<User[]> {
    const response = await apiClient.get(`/admin/users/district/${district}`)
    return response.data.data.users
  },

  async getDistrictResults(district?: string): Promise<any> {
    const url = district ? `/admin/results/district/${district}` : '/admin/results/by-district'
    const response = await apiClient.get(url)
    return response.data.data
  },

  async toggleResultsVisibility(visible: boolean): Promise<any> {
    const response = await apiClient.post('/admin/voting/toggle-results', { visible })
    return response.data.data
  },

  async getResultsVisibilityStatus(): Promise<{ visible: boolean; votingActive: boolean }> {
    const response = await apiClient.get('/admin/voting/results-status')
    return response.data.data
  },
}
