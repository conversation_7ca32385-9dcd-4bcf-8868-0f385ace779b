import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Skeleton } from '@/components/ui/skeleton'
import { Switch } from '@/components/ui/switch'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { Award, Eye, EyeOff, Medal, Trophy } from 'lucide-react'
import { useState } from 'react'

interface CandidateResult {
  municipalityName: string
  district: string
  voteCount: number
  rank: number
  candidateName?: string
  isActive?: boolean
  status?: string
}

interface ResultsByDistrict {
  [district: string]: CandidateResult[]
}

interface TotalStats {
  totalVotes: number
  totalVoters: number
  totalVotesCast: number
  participationRate: number
  totalCandidates: number
  totalActiveVoters?: number
}

interface ResultsDisplayTableProps {
  resultsByDistrict: ResultsByDistrict | null
  unifiedResults?: CandidateResult[]
  totalStats?: TotalStats | null
  showCandidateNames: boolean
  isLoading: boolean
  onToggleNames: (show: boolean) => void
}

export function ResultsDisplayTable({
  resultsByDistrict,
  unifiedResults,
  totalStats,
  showCandidateNames,
  isLoading,
  onToggleNames,
}: ResultsDisplayTableProps) {
  const [selectedDistrict, setSelectedDistrict] = useState<string>('all')
  const [viewMode, setViewMode] = useState<'unified' | 'district'>('unified')
  // Default to district display, admin can toggle to municipality
  const [displayMode, setDisplayMode] = useState<'district' | 'municipality'>('district')

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <Skeleton className='h-6 w-48' />
            <Skeleton className='h-6 w-32' />
          </div>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton key={i} className='h-12 w-full' />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!resultsByDistrict && !unifiedResults) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Voting Results</CardTitle>
        </CardHeader>
        <CardContent>
          <p className='text-muted-foreground py-8 text-center'>No voting results available</p>
        </CardContent>
      </Card>
    )
  }

  const districts = resultsByDistrict ? Object.keys(resultsByDistrict).sort() : []
  const displayData =
    selectedDistrict === 'all'
      ? resultsByDistrict
      : resultsByDistrict
        ? { [selectedDistrict]: resultsByDistrict[selectedDistrict] }
        : null

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className='h-4 w-4 text-yellow-500' />
      case 2:
        return <Medal className='h-4 w-4 text-gray-400' />
      case 3:
        return <Award className='h-4 w-4 text-amber-600' />
      default:
        return null
    }
  }

  const getRankBadge = (rank: number) => {
    if (rank <= 3) {
      const colors = {
        1: 'bg-yellow-100 text-yellow-800',
        2: 'bg-gray-100 text-gray-800',
        3: 'bg-amber-100 text-amber-800',
      }
      return <Badge className={colors[rank as keyof typeof colors]}>#{rank}</Badge>
    }
    return <Badge variant='outline'>#{rank}</Badge>
  }

  return (
    <Card>
      <CardHeader>
        {/* Admin Results Header */}
        <div className='mb-4 text-center'>
          <h2 className='mb-2 text-2xl font-bold text-gray-900'>Election Results</h2>
          <p className='text-muted-foreground text-sm'>Administrative view with full vote counts</p>
          {totalStats && (
            <div className='mt-3 flex justify-center gap-6 text-sm'>
              <div className='flex items-center gap-2'>
                <span className='font-medium'>Turnout:</span>
                <Badge variant='outline' className='font-bold'>
                  {totalStats.participationRate.toFixed(1)}%
                </Badge>
                <span className='text-muted-foreground'>
                  ({totalStats.totalVotesCast} of {totalStats.totalVoters} voters)
                </span>
              </div>
            </div>
          )}
        </div>

        <div className='flex items-center justify-between'>
          <CardTitle className='flex items-center gap-2'>
            {viewMode === 'unified'
              ? 'Unified Voting Results (1-35)'
              : 'Voting Results by District'}
            <Badge variant='secondary'>
              {viewMode === 'unified'
                ? `${unifiedResults?.length || 0} Candidates`
                : `${districts.length} Districts`}
            </Badge>
          </CardTitle>
          <div className='flex items-center space-x-4'>
            {/* View Mode Toggle */}
            <div className='flex items-center space-x-2'>
              <Button
                variant={viewMode === 'unified' ? 'default' : 'outline'}
                size='sm'
                onClick={() => setViewMode('unified')}
              >
                Unified (1-35)
              </Button>
              <Button
                variant={viewMode === 'district' ? 'default' : 'outline'}
                size='sm'
                onClick={() => setViewMode('district')}
              >
                By District
              </Button>
            </div>

            {/* Display Mode Toggle - Admin Only */}
            {viewMode === 'unified' && (
              <div className='flex items-center space-x-2'>
                <Button
                  variant={displayMode === 'district' ? 'default' : 'outline'}
                  size='sm'
                  onClick={() => setDisplayMode('district')}
                  title='Show district names (default view)'
                >
                  Show Districts
                </Button>
                <Button
                  variant={displayMode === 'municipality' ? 'default' : 'outline'}
                  size='sm'
                  onClick={() => setDisplayMode('municipality')}
                  title='Show municipality names (admin toggle)'
                >
                  Show Municipalities
                </Button>
              </div>
            )}

            <div className='flex items-center space-x-2'>
              <Switch
                id='show-names'
                checked={showCandidateNames}
                onCheckedChange={onToggleNames}
              />
              <Label htmlFor='show-names' className='text-sm'>
                {showCandidateNames ? (
                  <span className='flex items-center gap-1'>
                    <Eye className='h-4 w-4' />
                    Show Names
                  </span>
                ) : (
                  <span className='flex items-center gap-1'>
                    <EyeOff className='h-4 w-4' />
                    Hide Names
                  </span>
                )}
              </Label>
            </div>
          </div>
        </div>

        {/* District Filter - Only show in district view mode */}
        {viewMode === 'district' && (
          <div className='mt-4 flex flex-wrap gap-2'>
            <Button
              variant={selectedDistrict === 'all' ? 'default' : 'outline'}
              size='sm'
              onClick={() => setSelectedDistrict('all')}
            >
              All Districts
            </Button>
            {districts.map(district => (
              <Button
                key={district}
                variant={selectedDistrict === district ? 'default' : 'outline'}
                size='sm'
                onClick={() => setSelectedDistrict(district)}
              >
                {district}
              </Button>
            ))}
          </div>
        )}
      </CardHeader>

      <CardContent>
        {viewMode === 'unified' && unifiedResults ? (
          // Unified Results View (1-35 ranking) - Exactly 7 columns
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className='w-16'>Rank</TableHead>
                  <TableHead>Municipality</TableHead>
                  <TableHead>District</TableHead>
                  <TableHead className='text-center'>Status</TableHead>
                  <TableHead className='text-right'>Votes</TableHead>
                  <TableHead className='text-center'>Percentage</TableHead>
                  <TableHead className='text-center'>Position</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {unifiedResults.map(candidate => {
                  const percentage = totalStats?.totalVotes ? ((candidate.voteCount / totalStats.totalVotes) * 100).toFixed(1) : '0.0'
                  const isInactive = candidate.isActive === false

                  return (
                    <TableRow
                      key={`${candidate.district}-${candidate.municipalityName}`}
                      className={isInactive ? 'opacity-60 bg-muted/30' : ''}
                    >
                      <TableCell>
                        <div className='flex items-center gap-2'>
                          {getRankIcon(candidate.rank)}
                          {getRankBadge(candidate.rank)}
                        </div>
                      </TableCell>
                      <TableCell className='font-medium'>
                        {candidate.municipalityName}
                        {isInactive && <span className="text-xs text-muted-foreground ml-2">(Absent)</span>}
                      </TableCell>
                      <TableCell className='text-muted-foreground'>
                        {candidate.district}
                      </TableCell>
                      <TableCell className='text-center'>
                        <Badge variant={isInactive ? 'secondary' : 'default'} className="text-xs">
                          {candidate.status || (isInactive ? 'Inactive' : 'Active')}
                        </Badge>
                      </TableCell>
                      <TableCell className='text-right font-semibold'>
                        {candidate.voteCount.toLocaleString()}
                      </TableCell>
                      <TableCell className='text-center text-muted-foreground'>
                        {percentage}%
                      </TableCell>
                      <TableCell className='text-center'>
                        <Badge variant="outline" className="text-xs">
                          {candidate.rank <= 15 ? `Top ${candidate.rank}` : `#${candidate.rank}`}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          </div>
        ) : (
          // District-based Results View
          <div className='space-y-6'>
            {displayData &&
              Object.entries(displayData).map(([district, candidates]) => (
                <div key={district}>
                  <h3 className='mb-3 flex items-center gap-2 text-lg font-semibold'>
                    {district}
                    <Badge variant='outline'>{candidates.length} Candidates</Badge>
                  </h3>

                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className='w-16'>Rank</TableHead>
                        <TableHead>Municipality</TableHead>
                        {showCandidateNames && <TableHead>Candidate</TableHead>}
                        <TableHead className='text-right'>Votes</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {candidates.map(candidate => (
                        <TableRow key={`${district}-${candidate.municipalityName}`}>
                          <TableCell>
                            <div className='flex items-center gap-2'>
                              {getRankIcon(candidate.rank)}
                              {getRankBadge(candidate.rank)}
                            </div>
                          </TableCell>
                          <TableCell className='font-medium'>
                            {candidate.municipalityName}
                          </TableCell>
                          {showCandidateNames && (
                            <TableCell className='text-muted-foreground'>
                              {candidate.candidateName || candidate.municipalityName}
                            </TableCell>
                          )}
                          <TableCell className='text-right font-mono'>
                            {candidate.voteCount.toLocaleString()}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
