import { ReactNode } from 'react'
import { useLocation } from 'react-router-dom'
import { <PERSON><PERSON>, Header } from './organisms'

interface LayoutProps {
  children: ReactNode
}

const Layout = ({ children }: LayoutProps) => {
  const location = useLocation()

  // Don't show the main header on admin pages since they have their own DashboardHeader
  const showHeader = !location.pathname.startsWith('/admin')

  return (
    <div className='flex min-h-screen flex-col'>
      {showHeader && <Header />}
      <main className='flex-1'>{children}</main>
      <Footer />
    </div>
  )
}

export default Layout
