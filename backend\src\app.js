import compression from 'compression'
import cookieParser from 'cookie-parser'
import cors from 'cors'
import dotenv from 'dotenv'
import express from 'express'
import mongoSanitize from 'express-mongo-sanitize'
import rateLimit from 'express-rate-limit'
import helmet from 'helmet'
import hpp from 'hpp'
import morgan from 'morgan'
import swaggerJsdoc from 'swagger-jsdoc'
import swaggerUi from 'swagger-ui-express'
import xss from 'xss-clean'

import connectDB from './config/database.js'
import errorHandler from './middleware/errorHandler.js'
import notFound from './middleware/notFound.js'
import logger from './utils/logger.js'

// Import routes
import adminRoutes from './routes/admin.js'
import authRoutes from './routes/auth.js'
import publicRoutes from './routes/public.js'
import systemRoutes from './routes/system.js'
import votingSimpleRoutes from './routes/votingSimple.js'

// Load environment variables
dotenv.config()

// Create Express app
const app = express()

// Connect to MongoDB
connectDB()

// Trust proxy for rate limiting
app.set('trust proxy', 1)

// Security middleware
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", 'data:', 'https:'],
      },
    },
  })
)

// Rate limiting - only for authentication endpoints
const authLimiter = rateLimit({
  windowMs: (process.env.RATE_LIMIT_WINDOW || 1) * 60 * 1000, // 1 minute
  max: process.env.RATE_LIMIT_MAX || 100, // limit each IP to 100 auth requests per minute
  message: {
    error: 'Too many authentication attempts from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
})

// Apply rate limiting only to auth routes
app.use('/api/auth', authLimiter)

// CORS configuration - Updated to include port 3001
const corsOptions = {
  origin:
    process.env.NODE_ENV === 'production'
      ? process.env.FRONTEND_URL
      : ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:5173'],
  credentials: true,
  optionsSuccessStatus: 200,
}

app.use(cors(corsOptions))

// Body parsing middleware
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))
app.use(cookieParser())

// Data sanitization against NoSQL query injection
app.use(mongoSanitize())

// Data sanitization against XSS
app.use(xss())

// Prevent parameter pollution
app.use(hpp())

// Compression middleware
app.use(compression())

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'))
} else {
  app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }))
}

// Swagger documentation setup
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'DFPTA E-Voting System API',
      version: '2.0.0',
      description: 'API documentation for DFPTA E-Voting System',
    },
    servers: [
      {
        url:
          process.env.NODE_ENV === 'production'
            ? process.env.API_URL
            : `http://localhost:${process.env.PORT || 5000}`,
        description:
          process.env.NODE_ENV === 'production' ? 'Production server' : 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
  },
  apis: ['./src/routes/*.js', './src/models/*.js'],
}

const specs = swaggerJsdoc(swaggerOptions)

// Swagger UI
if (process.env.NODE_ENV === 'development') {
  app.use('/api/docs', swaggerUi.serve, swaggerUi.setup(specs))
}

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'DFPTA E-Voting System API is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
  })
})

// API routes
app.use('/api/auth', authRoutes)
app.use('/api/system', systemRoutes)
app.use('/api', votingSimpleRoutes)
app.use('/api/admin', adminRoutes)
app.use('/api/results', publicRoutes)

// 404 handler
app.use(notFound)

// Global error handler
app.use(errorHandler)

// Start server
const PORT = process.env.PORT || 5000

const server = app.listen(PORT, () => {
  logger.info(`Server running in ${process.env.NODE_ENV} mode on port ${PORT}`)
  if (process.env.NODE_ENV === 'development') {
    logger.info(`API Documentation available at http://localhost:${PORT}/api/docs`)
  }
})

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  logger.error(`Unhandled Rejection: ${err.message}`)
  server.close(() => {
    process.exit(1)
  })
})

// Handle uncaught exceptions
process.on('uncaughtException', err => {
  logger.error(`Uncaught Exception: ${err.message}`)
  process.exit(1)
})

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received. Shutting down gracefully...')
  server.close(() => {
    logger.info('Process terminated')
  })
})

export default app
