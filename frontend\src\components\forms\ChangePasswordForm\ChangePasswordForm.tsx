import { Alert, AlertDescription } from '@/components/ui/alert'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { changePasswordSchema, type ChangePasswordFormData } from '@/schemas/auth'
import { apiClient } from '@/services/apiClient'
import { logger } from '@/utils/logger'
import { zodResolver } from '@hookform/resolvers/zod'
import { AlertCircle, CheckCircle, Eye, EyeOff, Lock } from 'lucide-react'
import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'

interface ChangePasswordFormProps {
  className?: string
  onSuccess?: () => void
}

export const ChangePasswordForm: React.FC<ChangePasswordFormProps> = ({ className, onSuccess }) => {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<ChangePasswordFormData>({
    resolver: zodResolver(changePasswordSchema),
  })

  const newPassword = watch('newPassword')

  const onSubmit = async (data: ChangePasswordFormData) => {
    setIsSubmitting(true)
    setSubmitError(null)

    try {
      const response = await apiClient.put('/auth/change-password', {
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
        confirmPassword: data.confirmPassword,
      })

      if (response.data.success) {
        toast.success('Password changed successfully', {
          description: 'Your password has been updated.',
        })

        logger.info('Password changed successfully', {
          component: 'ChangePasswordForm',
          action: 'changePassword',
        })

        reset()
        onSuccess?.()
      } else {
        throw new Error(response.data.error || 'Failed to change password')
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || 'Failed to change password'
      setSubmitError(errorMessage)

      toast.error('Failed to change password', {
        description: errorMessage,
      })

      logger.error('Error changing password', new Error(errorMessage), {
        component: 'ChangePasswordForm',
        action: 'changePassword',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const getPasswordStrength = (password: string) => {
    if (!password) return { strength: 0, label: '', color: '' }

    let strength = 0
    if (password.length >= 5) strength++
    if (password.length >= 8) strength++
    if (/[a-z]/.test(password)) strength++
    if (/[A-Z]/.test(password)) strength++
    if (/\d/.test(password)) strength++
    if (/[@$!%*?&]/.test(password)) strength++

    const levels = [
      { strength: 0, label: 'Very Weak', color: 'bg-red-500' },
      { strength: 1, label: 'Weak', color: 'bg-red-400' },
      { strength: 2, label: 'Fair', color: 'bg-yellow-500' },
      { strength: 3, label: 'Good', color: 'bg-yellow-400' },
      { strength: 4, label: 'Strong', color: 'bg-green-500' },
      { strength: 5, label: 'Very Strong', color: 'bg-green-600' },
      { strength: 6, label: 'Excellent', color: 'bg-green-700' },
    ]

    return levels[Math.min(strength, levels.length - 1)] || levels[0]
  }

  const passwordStrength = getPasswordStrength(newPassword || '')

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <Lock className='h-5 w-5' />
          Change Password
        </CardTitle>
        <CardDescription>
          Update your account password. Make sure to use a strong password.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
          {submitError && (
            <Alert variant='destructive'>
              <AlertCircle className='h-4 w-4' />
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          {/* Current Password */}
          <div className='space-y-2'>
            <Label htmlFor='currentPassword'>Current Password</Label>
            <div className='relative'>
              <Input
                id='currentPassword'
                type={showCurrentPassword ? 'text' : 'password'}
                placeholder='Enter your current password'
                {...register('currentPassword')}
                className={errors.currentPassword ? 'border-red-500' : ''}
              />
              <Button
                type='button'
                variant='ghost'
                size='sm'
                className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
              >
                {showCurrentPassword ? <EyeOff className='h-4 w-4' /> : <Eye className='h-4 w-4' />}
              </Button>
            </div>
            {errors.currentPassword && (
              <p className='text-sm text-red-500'>{errors.currentPassword.message}</p>
            )}
          </div>

          {/* New Password */}
          <div className='space-y-2'>
            <Label htmlFor='newPassword'>New Password</Label>
            <div className='relative'>
              <Input
                id='newPassword'
                type={showNewPassword ? 'text' : 'password'}
                placeholder='Enter your new password'
                {...register('newPassword')}
                className={errors.newPassword ? 'border-red-500' : ''}
              />
              <Button
                type='button'
                variant='ghost'
                size='sm'
                className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                onClick={() => setShowNewPassword(!showNewPassword)}
              >
                {showNewPassword ? <EyeOff className='h-4 w-4' /> : <Eye className='h-4 w-4' />}
              </Button>
            </div>
            {errors.newPassword && (
              <p className='text-sm text-red-500'>{errors.newPassword.message}</p>
            )}

            {/* Password Strength Indicator */}
            {newPassword && (
              <div className='space-y-2'>
                <div className='flex items-center gap-2'>
                  <div className='h-2 flex-1 rounded-full bg-gray-200'>
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${passwordStrength.color}`}
                      style={{ width: `${(passwordStrength.strength / 6) * 100}%` }}
                    />
                  </div>
                  <span className='text-xs font-medium'>{passwordStrength.label}</span>
                </div>
                <div className='text-xs text-gray-600'>
                  Password must be at least 5 characters and contain at least one number
                </div>
              </div>
            )}
          </div>

          {/* Confirm Password */}
          <div className='space-y-2'>
            <Label htmlFor='confirmPassword'>Confirm New Password</Label>
            <div className='relative'>
              <Input
                id='confirmPassword'
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder='Confirm your new password'
                {...register('confirmPassword')}
                className={errors.confirmPassword ? 'border-red-500' : ''}
              />
              <Button
                type='button'
                variant='ghost'
                size='sm'
                className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? <EyeOff className='h-4 w-4' /> : <Eye className='h-4 w-4' />}
              </Button>
            </div>
            {errors.confirmPassword && (
              <p className='text-sm text-red-500'>{errors.confirmPassword.message}</p>
            )}
          </div>

          {/* Submit Button */}
          <div className='flex gap-3 pt-4'>
            <Button type='submit' disabled={isSubmitting} className='flex-1'>
              {isSubmitting ? (
                <>
                  <div className='mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white' />
                  Changing Password...
                </>
              ) : (
                <>
                  <CheckCircle className='mr-2 h-4 w-4' />
                  Change Password
                </>
              )}
            </Button>
            <Button type='button' variant='outline' onClick={() => reset()} disabled={isSubmitting}>
              Reset
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
