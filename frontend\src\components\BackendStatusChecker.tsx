import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { getBackendStatus } from '@/utils/healthCheck'
import { AlertCircle, CheckCircle, RefreshCw, Server, Wifi, WifiOff } from 'lucide-react'
import { useEffect, useState } from 'react'

interface BackendStatusCheckerProps {
  className?: string
}

export const BackendStatusChecker: React.FC<BackendStatusCheckerProps> = ({ className }) => {
  const [status, setStatus] = useState<string>('Checking...')
  const [isChecking, setIsChecking] = useState(false)
  const [lastChecked, setLastChecked] = useState<Date | null>(null)

  const checkStatus = async () => {
    setIsChecking(true)
    try {
      const newStatus = await getBackendStatus()
      setStatus(newStatus)
      setLastChecked(new Date())
    } catch (error) {
      setStatus('🔴 Status check failed')
      setLastChecked(new Date())
    } finally {
      setIsChecking(false)
    }
  }

  useEffect(() => {
    checkStatus()
  }, [])

  const getStatusIcon = () => {
    if (status.includes('🟢')) return <CheckCircle className='h-5 w-5 text-green-600' />
    if (status.includes('🟡')) return <AlertCircle className='h-5 w-5 text-yellow-600' />
    if (status.includes('🔴')) return <WifiOff className='h-5 w-5 text-red-600' />
    return <Server className='h-5 w-5 text-gray-600' />
  }

  const getStatusColor = () => {
    if (status.includes('🟢')) return 'border-green-200 bg-green-50'
    if (status.includes('🟡')) return 'border-yellow-200 bg-yellow-50'
    if (status.includes('🔴')) return 'border-red-200 bg-red-50'
    return 'border-gray-200 bg-gray-50'
  }

  return (
    <Card className={`${getStatusColor()} ${className}`}>
      <CardHeader className='pb-3'>
        <CardTitle className='flex items-center gap-2 text-sm'>
          {getStatusIcon()}
          Backend Status
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-3'>
        <div className='flex items-center justify-between'>
          <span className='text-sm font-medium'>{status}</span>
          <Button
            variant='outline'
            size='sm'
            onClick={checkStatus}
            disabled={isChecking}
            className='h-8'
          >
            {isChecking ? (
              <RefreshCw className='h-4 w-4 animate-spin' />
            ) : (
              <Wifi className='h-4 w-4' />
            )}
          </Button>
        </div>

        {lastChecked && (
          <p className='text-xs text-muted-foreground'>
            Last checked: {lastChecked.toLocaleTimeString()}
          </p>
        )}

        {status.includes('🔴') && (
          <div className='mt-3 rounded-lg bg-red-100 p-3'>
            <h4 className='mb-2 text-sm font-medium text-red-800'>Troubleshooting Tips:</h4>
            <ul className='space-y-1 text-xs text-red-700'>
              <li>• Make sure the backend server is running on port 5000</li>
              <li>• Check if the backend has CORS properly configured</li>
              <li>• Verify the backend is not rate limiting requests</li>
              <li>• Try refreshing the page or restarting the backend</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
