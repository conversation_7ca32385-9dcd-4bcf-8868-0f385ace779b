import { apiClient } from '@/services/apiClient'
import { logger } from '@/utils/logger'
import { useEffect, useState } from 'react'
import { useQuery } from 'react-query'

interface SystemSetting {
  _id: string
  key: string
  value: any
  type: 'boolean' | 'string' | 'number' | 'object' | 'array'
  description: string
  category: 'system' | 'voting' | 'display' | 'security' | 'notifications'
  isEditable: boolean
  requiresRestart: boolean
  lastModifiedBy?: string
  lastModifiedAt: string
  defaultValue?: any
  createdAt: string
  updatedAt: string
}

interface UseSystemSettingsReturn {
  settings: SystemSetting[]
  isLoading: boolean
  error: string | null
  getSetting: (key: string) => SystemSetting | undefined
  updateSetting: (key: string, value: any) => Promise<boolean>
  toggleElection: () => Promise<boolean>
  isElectionActive: boolean
  refreshSettings: () => Promise<void>
}

export const useSystemSettings = (): UseSystemSettingsReturn => {
  const [settings, setSettings] = useState<SystemSetting[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Get a specific setting by key
  const getSetting = (key: string): SystemSetting | undefined => {
    return settings.find(setting => setting.key === key)
  }

  // Get election active status
  const isElectionActive = getSetting('voting_session_active')?.value || false

  // Fetch all settings
  const fetchSettings = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await apiClient.get('/system/settings')

      if (response.data.success) {
        setSettings(response.data.data)
        logger.debug('System settings fetched successfully', {
          component: 'useSystemSettings',
          action: 'fetchSettings',
          metadata: { settingsCount: response.data.data.length },
        })
      } else {
        throw new Error('Failed to fetch system settings')
      }
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.error || err.message || 'Failed to fetch system settings'
      setError(errorMessage)
      logger.error('Error fetching system settings', new Error(errorMessage), {
        component: 'useSystemSettings',
        action: 'fetchSettings',
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Update a specific setting
  const updateSetting = async (key: string, value: any): Promise<boolean> => {
    try {
      setError(null)

      const response = await apiClient.put(`/system/settings/${key}`, { value })

      if (response.data.success) {
        // Update the setting in local state
        setSettings(prevSettings =>
          prevSettings.map(setting =>
            setting.key === key
              ? { ...setting, value, lastModifiedAt: response.data.data.lastModifiedAt }
              : setting
          )
        )

        logger.info(`System setting '${key}' updated successfully`, {
          component: 'useSystemSettings',
          action: 'updateSetting',
          metadata: { settingKey: key, newValue: value },
        })

        return true
      } else {
        throw new Error('Failed to update system setting')
      }
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.error || err.message || 'Failed to update system setting'
      setError(errorMessage)
      logger.error('Error updating system setting', new Error(errorMessage), {
        component: 'useSystemSettings',
        action: 'updateSetting',
        metadata: { settingKey: key },
      })
      return false
    }
  }

  // Toggle election status
  const toggleElection = async (): Promise<boolean> => {
    try {
      setError(null)

      const response = await apiClient.post('/system/toggle-election')

      if (response.data.success) {
        // Update the election setting in local state
        setSettings(prevSettings =>
          prevSettings.map(setting =>
            setting.key === 'voting_session_active'
              ? {
                  ...setting,
                  value: response.data.data.electionActive,
                  lastModifiedAt: response.data.data.lastModifiedAt,
                }
              : setting
          )
        )

        const action = response.data.data.electionActive ? 'opened' : 'closed'
        logger.info(`Election ${action} successfully`, {
          component: 'useSystemSettings',
          action: 'toggleElection',
          metadata: { electionActive: response.data.data.electionActive },
        })

        return true
      } else {
        throw new Error('Failed to toggle election status')
      }
    } catch (err: any) {
      const errorMessage =
        err.response?.data?.error || err.message || 'Failed to toggle election status'
      setError(errorMessage)
      logger.error('Error toggling election status', new Error(errorMessage), {
        component: 'useSystemSettings',
        action: 'toggleElection',
      })
      return false
    }
  }

  // Refresh settings
  const refreshSettings = async () => {
    await fetchSettings()
  }

  // Fetch settings on mount
  useEffect(() => {
    fetchSettings()
  }, [])

  return {
    settings,
    isLoading,
    error,
    getSetting,
    updateSetting,
    toggleElection,
    isElectionActive,
    refreshSettings,
  }
}

export const useElectionAccess = () => {
  const { isElectionActive, isLoading, error } = usePublicSystemSettings()

  return {
    canAccessVoting: isElectionActive,
    isLoading,
    error,
    electionStatus: isElectionActive ? 'active' : 'inactive',
  }
}

// Hook for public system settings (accessible by all users) - using React Query for caching
export const usePublicSystemSettings = () => {
  const { data, isLoading, error, refetch } = useQuery(
    ['system', 'public-settings'],
    async () => {
      const response = await apiClient.get('/system/public-settings')
      if (response.data.success) {
        return response.data.data
      }
      throw new Error('Failed to fetch public system settings')
    },
    {
      staleTime: 30000, // Consider data stale after 30 seconds
      cacheTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
      refetchInterval: 60000, // Refetch every minute
      retry: 2,
      onError: (err: any) => {
        const errorMessage = err.response?.data?.error || err.message || 'Failed to fetch public system settings'
        logger.error('Error fetching public system settings', new Error(errorMessage), {
          component: 'usePublicSystemSettings',
          action: 'fetchPublicSettings',
        })
      },
      onSuccess: (data) => {
        logger.debug('Public system settings fetched successfully', {
          component: 'usePublicSystemSettings',
          action: 'fetchPublicSettings',
          metadata: { settingsCount: Object.keys(data).length },
        })
      }
    }
  )

  const settings = data || {}
  const isElectionActive = settings.voting_session_active?.value || false

  return {
    settings,
    isLoading,
    error: error?.message || null,
    isElectionActive,
    refreshSettings: refetch,
  }
}
