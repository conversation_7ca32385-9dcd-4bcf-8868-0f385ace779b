import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/hooks/useAuth'
import { cn } from '@/lib/utils'
import { Bell, LogOut, Settings, User } from 'lucide-react'
import { ReactNode } from 'react'

export interface DashboardHeaderProps {
  title: string
  subtitle?: string
  actions?: ReactNode
  showUserInfo?: boolean
  showNotifications?: boolean
  className?: string
  onLogout?: () => void
  onNotificationClick?: () => void
  onSettingsClick?: () => void
  onProfileClick?: () => void
}

export function DashboardHeader({
  title,
  subtitle,
  actions,
  showUserInfo = true,
  showNotifications = true,
  className,
  onLogout,
  onNotificationClick,
  onSettingsClick,
  onProfileClick,
}: DashboardHeaderProps) {
  const { user, logout } = useAuth()

  const handleLogout = async () => {
    if (onLogout) {
      onLogout()
    } else {
      await logout(true)
    }
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin':
        return 'destructive'
      case 'execom':
        return 'default'
      case 'tie-breaker':
        return 'secondary'
      default:
        return 'outline'
    }
  }

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Administrator'
      case 'execom':
        return 'Executive Committee'
      case 'tie-breaker':
        return 'Tie-breaker'
      case 'voter':
        return 'Voter'
      default:
        return role
    }
  }

  return (
    <div className={cn('mb-8 flex items-center justify-between', className)}>
      {/* Title and Subtitle */}
      <div className="flex-1">
        <h1 className="font-heading text-3xl font-bold text-foreground">
          {title}
        </h1>
        {subtitle && (
          <p className="text-muted-foreground mt-2 text-base">
            {subtitle}
          </p>
        )}
      </div>

      {/* Actions and User Info */}
      <div className="flex items-center gap-3">
        {/* Custom Actions */}
        {actions}

        {/* Notifications */}
        {showNotifications && (
          <Button
            variant="outline"
            size="icon"
            onClick={onNotificationClick}
            className="relative"
          >
            <Bell className="h-4 w-4" />
            {/* Notification badge - you can make this dynamic */}
            <span className="absolute -top-1 -right-1 h-2 w-2 bg-destructive rounded-full" />
          </Button>
        )}

        {/* Settings */}
        {onSettingsClick && (
          <Button
            variant="outline"
            size="icon"
            onClick={onSettingsClick}
          >
            <Settings className="h-4 w-4" />
          </Button>
        )}

        {/* User Info */}
        {showUserInfo && user && (
          <div className="flex items-center gap-3">
            {/* User Profile Button */}
            {onProfileClick && (
              <Button
                variant="outline"
                size="icon"
                onClick={onProfileClick}
              >
                <User className="h-4 w-4" />
              </Button>
            )}

            {/* User Details */}
            <div className="text-right">
              <p className="text-sm font-medium text-foreground">
                {user.municipality}
              </p>
              <Badge
                variant={getRoleBadgeVariant(user.role)}
                className="text-xs"
              >
                {getRoleDisplayName(user.role)}
              </Badge>
            </div>
          </div>
        )}

        {/* Logout Button */}
        <Button
          variant="outline"
          onClick={handleLogout}
          className="flex items-center gap-2"
        >
          <LogOut className="h-4 w-4" />
          Logout
        </Button>
      </div>
    </div>
  )
}

export default DashboardHeader
