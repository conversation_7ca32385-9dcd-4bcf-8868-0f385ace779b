import { Alert, AlertDescription } from '@/components/ui/alert'
import { But<PERSON> } from '@/components/ui/button'
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { apiClient } from '@/services/apiClient'
import { logger } from '@/utils/logger'
import { zodResolver } from '@hookform/resolvers/zod'
import { AlertCircle, CheckCircle, Eye, EyeOff, Lock } from 'lucide-react'
import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import toast from 'react-hot-toast'
import { z } from 'zod'

const adminPasswordResetSchema = z
  .object({
    newPassword: z
      .string()
      .min(6, 'Password must be at least 6 characters')
      .max(128, 'Password cannot exceed 128 characters')
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
    confirmPassword: z.string(),
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  })

type AdminPasswordResetFormData = z.infer<typeof adminPasswordResetSchema>

interface User {
  id: string
  username: string
  municipality?: string
  role: string
}

interface AdminPasswordResetFormProps {
  user: User | null
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
}

export const AdminPasswordResetForm: React.FC<AdminPasswordResetFormProps> = ({
  user,
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)



  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<AdminPasswordResetFormData>({
    resolver: zodResolver(adminPasswordResetSchema),
  })

  const newPassword = watch('newPassword')

  const onSubmit = async (data: AdminPasswordResetFormData) => {
    logger.info('Password reset form submitted', {
      component: 'AdminPasswordResetForm',
      action: 'onSubmit',
      metadata: {
        user: user ? { id: user.id, username: user.username } : null,
        isOpen,
        hasUser: !!user,
        userId: user?.id,
      },
    })

    if (!user || !user.id) {
      const errorMessage = 'No user selected for password reset'
      setSubmitError(errorMessage)
      toast.error(errorMessage)
      logger.error('Password reset failed - no user selected', new Error(errorMessage), {
        component: 'AdminPasswordResetForm',
        action: 'resetPassword',
        metadata: { user, isOpen },
      })
      return
    }

    setIsSubmitting(true)
    setSubmitError(null)

    try {
      const response = await apiClient.put(`/auth/admin/reset-password/${user.id}`, {
        newPassword: data.newPassword,
      })

      if (response.data.success) {
        toast.success(`Password has been reset for ${user.username}.`)

        logger.info('Admin password reset successful', {
          component: 'AdminPasswordResetForm',
          action: 'resetPassword',
          metadata: { targetUserId: user.id, targetUsername: user.username },
        })

        reset()
        onSuccess?.()
        onClose()
      } else {
        throw new Error(response.data.error || 'Failed to reset password')
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || 'Failed to reset password'
      setSubmitError(errorMessage)

      toast.error(`Failed to reset password: ${errorMessage}`)

      logger.error('Error resetting password', new Error(errorMessage), {
        component: 'AdminPasswordResetForm',
        action: 'resetPassword',
        metadata: { targetUserId: user.id, targetUsername: user.username },
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    reset()
    setSubmitError(null)
    onClose()
  }

  const getPasswordStrength = (password: string) => {
    if (!password) return { strength: 0, label: '', color: '' }

    let strength = 0
    if (password.length >= 5) strength++
    if (password.length >= 8) strength++
    if (/[a-z]/.test(password)) strength++
    if (/[A-Z]/.test(password)) strength++
    if (/\d/.test(password)) strength++
    if (/[@$!%*?&]/.test(password)) strength++

    const levels = [
      { strength: 0, label: 'Very Weak', color: 'bg-red-500' },
      { strength: 1, label: 'Weak', color: 'bg-red-400' },
      { strength: 2, label: 'Fair', color: 'bg-yellow-500' },
      { strength: 3, label: 'Good', color: 'bg-yellow-400' },
      { strength: 4, label: 'Strong', color: 'bg-green-500' },
      { strength: 5, label: 'Very Strong', color: 'bg-green-600' },
      { strength: 6, label: 'Excellent', color: 'bg-green-700' },
    ]

    return levels[Math.min(strength, levels.length - 1)] || levels[0]
  }

  const passwordStrength = getPasswordStrength(newPassword || '')

  // Don't render if no user is provided
  if (!user) {
    logger.warn('AdminPasswordResetForm rendered without user', {
      component: 'AdminPasswordResetForm',
      action: 'render',
      metadata: { isOpen, user },
    })
    return null
  }



  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Lock className='h-5 w-5' />
            Reset Password
          </DialogTitle>
          <DialogDescription>
            Reset password for <strong>{user.username}</strong> ({user.municipality || user.role}).
            The user will need to change this password on their next login.
          </DialogDescription>
        </DialogHeader>

        <form
          onSubmit={e => {
            if (!user || !user.id) {
              e.preventDefault()
              const errorMessage = 'No user selected for password reset'
              setSubmitError(errorMessage)
              toast.error(errorMessage)
              logger.error(
                'Password reset form submission blocked - no user',
                new Error(errorMessage),
                {
                  component: 'AdminPasswordResetForm',
                  action: 'formSubmission',
                  metadata: { user, isOpen },
                }
              )
              return
            }
            handleSubmit(onSubmit)(e)
          }}
          className='space-y-4'
        >
          {submitError && (
            <Alert variant='destructive'>
              <AlertCircle className='h-4 w-4' />
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          {/* New Password */}
          <div className='space-y-2'>
            <Label htmlFor='newPassword'>New Password</Label>
            <div className='relative'>
              <Input
                id='newPassword'
                type={showNewPassword ? 'text' : 'password'}
                placeholder='Enter new password'
                {...register('newPassword')}
                className={errors.newPassword ? 'border-red-500' : ''}
              />
              <Button
                type='button'
                variant='ghost'
                size='sm'
                className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                onClick={() => setShowNewPassword(!showNewPassword)}
              >
                {showNewPassword ? <EyeOff className='h-4 w-4' /> : <Eye className='h-4 w-4' />}
              </Button>
            </div>
            {errors.newPassword && (
              <p className='text-sm text-red-500'>{errors.newPassword.message}</p>
            )}

            {/* Password Strength Indicator */}
            {newPassword && (
              <div className='space-y-2'>
                <div className='flex items-center gap-2'>
                  <div className='h-2 flex-1 rounded-full bg-gray-200'>
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${passwordStrength.color}`}
                      style={{ width: `${(passwordStrength.strength / 6) * 100}%` }}
                    />
                  </div>
                  <span className='text-xs font-medium'>{passwordStrength.label}</span>
                </div>
                <div className='text-xs text-gray-600'>
                  Password must be at least 5 characters and contain at least one number
                </div>
              </div>
            )}
          </div>

          {/* Confirm Password */}
          <div className='space-y-2'>
            <Label htmlFor='confirmPassword'>Confirm New Password</Label>
            <div className='relative'>
              <Input
                id='confirmPassword'
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder='Confirm new password'
                {...register('confirmPassword')}
                className={errors.confirmPassword ? 'border-red-500' : ''}
              />
              <Button
                type='button'
                variant='ghost'
                size='sm'
                className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? <EyeOff className='h-4 w-4' /> : <Eye className='h-4 w-4' />}
              </Button>
            </div>
            {errors.confirmPassword && (
              <p className='text-sm text-red-500'>{errors.confirmPassword.message}</p>
            )}
          </div>

          <DialogFooter>
            <Button type='button' variant='outline' onClick={handleClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button type='submit' disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <div className='mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white' />
                  Resetting Password...
                </>
              ) : (
                <>
                  <CheckCircle className='mr-2 h-4 w-4' />
                  Reset Password
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
