# DFPTA E-Voting System - Troubleshooting Guide

## CORS and Connection Issues

If you're experiencing CORS errors or connection issues, follow these steps:

### 1. Start Both Servers

Make sure both backend and frontend servers are running:

```bash
# Start both servers with one command
npm run dev

# Or start them separately:
# Terminal 1 - Backend
cd backend
npm run dev

# Terminal 2 - Frontend
cd frontend
npm run dev
```

### 2. Check Backend Status

The frontend now includes a **Backend Status Checker** on the login page that will show:

- 🟢 **Green**: Backend is healthy and accessible
- 🟡 **Yellow**: Backend is rate limiting requests
- 🔴 **Red**: Backend is not accessible (CORS or network issue)

### 3. Common Issues and Solutions

#### Issue: "CORS Missing Allow Origin"

**Solution**:

- Make sure the backend server is running on port 5000
- Check that the backend has CORS properly configured
- The frontend now uses a Vite proxy in development to avoid CORS issues

#### Issue: "Status code: 429 (Too Many Requests)"

**Solution**:

- The backend is rate limiting requests
- Wait a moment and try again
- Check backend rate limiting configuration

#### Issue: "Network Error" or "ERR_NETWORK"

**Solution**:

- Verify the backend server is running: `http://localhost:5000`
- Check if MongoDB is running and accessible
- Ensure all environment variables are set correctly

### 4. Backend Configuration

Make sure your backend has proper CORS configuration:

```javascript
// In backend/src/app.js
import cors from "cors";

app.use(
  cors({
    origin: ["http://localhost:3000", "http://localhost:5173"],
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
  })
);
```

### 5. Environment Variables

Check that your backend `.env` file has the correct configuration:

```env
PORT=5000
MONGODB_URI=mongodb://localhost:27017/dfpta
JWT_SECRET=your-secret-key
NODE_ENV=development
```

### 6. Database Connection

Ensure MongoDB is running:

```bash
# Start MongoDB (if using local installation)
mongod

# Or if using MongoDB Atlas, check your connection string
```

### 7. Development Proxy

The frontend is configured to use a Vite proxy in development:

- Frontend requests to `/api/*` are proxied to `http://localhost:5000/api/*`
- This avoids CORS issues in development
- In production, the frontend will use the full backend URL

### 8. Health Check Endpoint

The backend should have a health check endpoint at `/api/health`:

```javascript
// In backend/src/routes/public.js
router.get("/health", (req, res) => {
  res.json({
    success: true,
    message: "Backend is healthy",
    timestamp: new Date().toISOString(),
  });
});
```

### 9. Manual Testing

Test the backend directly:

```bash
# Test if backend is running
curl http://localhost:5000/api/health

# Test login endpoint
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"socmob123"}'
```

### 10. Reset Everything

If all else fails:

```bash
# Stop all servers (Ctrl+C)
# Clear node_modules and reinstall
npm run clean
npm run install:all

# Start fresh
npm run dev
```

## Still Having Issues?

1. Check the browser console for detailed error messages
2. Check the backend server logs for errors
3. Verify all dependencies are installed correctly
4. Ensure you're using Node.js version 18 or higher
5. Check if any antivirus or firewall is blocking the connections

## Support

If you continue to have issues, please check:

- Backend server logs for specific error messages
- Browser developer tools console for frontend errors
- Network tab in browser dev tools for failed requests
