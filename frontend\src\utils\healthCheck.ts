import { apiClient } from '@/services/apiClient'
import { logger } from './logger'

export interface HealthCheckResult {
  isHealthy: boolean
  status: number
  message: string
  corsEnabled: boolean
  responseTime: number
}

export const checkBackendHealth = async (): Promise<HealthCheckResult> => {
  const startTime = Date.now()

  try {
    // Try to access a simple endpoint
    const response = await apiClient.get('/health', {
      timeout: 5000,
    })

    const responseTime = Date.now() - startTime

    logger.info('Backend health check successful', {
      component: 'HealthCheck',
      action: 'checkBackendHealth',
      metadata: {
        status: response.status,
        responseTime,
        url: response.config.url,
      },
    })

    return {
      isHealthy: true,
      status: response.status,
      message: 'Backend is healthy and accessible',
      corsEnabled: true,
      responseTime,
    }
  } catch (error: any) {
    const responseTime = Date.now() - startTime
    const status = error.response?.status
    const message = error.response?.data?.message || error.message || 'Backend is not accessible'

    logger.error('Backend health check failed', error, {
      component: 'HealthCheck',
      action: 'checkBackendHealth',
      metadata: {
        status,
        responseTime,
        message,
      },
    })

    return {
      isHealthy: false,
      status: status || 0,
      message,
      corsEnabled: status !== 0, // If we get a status code, CORS is likely enabled
      responseTime,
    }
  }
}

export const getBackendStatus = async (): Promise<string> => {
  try {
    const health = await checkBackendHealth()

    if (health.isHealthy) {
      return '🟢 Backend is healthy'
    } else if (health.status === 429) {
      return '🟡 Backend is rate limiting requests'
    } else if (health.status === 0) {
      return '🔴 Backend is not accessible (CORS or network issue)'
    } else {
      return `🔴 Backend error: ${health.status} - ${health.message}`
    }
  } catch (error) {
    return '🔴 Backend health check failed'
  }
}
