import mongoose from 'mongoose'

const candidateSchema = new mongoose.Schema(
  {
    municipalityName: {
      type: String,
      required: [true, 'Municipality name is required'],
      trim: true,
      unique: true,
      index: true,
    },
    district: {
      type: String,
      required: [true, 'District is required'],
      enum: {
        values: ['1st District', '2nd District', '3rd District', '4th District', '5th District'],
        message:
          'Invalid district. Must be one of: 1st District, 2nd District, 3rd District, 4th District, 5th District',
      },
      index: true,
    },
    totalVotes: {
      type: Number,
      default: 0,
      min: [0, 'Total votes cannot be negative'],
    },
    isActive: {
      type: Boolean,
      default: true,
      index: true,
    },
    // Voting statistics (simplified for single round)
    // Rankings and positions
    currentRank: {
      type: Number,
      min: 1,
    },
    finalPosition: {
      type: Number,
      min: 1,
    },
    isWinner: {
      type: Boolean,
      default: false,
    },
    isEliminated: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (_doc, ret) {
        delete ret.__v
        return ret
      },
    },
  }
)

// Indexes for performance
candidateSchema.index({ totalVotes: -1 })
candidateSchema.index({ currentRank: 1 })

// Compound indexes
candidateSchema.index({ isActive: 1, totalVotes: -1 })
candidateSchema.index({ district: 1, totalVotes: -1 })
candidateSchema.index({ municipalityName: 1, isActive: 1 })

// Virtual for vote percentage
candidateSchema.virtual('votePercentage').get(function () {
  // This will be calculated based on total votes cast
  return 0 // Placeholder - will be calculated in aggregation
})

// Instance method to add votes (simplified for single round)
candidateSchema.methods.addVote = function (voteCount = 1) {
  this.totalVotes += voteCount
  return this.save()
}

// Instance method to get votes for a specific round
candidateSchema.methods.getVotesForRound = function (round, voteType = 'regular') {
  const roundVotes = this.votesByRound.find(v => v.round === round && v.voteType === voteType)
  return roundVotes ? roundVotes.votes : 0
}

// Static method to get top candidates
candidateSchema.statics.getTopCandidates = function (limit = 15) {
  return this.find({ isActive: true }).sort({ totalVotes: -1, createdAt: 1 }).limit(limit)
}



// Static method to get candidates by district
candidateSchema.statics.getCandidatesByDistrict = function () {
  return this.aggregate([
    { $match: { isActive: true } },
    {
      $group: {
        _id: '$district',
        candidates: { $push: '$$ROOT' },
        count: { $sum: 1 },
        totalVotes: { $sum: '$totalVotes' },
      },
    },
    { $sort: { totalVotes: -1 } },
  ])
}

// Static method to get all active municipalities as candidates
candidateSchema.statics.getAllMunicipalities = function () {
  return this.find({ isActive: true })
    .select('municipalityName district totalVotes currentRank isActive')
    .sort({ totalVotes: -1, municipalityName: 1 })
}

// Static method to reset all vote counts (for testing/admin purposes)
candidateSchema.statics.resetAllVotes = function () {
  return this.updateMany(
    {},
    {
      $set: {
        totalVotes: 0,
        currentRank: null,
        finalPosition: null,
        isWinner: false,
        isEliminated: false,
      },
    }
  )
}

// Pre-save middleware to update rank
candidateSchema.pre('save', function (next) {
  // Rank will be calculated in a separate process
  next()
})

const Candidate = mongoose.model('Candidate', candidateSchema)

export default Candidate
