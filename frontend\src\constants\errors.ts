// Centralized error messages and types
export const ERROR_MESSAGES = {
  // Network and Connection Errors
  NETWORK_ERROR: 'Unable to connect to the server. Please check your internet connection.',
  CORS_ERROR: 'Server connection error. Please contact your administrator.',
  RATE_LIMITED: 'Too many requests. Please wait a moment and try again.',
  SERVER_ERROR: 'Server error occurred. Please try again later.',
  
  // Authentication Errors
  INVALID_CREDENTIALS: 'Invalid username or password',
  ACCOUNT_LOCKED: 'Account temporarily locked due to too many failed login attempts. Please try again later.',
  TOKEN_EXPIRED: 'Your session has expired. Please log in again.',
  UNAUTHORIZED: 'You are not authorized to access this resource.',
  
  // User Management Errors
  USER_NOT_FOUND: 'User not found',
  USER_CREATE_FAILED: 'Failed to create user',
  USER_UPDATE_FAILED: 'Failed to update user',
  USER_DELETE_FAILED: 'Failed to delete user',
  
  // Voting Errors
  VOTING_DISABLED: 'Voting is currently disabled by the administrator.',
  ALREADY_VOTED: 'You have already cast your vote.',
  INVALID_CANDIDATES: 'Invalid candidate selection',
  VOTE_SUBMISSION_FAILED: 'Failed to submit your vote. Please try again.',
  
  // System Errors
  SETTINGS_LOAD_FAILED: 'Failed to load system settings',
  SETTINGS_UPDATE_FAILED: 'Failed to update system settings',
  
  // Password Errors
  PASSWORD_CHANGE_FAILED: 'Failed to change password',
  PASSWORD_RESET_FAILED: 'Failed to reset password',
  CURRENT_PASSWORD_INCORRECT: 'Current password is incorrect',
  
  // Validation Errors
  VALIDATION_FAILED: 'Please check your input and try again',
  REQUIRED_FIELD: 'This field is required',
  
  // Generic Errors
  UNEXPECTED_ERROR: 'An unexpected error occurred. Please try again.',
  LOADING_ERROR: 'Failed to load data. Please refresh the page.',
} as const

export const ERROR_TYPES = {
  NETWORK: 'NETWORK',
  AUTH: 'AUTH',
  VALIDATION: 'VALIDATION',
  SERVER: 'SERVER',
  USER: 'USER',
} as const

export type ErrorType = keyof typeof ERROR_TYPES
export type ErrorMessage = keyof typeof ERROR_MESSAGES

// Error severity levels
export const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
} as const

export type ErrorSeverity = typeof ERROR_SEVERITY[keyof typeof ERROR_SEVERITY]

// Error interface
export interface AppError {
  type: ErrorType
  message: string
  severity: ErrorSeverity
  code?: string
  details?: any
}

// Helper function to create standardized errors
export const createError = (
  type: ErrorType,
  message: string,
  severity: ErrorSeverity = ERROR_SEVERITY.MEDIUM,
  code?: string,
  details?: any
): AppError => ({
  type,
  message,
  severity,
  code,
  details,
})

// Common error creators
export const createNetworkError = (message: string = ERROR_MESSAGES.NETWORK_ERROR) =>
  createError(ERROR_TYPES.NETWORK, message, ERROR_SEVERITY.HIGH)

export const createAuthError = (message: string = ERROR_MESSAGES.UNAUTHORIZED) =>
  createError(ERROR_TYPES.AUTH, message, ERROR_SEVERITY.HIGH)

export const createValidationError = (message: string = ERROR_MESSAGES.VALIDATION_FAILED) =>
  createError(ERROR_TYPES.VALIDATION, message, ERROR_SEVERITY.MEDIUM)

export const createServerError = (message: string = ERROR_MESSAGES.SERVER_ERROR) =>
  createError(ERROR_TYPES.SERVER, message, ERROR_SEVERITY.CRITICAL)
