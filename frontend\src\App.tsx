import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { Toaster } from 'react-hot-toast'
import { Route, Routes } from 'react-router-dom'
import { ErrorBoundary } from './components/ErrorBoundary'
import { ThemeProvider } from './components/theme-provider'
import { AuthProvider } from './hooks/useAuth'

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime)
      refetchOnWindowFocus: false,
    },
  },
})

// Pages
import { AdminDashboard } from './pages/admin/AdminDashboard'
// ExecomDashboard removed - feature no longer supported
import HomePage from './pages/HomePage'
import LoginPage from './pages/LoginPage'
import NotFoundPage from './pages/NotFoundPage'
import ResultsPage from './pages/ResultsPage'
import VotingPage from './pages/VotingPage'

// Components
import Layout from './components/Layout'
import ProtectedRoute from './components/ProtectedRoute'

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider defaultTheme='light' storageKey='dfpta-ui-theme'>
          <AuthProvider>
            <Layout>
            <Routes>
              {/* Public routes */}
              <Route path='/' element={<HomePage />} />
              <Route path='/login' element={<LoginPage />} />
              <Route path='/results' element={<ResultsPage />} />

              {/* Protected routes */}
              <Route
                path='/vote'
                element={
                  <ProtectedRoute allowedRoles={['voter']}>
                    <VotingPage />
                  </ProtectedRoute>
                }
              />

              <Route
                path='/admin/*'
                element={
                  <ProtectedRoute allowedRoles={['admin']}>
                    <AdminDashboard />
                  </ProtectedRoute>
                }
              />

              {/* ExecomDashboard route removed - feature no longer supported */}

              {/* 404 page */}
              <Route path='*' element={<NotFoundPage />} />
            </Routes>
          </Layout>
          <Toaster
            position='top-right'
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
            }}
          />
          {/* React Query DevTools - only in development */}
          {process.env.NODE_ENV === 'development' && (
            <ReactQueryDevtools initialIsOpen={false} />
          )}
        </AuthProvider>
      </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  )
}

export default App
