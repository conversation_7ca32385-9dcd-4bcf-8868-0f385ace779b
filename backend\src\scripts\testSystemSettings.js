import dotenv from 'dotenv'
import mongoose from 'mongoose'
import SystemSettings from '../models/SystemSettings.js'
import logger from '../utils/logger.js'

// Load environment variables
dotenv.config({ path: './backend/.env' })

/**
 * Test system settings functionality
 */
const testSystemSettings = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI)
    logger.info('Connected to MongoDB for system settings test')

    // Check if municipality names setting exists
    const municipalityNamesSetting = await SystemSettings.findOne({ key: 'show_municipality_names' })

    if (!municipalityNamesSetting) {
      logger.error('❌ Municipality names setting not found in database')

      // Create the setting
      const newSetting = new SystemSettings({
        key: 'show_municipality_names',
        value: false,
        type: 'boolean',
        description: 'Show municipality names in public results',
        category: 'display',
        isEditable: true,
        requiresRestart: false,
        defaultValue: false,
      })

      await newSetting.save()
      logger.info('✅ Created municipality names setting')
    } else {
      logger.info('✅ Municipality names setting exists:', {
        key: municipalityNamesSetting.key,
        value: municipalityNamesSetting.value,
        isEditable: municipalityNamesSetting.isEditable
      })
    }

    // Check if district results setting exists
    const districtResultsSetting = await SystemSettings.findOne({ key: 'show_district_results' })

    if (!districtResultsSetting) {
      logger.error('❌ District results setting not found in database')

      // Create the setting
      const newSetting = new SystemSettings({
        key: 'show_district_results',
        value: true,
        type: 'boolean',
        description: 'Show district-based results section in public results',
        category: 'display',
        isEditable: true,
        requiresRestart: false,
        defaultValue: true,
      })

      await newSetting.save()
      logger.info('✅ Created district results setting')
    } else {
      logger.info('✅ District results setting exists:', {
        key: districtResultsSetting.key,
        value: districtResultsSetting.value,
        isEditable: districtResultsSetting.isEditable
      })
    }

    // Test updating municipality names setting
    try {
      const setting = await SystemSettings.findOne({ key: 'show_municipality_names' })
      const originalValue = setting.value

      // Toggle the value
      setting.value = !originalValue
      await setting.save()

      logger.info('✅ Successfully updated municipality names setting:', {
        from: originalValue,
        to: setting.value
      })

      // Restore original value
      setting.value = originalValue
      await setting.save()

      logger.info('✅ Restored original municipality names setting value')

    } catch (error) {
      logger.error('❌ Failed to update municipality names setting:', error)
    }

    // Test updating district results setting
    try {
      const setting = await SystemSettings.findOne({ key: 'show_district_results' })
      const originalValue = setting.value

      // Toggle the value
      setting.value = !originalValue
      await setting.save()

      logger.info('✅ Successfully updated district results setting:', {
        from: originalValue,
        to: setting.value
      })

      // Restore original value
      setting.value = originalValue
      await setting.save()

      logger.info('✅ Restored original district results setting value')

    } catch (error) {
      logger.error('❌ Failed to update district results setting:', error)
    }

    logger.info('🎉 System settings test completed successfully!')

  } catch (error) {
    logger.error('💥 System settings test failed:', error)
  } finally {
    await mongoose.disconnect()
    process.exit(0)
  }
}

// Run test
testSystemSettings()
