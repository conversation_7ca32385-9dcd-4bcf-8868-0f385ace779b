import { VoteTrackingCards } from '@/components/admin/VoteTrackingCards'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { usePublicResults } from '@/hooks/useVoting'
import { AlertCircle, Award, BarChart3, CheckCircle, Clock, Medal, Trophy } from 'lucide-react'

// Component to display results with municipality names (4 columns)
const PublicResultsWithNames = ({ results }: { results: any }) => {
  const { unifiedResults } = results

  const getRankIcon = (rank: number) => {
    if (rank === 1) return <Trophy className="h-4 w-4 text-yellow-500" />
    if (rank === 2) return <Medal className="h-4 w-4 text-gray-400" />
    if (rank === 3) return <Award className="h-4 w-4 text-amber-600" />
    return null
  }

  const getRankBadge = (rank: number) => {
    if (rank <= 3) {
      const colors = ['bg-yellow-100 text-yellow-800', 'bg-gray-100 text-gray-800', 'bg-amber-100 text-amber-800']
      return <Badge className={`text-xs ${colors[rank - 1]}`}>#{rank}</Badge>
    }
    return <Badge variant="outline" className="text-xs">#{rank}</Badge>
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-20">Rank</TableHead>
            <TableHead>Municipality</TableHead>
            <TableHead>District</TableHead>
            <TableHead className="text-right w-24">Votes</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {unifiedResults.map((candidate: any) => {
            const isInactive = candidate.voteCount === 0

            return (
              <TableRow
                key={`${candidate.district}-${candidate.municipalityName}`}
                className={isInactive ? 'opacity-60 bg-muted/30' : ''}
              >
                <TableCell>
                  <div className="flex items-center gap-2">
                    {getRankIcon(candidate.rank)}
                    {getRankBadge(candidate.rank)}
                  </div>
                </TableCell>
                <TableCell className="font-medium">
                  {candidate.municipalityName}
                </TableCell>
                <TableCell className="text-muted-foreground">
                  {candidate.district}
                </TableCell>
                <TableCell className="text-right font-semibold">
                  {candidate.voteCount.toLocaleString()}
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
  )
}

// Component to display anonymous results
const PublicResultsAnonymous = ({ results }: { results: any }) => {
  const { unifiedResults } = results

  const getRankIcon = (rank: number) => {
    if (rank === 1) return <Trophy className="h-4 w-4 text-yellow-500" />
    if (rank === 2) return <Medal className="h-4 w-4 text-gray-400" />
    if (rank === 3) return <Award className="h-4 w-4 text-amber-600" />
    return null
  }

  const getRankBadge = (rank: number) => {
    if (rank <= 3) {
      const colors = ['bg-yellow-100 text-yellow-800', 'bg-gray-100 text-gray-800', 'bg-amber-100 text-amber-800']
      return <Badge className={`text-xs ${colors[rank - 1]}`}>#{rank}</Badge>
    }
    return <Badge variant="outline" className="text-xs">#{rank}</Badge>
  }

  // Split results into 7 columns with 5 candidates each
  const columns = []
  for (let i = 0; i < 7; i++) {
    const startIndex = i * 5
    const endIndex = startIndex + 5
    columns.push(unifiedResults.slice(startIndex, endIndex))
  }

  return (
    <div className="overflow-x-auto">
      <div className="grid grid-cols-7 gap-4 min-w-[1200px]">
        {columns.map((columnCandidates, columnIndex) => (
          <div key={columnIndex} className="space-y-2">
            <h4 className="text-sm font-semibold text-center border-b pb-2">
              Ranks {columnIndex * 5 + 1}–{Math.min(columnIndex * 5 + 5, 35)}
            </h4>
            <div className="space-y-1">
              {columnCandidates.map((candidate: any) => {
                const isInactive = candidate.voteCount === 0

                return (
                  <div
                    key={`candidate-${candidate.rank}`}
                    className={`p-2 rounded border text-center text-xs ${
                      isInactive ? 'opacity-60 bg-muted/30' : 'bg-white hover:bg-muted/20'
                    }`}
                  >
                    <div className="flex items-center justify-center gap-1 mb-1">
                      {getRankIcon(candidate.rank)}
                      {getRankBadge(candidate.rank)}
                    </div>
                    <div className="text-muted-foreground mb-1">
                      {candidate.district}
                    </div>
                    <div className="font-semibold">
                      {candidate.voteCount.toLocaleString()}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

const ResultsPage = () => {
  const { data: resultsData, isLoading, error, lastUpdated } = usePublicResults()

  if (isLoading) {
    return (
      <div className='min-h-screen bg-gray-50 py-8'>
        <div className='mx-auto max-w-6xl px-4 sm:px-6 lg:px-8'>
          <div className='flex items-center justify-center'>
            <div className='text-center'>
              <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4'></div>
              <p className='text-gray-600'>Loading results...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !resultsData?.success) {
    return (
      <div className='min-h-screen bg-gray-50 py-8'>
        <div className='mx-auto max-w-6xl px-4 sm:px-6 lg:px-8'>
          <Card className='border-red-200 bg-red-50'>
            <CardContent className='pt-6'>
              <div className='flex items-center gap-2 text-red-800'>
                <AlertCircle className='h-5 w-5' />
                <p className='font-medium'>Error loading results</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  const { available, message, statusMessage, statistics } = resultsData.data

  return (
    <div className='min-h-screen bg-gray-50 py-8'>
      <div className='mx-auto max-w-6xl px-4 sm:px-6 lg:px-8'>
        {/* Header */}
        <div className='mb-8 text-center'>
          <h1 className='mb-4 text-4xl font-bold text-gray-900'>DFPTA Election Results</h1>
          <p className='mt-2 text-gray-600'>SDO Camarines Sur <br></br> SY 2025-2026</p>

          {/* Last Updated Timestamp */}
          {lastUpdated && (
            <div className='mt-3 flex items-center justify-center gap-2 text-sm text-gray-500'>
              <Clock className='h-4 w-4' />
              <span>Last updated on {lastUpdated.toLocaleString()}</span>
            </div>
          )}

          {/* Status Badge */}
          <div className='mt-4 flex justify-center'>
            <Badge
              variant={available ? 'default' : 'secondary'}
              className='flex items-center gap-2 px-4 py-2 text-sm'
            >
              {available ? (
                <>
                  <CheckCircle className='h-4 w-4' />
                  {message}
                </>
              ) : (
                <>{/*
                  <Clock className='h-4 w-4' />
                  {message} */}
                </>
              )}
            </Badge>
          </div>

          {/*
          <p className='mt-2 text-sm text-gray-600'>{statusMessage}</p>
          */}
        </div>

        {/* Voting Progress - Using same component as admin dashboard */}
        {available && statistics && (
          <div className='mb-8'>
            <h2 className='mb-4 text-2xl font-semibold'>Voting Progress & Statistics</h2>
            <VoteTrackingCards
              stats={{
                totalVotes: statistics.totalVotesCast, // Actual number of votes cast by users
                totalVoters: statistics.totalRegisteredVoters, // Total registered voters (35)
                totalVotesCast: statistics.totalVotesCast, // Number of users who have voted
                participationRate: statistics.participationRate, // (users who voted / total registered) × 100%
                totalCandidates: statistics.totalCandidates,
                totalActiveVoters: statistics.totalActiveCandidates, // Active users only
                totalAbsentUsers: statistics.totalAbsentParticipants // Inactive users from User Management
              }}
              isLoading={false}
            />
          </div>
        )}



        {/* Results not available message */}
        {!available && (
          <Card className='mb-8 border-amber-200 bg-amber-50'>
            <CardContent className='pt-6'>
              <div className='text-center'>
                <Clock className='h-12 w-12 text-amber-600 mx-auto mb-4' />
                <h3 className='text-lg font-semibold text-amber-800 mb-2'>Results Not Available</h3>
                <p className='text-amber-700'>
                  Election results are not currently available to the public. Please check back later or contact your administrator for more information.
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Top 35 Results when available */}
        {available && resultsData.data.results && (
          <Card className='mb-8'>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <BarChart3 className='h-5 w-5' />
                <span>Top 35 Candidates</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {resultsData.data.results.showMunicipalityNames ? (
                <PublicResultsWithNames results={resultsData.data.results} />
              ) : (
                <PublicResultsAnonymous results={resultsData.data.results} />
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

export default ResultsPage
