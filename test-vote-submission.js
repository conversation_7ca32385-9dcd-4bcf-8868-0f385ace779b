// Test script to verify vote submission functionality
import axios from 'axios';

const API_BASE_URL = 'http://localhost:5000/api';

async function testVoteSubmission() {
  console.log('🧪 Testing Vote Submission Functionality...\n');

  try {
    // Step 1: Login as a voter
    console.log('1. 🔐 Logging in as voter...');
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'bala<PERSON>',
      password: 'bala#767'
    });

    if (!loginResponse.data.success) {
      throw new Error('Login failed');
    }

    const token = loginResponse.data.data.token;
    const user = loginResponse.data.data.user;
    console.log(`   ✅ Successfully logged in as ${user.username} (${user.municipality})`);

    // Step 2: Get voting status
    console.log('\n2. 📊 Checking voting status...');
    const statusResponse = await axios.get(`${API_BASE_URL}/voting/status`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    console.log(`   ✅ Voting status: ${statusResponse.data.data.canVote ? 'Can vote' : 'Cannot vote'}`);
    console.log(`   📝 Status: ${statusResponse.data.data.votingStatus}`);

    if (!statusResponse.data.data.canVote) {
      console.log('   ⚠️  User cannot vote, skipping vote submission test');
      return;
    }

    // Step 3: Get candidates
    console.log('\n3. 👥 Fetching candidates...');
    const candidatesResponse = await axios.get(`${API_BASE_URL}/candidates`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    const candidates = candidatesResponse.data.data.candidates;
    console.log(`   ✅ Found ${candidates.length} candidates`);

    // Step 4: Select some candidates for voting (first 5 active candidates)
    const activeCandidates = candidates.filter(c => c.isActive && !c.isDisabled);
    const selectedCandidates = activeCandidates.slice(0, 5).map(c => c._id);

    console.log('\n4. 🗳️  Preparing to submit votes...');
    console.log(`   📋 Selected ${selectedCandidates.length} candidates:`);
    activeCandidates.slice(0, 5).forEach(c => {
      console.log(`      - ${c.municipalityName} (${c.district})`);
    });

    // Step 5: Submit votes
    console.log('\n5. 📤 Submitting votes...');
    const voteResponse = await axios.post(`${API_BASE_URL}/voting/submit`, {
      candidateIds: selectedCandidates
    }, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    if (voteResponse.data.success) {
      console.log('   ✅ Vote submission successful!');
      console.log(`   📊 Votes submitted: ${voteResponse.data.data.votesCount}`);
      console.log(`   🆔 Batch ID: ${voteResponse.data.data.batchId}`);
      console.log(`   ⏰ Submitted at: ${voteResponse.data.data.submittedAt}`);
    } else {
      throw new Error('Vote submission failed');
    }

    // Step 6: Verify voting status after submission
    console.log('\n6. 🔍 Verifying post-submission status...');
    const postStatusResponse = await axios.get(`${API_BASE_URL}/voting/status`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    console.log(`   ✅ Post-submission status: ${postStatusResponse.data.data.votingStatus}`);
    console.log(`   📝 Can vote again: ${postStatusResponse.data.data.canVote ? 'Yes' : 'No'}`);

    console.log('\n🎉 Vote submission test completed successfully!');
    console.log('✅ All functionality working correctly - no JavaScript errors detected');

  } catch (error) {
    console.error('\n❌ Vote submission test failed:');
    console.error(`   Error: ${error.message}`);

    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }

    console.log('\n🔧 This indicates there may be issues with the vote submission functionality');
  }
}

// Run the test
testVoteSubmission();
