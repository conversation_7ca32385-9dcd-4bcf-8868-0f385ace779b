import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { logger } from '@/utils/logger'
import { AlertTriangle, Map, MapPin, Settings } from 'lucide-react'
import { useState } from 'react'

export interface DistrictResultsControlProps {
  showDistrictResults: boolean
  onToggleDistrictResults: (show: boolean) => Promise<{ success: boolean; error?: string } | void>
  isLoading?: boolean
}

export function DistrictResultsControl({
  showDistrictResults,
  onToggleDistrictResults,
  isLoading = false
}: DistrictResultsControlProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [pendingState, setPendingState] = useState<boolean | null>(null)

  const handleToggleClick = (checked: boolean) => {
    setPendingState(checked)
    setShowConfirmDialog(true)
  }

  const handleConfirm = async () => {
    if (pendingState !== null) {
      const result = await onToggleDistrictResults(pendingState)

      logger.info('Admin toggled district results visibility', {
        metadata: {
          newState: pendingState ? 'visible' : 'hidden',
          timestamp: new Date().toISOString(),
          success: result?.success || false
        }
      })
    }
    setShowConfirmDialog(false)
    setPendingState(null)
  }

  const handleCancel = () => {
    setShowConfirmDialog(false)
    setPendingState(null)
  }

  return (
    <Card className="border-l-4 border-l-purple-500">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Settings className="h-5 w-5 text-purple-500" />
            <CardTitle className="text-lg">District Results Display</CardTitle>
          </div>
          <Badge
            variant={showDistrictResults ? "default" : "secondary"}
            className="flex items-center gap-1"
          >
            {showDistrictResults ? (
              <>
                <Map className="h-3 w-3" />
                Visible
              </>
            ) : (
              <>
                <MapPin className="h-3 w-3" />
                Hidden
              </>
            )}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor="district-results" className="text-sm font-medium">
              Show "Results by District" Section in Public Results
            </Label>
            <p className="text-xs text-muted-foreground">
              Control whether district-based groupings are shown in addition to unified top 35 results
            </p>
          </div>

          <Switch
            id="district-results"
            checked={showDistrictResults}
            onCheckedChange={handleToggleClick}
            disabled={isLoading}
          />
        </div>

        <div className="rounded-lg bg-muted/50 p-3">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0" />
            <div className="text-xs text-muted-foreground">
              <p className="font-medium text-foreground mb-1">Display Options:</p>
              <ul className="space-y-1">
                <li>• When <strong>enabled</strong>: Shows both "Top 35 Candidates" and "Results by District" sections</li>
                <li>• When <strong>disabled</strong>: Shows only "Top 35 Candidates" unified ranking</li>
                <li>• This setting is independent of municipality names visibility</li>
                <li>• Changes take effect immediately on the public results page</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="text-xs text-muted-foreground">
          <p>
            <strong>Current Status:</strong> District-based results are{' '}
            <span className={showDistrictResults ? 'text-green-600 font-medium' : 'text-red-600 font-medium'}>
              {showDistrictResults ? 'visible to public' : 'hidden from public'}
            </span>
          </p>
        </div>

        <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {pendingState ? 'Show' : 'Hide'} District Results in Public Results?
              </AlertDialogTitle>
              <AlertDialogDescription>
                {pendingState ? (
                  <>
                    This will show the <strong>"Results by District"</strong> section in public results.
                    Users will see both the unified top 35 ranking and district-based groupings.
                  </>
                ) : (
                  <>
                    This will hide the <strong>"Results by District"</strong> section from public results.
                    Users will only see the unified top 35 ranking without district groupings.
                  </>
                )}
                <br /><br />
                This change will take effect immediately on the public results page. Are you sure you want to continue?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={handleCancel}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleConfirm}
                className={pendingState ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
              >
                {pendingState ? 'Show District Results' : 'Hide District Results'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  )
}
