import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { AlertCircle, Lock, LogIn, User } from 'lucide-react'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'react-hot-toast'
import { useLocation, useNavigate } from 'react-router-dom'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/hooks/useAuth'
import { cn } from '@/lib/utils'
import { LoginFormData, loginSchema } from '@/schemas/auth'
import { Button } from '../../atoms/Button'
import { FormField } from '../../molecules/FormField'

export interface LoginFormProps {
  className?: string
  onSuccess?: () => void
  showForgotPassword?: boolean
}

export function LoginForm({ className, onSuccess, showForgotPassword = true }: LoginFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [loginError, setLoginError] = useState<string | null>(null)

  const { login } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
    mode: 'onBlur',
  })

  const {
    control,
    handleSubmit,
    formState: { isValid },
  } = form

  const onSubmit = async (data: LoginFormData) => {
    try {
      setIsLoading(true)
      setLoginError(null)

      const success = await login(data)

      if (success) {
        onSuccess?.()

        // Redirect to intended page or role-specific dashboard
        const from = location.state?.from?.pathname || '/'
        navigate(from, { replace: true })
      } else {
        setLoginError('Invalid username or password')
      }
    } catch (error: any) {
      console.error('Login error:', error)

      // Handle specific error types
      if (error.message.includes('locked')) {
        setLoginError('Account temporarily locked due to too many failed attempts')
      } else if (error.message.includes('Invalid')) {
        setLoginError('Invalid username or password')
      } else {
        setLoginError('Login failed. Please try again.')
      }

      toast.error(error.message || 'Login failed')
    } finally {
      setIsLoading(false)
    }
  }

  const handleForgotPassword = () => {
    // TODO: Implement forgot password functionality
    toast('Forgot password feature coming soon', { icon: 'ℹ️' })
  }

  return (
    <Card className={cn('mx-auto w-full max-w-md', className)}>
      <CardHeader className='space-y-1 text-center'>
        <CardTitle className='text-2xl font-bold'>Welcome Back</CardTitle>
        <p className='text-sm text-muted-foreground'>Sign in to your DFPTA account</p>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
          {/* Global error message */}
          {loginError && (
            <div className='flex items-center gap-2 rounded-md border border-destructive/20 bg-destructive/10 p-3 text-sm text-destructive'>
              <AlertCircle className='h-4 w-4' />
              <span>{loginError}</span>
            </div>
          )}

          {/* Username field */}
          <FormField
            name='username'
            control={control}
            label='Username'
            placeholder='Enter your username'
            leftIcon={<User className='h-4 w-4' />}
            autoComplete='username'
            autoFocus
            disabled={isLoading}
          />

          {/* Password field */}
          <FormField
            name='password'
            control={control}
            type='password'
            label='Password'
            placeholder='Enter your password'
            leftIcon={<Lock className='h-4 w-4' />}
            showPasswordToggle
            autoComplete='current-password'
            disabled={isLoading}
          />

          {/* Forgot password link */}
          {showForgotPassword && (
            <div className='text-right'>
              <button
                type='button'
                onClick={handleForgotPassword}
                className='text-sm text-primary hover:underline focus:underline focus:outline-none'
                disabled={isLoading}
              >
                Forgot password?
              </button>
            </div>
          )}

          {/* Submit button */}
          <Button
            type='submit'
            fullWidth
            size='lg'
            loading={isLoading}
            disabled={!isValid || isLoading}
            leftIcon={!isLoading ? <LogIn className='h-4 w-4' /> : undefined}
          >
            {isLoading ? 'Signing in...' : 'Sign In'}
          </Button>
        </form>

        {/* Additional info */}
        <div className='mt-6 text-center'>
          <p className='text-xs text-muted-foreground'>DFPTA E-Voting System v2.0</p>
          <p className='mt-1 text-xs text-muted-foreground'>
            Social Mobilization and Networking Unit - SDO Camarines Sur
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

export default LoginForm
