import { zodResolver } from '@hookform/resolvers/zod'
import { AlertCircle, CheckCircle, Eye, EyeOff, Loader2, Lock, LogIn, Shield, User } from 'lucide-react'
import { useCallback, useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'react-hot-toast'
import { useLocation, useNavigate } from 'react-router-dom'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useAuth } from '@/hooks/useAuth'
import { cn } from '@/lib/utils'
import { LoginFormData, loginSchema } from '@/schemas/auth'

export interface LoginFormProps {
  className?: string
  onSuccess?: () => void
  showForgotPassword?: boolean
}

export function LoginForm({ className, onSuccess, showForgotPassword = true }: LoginFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [loginError, setLoginError] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [loginAttempts, setLoginAttempts] = useState(0)

  const { login } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
    mode: 'onBlur',
  })

  const {
    register,
    handleSubmit,
    formState: { errors, isValid, touchedFields },
    watch,
  } = form

  const watchedUsername = watch('username')
  const watchedPassword = watch('password')

  const togglePasswordVisibility = useCallback(() => {
    setShowPassword(prev => !prev)
  }, [])

  const onSubmit = async (data: LoginFormData) => {
    try {
      setIsLoading(true)
      setLoginError(null)
      setLoginAttempts(prev => prev + 1)

      const success = await login(data)

      if (success) {
        // Enhanced success feedback
        toast.success(
          '🎉 Login Successful!\nWelcome to DFPTA E-Voting System',
          {
            duration: 3000,
            style: {
              background: '#10b981',
              color: '#ffffff',
              fontWeight: '500',
              borderRadius: '8px',
              boxShadow: '0 10px 25px rgba(16, 185, 129, 0.3)',
            },
            iconTheme: {
              primary: '#ffffff',
              secondary: '#10b981',
            },
          }
        )

        onSuccess?.()

        // Small delay for better UX before redirect
        setTimeout(() => {
          const from = location.state?.from?.pathname || '/'
          navigate(from, { replace: true })
        }, 1000)
      } else {
        setLoginError('Invalid username or password')
      }
    } catch (error: any) {
      // Enhanced error handling with specific error types
      let errorMessage = 'Login failed. Please try again.'

      if (error.message.includes('locked') || error.message.includes('too many')) {
        errorMessage = 'Account temporarily locked due to too many failed attempts. Please try again later.'
      } else if (error.message.includes('Invalid') || error.message.includes('incorrect')) {
        errorMessage = 'Invalid username or password. Please check your credentials.'
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        errorMessage = 'Network error. Please check your connection and try again.'
      } else if (error.message.includes('server') || error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later or contact support.'
      }

      setLoginError(errorMessage)

      // Enhanced error toast
      toast.error(
        `❌ Login Failed\n\n${errorMessage}`,
        {
          duration: 6000,
          style: {
            background: '#ef4444',
            color: '#ffffff',
            fontWeight: '500',
            borderRadius: '8px',
            boxShadow: '0 10px 25px rgba(239, 68, 68, 0.3)',
          },
          iconTheme: {
            primary: '#ffffff',
            secondary: '#ef4444',
          },
        }
      )
    } finally {
      setIsLoading(false)
    }
  }

  const handleForgotPassword = () => {
    // TODO: Implement forgot password functionality
    toast('Forgot password feature coming soon', { icon: 'ℹ️' })
  }

  return (
    <Card className={cn('mx-auto w-full max-w-md shadow-xl border-0 bg-white/95 backdrop-blur-sm', className)}>
      <CardHeader className='space-y-3 text-center pb-6'>
        <div className='mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-2'>
          <Shield className='w-6 h-6 text-primary' />
        </div>
        <CardTitle className='text-2xl font-bold text-gray-900'>Welcome Back</CardTitle>
        <p className='text-sm text-gray-600'>Sign in to your DFPTA account to continue</p>
      </CardHeader>

      <CardContent className='space-y-6'>
        <form onSubmit={handleSubmit(onSubmit)} className='space-y-5'>
          {/* Enhanced Global Error Message */}
          {loginError && (
            <div className='flex items-start gap-3 rounded-lg border border-red-200 bg-red-50 p-4 text-sm text-red-800 animate-in slide-in-from-top-2 duration-300'>
              <AlertCircle className='h-5 w-5 text-red-600 flex-shrink-0 mt-0.5' />
              <div>
                <p className='font-medium mb-1'>Authentication Failed</p>
                <p className='text-red-700'>{loginError}</p>
                {loginAttempts > 2 && (
                  <p className='text-xs text-red-600 mt-2'>
                    💡 Tip: Make sure your username and password are correct
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Enhanced Username Field */}
          <div className='space-y-2'>
            <Label htmlFor='username' className='text-sm font-medium text-gray-700 flex items-center gap-2'>
              <User className='h-4 w-4 text-gray-500' />
              Username
            </Label>
            <div className='relative'>
              <Input
                id='username'
                type='text'
                placeholder='Enter your username'
                autoComplete='username'
                autoFocus
                disabled={isLoading}
                className={cn(
                  'pl-10 h-11 transition-all duration-200 border-gray-300 focus:border-primary focus:ring-primary/20',
                  errors.username && 'border-red-300 focus:border-red-500 focus:ring-red-500/20',
                  touchedFields.username && !errors.username && watchedUsername && 'border-green-300 focus:border-green-500 focus:ring-green-500/20'
                )}
                {...register('username')}
              />
              <User className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
              {touchedFields.username && !errors.username && watchedUsername && (
                <CheckCircle className='absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500' />
              )}
            </div>
            {errors.username && (
              <p className='text-xs text-red-600 flex items-center gap-1 mt-1'>
                <AlertCircle className='h-3 w-3' />
                {errors.username.message}
              </p>
            )}
          </div>

          {/* Enhanced Password Field */}
          <div className='space-y-2'>
            <Label htmlFor='password' className='text-sm font-medium text-gray-700 flex items-center gap-2'>
              <Lock className='h-4 w-4 text-gray-500' />
              Password
            </Label>
            <div className='relative'>
              <Input
                id='password'
                type={showPassword ? 'text' : 'password'}
                placeholder='Enter your password'
                autoComplete='current-password'
                disabled={isLoading}
                className={cn(
                  'pl-10 pr-10 h-11 transition-all duration-200 border-gray-300 focus:border-primary focus:ring-primary/20',
                  errors.password && 'border-red-300 focus:border-red-500 focus:ring-red-500/20',
                  touchedFields.password && !errors.password && watchedPassword && 'border-green-300 focus:border-green-500 focus:ring-green-500/20'
                )}
                {...register('password')}
              />
              <Lock className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
              <button
                type='button'
                onClick={togglePasswordVisibility}
                disabled={isLoading}
                className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200 disabled:opacity-50'
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? <EyeOff className='h-4 w-4' /> : <Eye className='h-4 w-4' />}
              </button>
            </div>
            {errors.password && (
              <p className='text-xs text-red-600 flex items-center gap-1 mt-1'>
                <AlertCircle className='h-3 w-3' />
                {errors.password.message}
              </p>
            )}
          </div>

          {/* Enhanced Forgot Password Link */}
          {showForgotPassword && (
            <div className='text-right'>
              <button
                type='button'
                onClick={handleForgotPassword}
                className='text-sm text-primary hover:text-primary/80 hover:underline focus:underline focus:outline-none transition-colors duration-200 disabled:opacity-50'
                disabled={isLoading}
              >
                Forgot password?
              </button>
            </div>
          )}

          {/* Enhanced Submit Button */}
          <Button
            type='submit'
            size='lg'
            disabled={!isValid || isLoading}
            className='w-full h-12 bg-primary hover:bg-primary/90 text-white font-semibold transition-all duration-200 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed'
            aria-label={isLoading ? 'Signing in, please wait' : 'Sign in to your account'}
          >
            {isLoading ? (
              <div className='flex items-center gap-2'>
                <Loader2 className='h-5 w-5 animate-spin' />
                <span>Signing in...</span>
              </div>
            ) : (
              <div className='flex items-center gap-2'>
                <LogIn className='h-5 w-5' />
                <span>Sign In</span>
              </div>
            )}
          </Button>
        </form>

        {/* Enhanced Additional Info */}
        <div className='mt-8 text-center space-y-3'>
          <div className='flex items-center justify-center gap-2 text-xs text-gray-500'>
            <div className='h-px bg-gray-200 flex-1' />
            <span className='px-2 bg-white'>DFPTA E-Voting System v2.0</span>
            <div className='h-px bg-gray-200 flex-1' />
          </div>
          <p className='text-xs text-gray-400'>
            Social Mobilization and Networking Unit - SDO Camarines Sur
          </p>
          <div className='flex items-center justify-center gap-4 text-xs text-gray-400'>
            <span className='flex items-center gap-1'>
              <Shield className='h-3 w-3' />
              Secure
            </span>
            <span>•</span>
            <span className='flex items-center gap-1'>
              <CheckCircle className='h-3 w-3' />
              Verified
            </span>
            <span>•</span>
            <span className='flex items-center gap-1'>
              <Lock className='h-3 w-3' />
              Encrypted
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default LoginForm
