import ActivityLog from '../models/ActivityLog.js'
import logger from '../utils/logger.js'

/**
 * Service for logging user activities
 */
export class ActivityLogService {
  /**
   * Log user login activity
   */
  static async logLogin(user, ipAddress, userAgent = '', success = true) {
    try {
      const activityData = {
        activityType: 'user_login',
        performedBy: user._id,
        description: success 
          ? `User ${user.username} logged in successfully`
          : `Failed login attempt for user ${user.username}`,
        metadata: {
          username: user.username,
          municipality: user.municipality,
          district: user.district,
          role: user.role,
          ipAddress,
          userAgent,
          success,
          timestamp: new Date()
        },
        ipAddress,
        userAgent,
        severity: success ? 'info' : 'warning'
      }

      const log = await ActivityLog.create(activityData)
      logger.info(`Activity logged: ${success ? 'Login' : 'Failed login'} - ${user.username}`)
      return log
    } catch (error) {
      logger.error('Failed to log login activity:', error)
      return null
    }
  }

  /**
   * Log user logout activity
   */
  static async logLogout(user, ipAddress, sessionDuration = null) {
    try {
      const activityData = {
        activityType: 'user_logout',
        performedBy: user._id,
        description: `User ${user.username} logged out`,
        metadata: {
          username: user.username,
          municipality: user.municipality,
          district: user.district,
          role: user.role,
          ipAddress,
          sessionDuration,
          timestamp: new Date()
        },
        ipAddress,
        severity: 'info'
      }

      const log = await ActivityLog.create(activityData)
      logger.info(`Activity logged: Logout - ${user.username}`)
      return log
    } catch (error) {
      logger.error('Failed to log logout activity:', error)
      return null
    }
  }

  /**
   * Log vote submission activity
   */
  static async logVoteSubmission(user, voteCount, ipAddress) {
    try {
      const activityData = {
        activityType: 'vote_submitted',
        performedBy: user._id,
        description: `User ${user.username} submitted ${voteCount} votes`,
        metadata: {
          username: user.username,
          municipality: user.municipality,
          district: user.district,
          voteCount,
          ipAddress,
          timestamp: new Date()
        },
        ipAddress,
        severity: 'info'
      }

      const log = await ActivityLog.create(activityData)
      logger.info(`Activity logged: Vote submission - ${user.username} (${voteCount} votes)`)
      return log
    } catch (error) {
      logger.error('Failed to log vote submission activity:', error)
      return null
    }
  }

  /**
   * Get recent activities for admin dashboard
   */
  static async getRecentActivities(limit = 50, skip = 0) {
    try {
      const activities = await ActivityLog.find({})
        .populate('performedBy', 'username municipality district role')
        .sort({ createdAt: -1 })
        .limit(limit)
        .skip(skip)
        .lean()

      // Transform data for frontend compatibility
      const transformedActivities = activities.map(activity => ({
        id: activity._id,
        username: activity.performedBy?.username || 'Unknown',
        municipality: activity.performedBy?.municipality || 'Unknown',
        district: activity.performedBy?.district || 'Unknown',
        action: this.mapActivityTypeToAction(activity.activityType),
        description: activity.description,
        timestamp: activity.createdAt,
        ipAddress: activity.metadata?.ipAddress || activity.ipAddress || 'Unknown'
      }))

      return transformedActivities
    } catch (error) {
      logger.error('Failed to get recent activities:', error)
      return []
    }
  }

  /**
   * Get total count of activities
   */
  static async getActivitiesCount() {
    try {
      return await ActivityLog.countDocuments({})
    } catch (error) {
      logger.error('Failed to get activities count:', error)
      return 0
    }
  }

  /**
   * Map activity type to frontend action
   */
  static mapActivityTypeToAction(activityType) {
    const mapping = {
      'user_login': 'login',
      'user_logout': 'logout',
      'vote_submitted': 'vote',
      'vote_reset': 'reset',
      'user_created': 'create',
      'user_updated': 'update',
      'user_deleted': 'delete',
      'admin_action': 'admin',
      'security_event': 'security'
    }
    return mapping[activityType] || 'other'
  }

  /**
   * Get activity statistics
   */
  static async getActivityStats(days = 7) {
    try {
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)

      const stats = await ActivityLog.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: '$activityType',
            count: { $sum: 1 }
          }
        },
        {
          $sort: { count: -1 }
        }
      ])

      return stats
    } catch (error) {
      logger.error('Failed to get activity stats:', error)
      return []
    }
  }
}

export default ActivityLogService
