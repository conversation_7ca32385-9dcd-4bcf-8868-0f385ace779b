import { Document, Packer, Paragraph, TextRun, Table, TableRow, TableCell, AlignmentType, WidthType } from 'docx'

/**
 * Generate DOCX export with specific DFPTA formatting
 */
export class DOCXExportService {
  constructor() {
    this.doc = null
  }

  /**
   * Create DFPTA header with exact formatting specifications
   */
  createDFPTAHeader() {
    return [
      // Line 1: "Republika ng Pilipinas" (Old English Text MT, 12pt, Bold)
      new Paragraph({
        children: [
          new TextRun({
            text: "Republika ng Pilipinas",
            bold: true,
            size: 24, // 12pt = 24 half-points
            font: "Times New Roman", // Fallback font
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: { after: 200 },
      }),

      // Line 2: "Kagawaran ng Edukasyon" (Old English Text MT, 17pt, Bold)
      new Paragraph({
        children: [
          new TextRun({
            text: "Kagawaran ng Edukasyon",
            bold: true,
            size: 34, // 17pt = 34 half-points
            font: "Times New Roman", // Fallback font
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: { after: 200 },
      }),

      // Line 3: "Rehiyon V" (<PERSON><PERSON><PERSON>, 10pt, Bold)
      new Paragraph({
        children: [
          new TextRun({
            text: "Rehiyon V",
            bold: true,
            size: 20, // 10pt = 20 half-points
            font: "Times New Roman", // Fallback font
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: { after: 200 },
      }),

      // Line 4: "TANGGAPAN NG MGA PAARALANG PANSANGAY NG CAMARINES SUR" (Tahoma, 10pt, Bold)
      new Paragraph({
        children: [
          new TextRun({
            text: "TANGGAPAN NG MGA PAARALANG PANSANGAY NG CAMARINES SUR",
            bold: true,
            size: 20, // 10pt = 20 half-points
            font: "Arial", // Fallback font similar to Tahoma
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: { after: 400 },
      }),
    ]
  }

  /**
   * Create election results content
   */
  createElectionResults(results, statistics, generationDate) {
    const content = []

    // Title
    content.push(
      new Paragraph({
        children: [
          new TextRun({
            text: "DFPTA ELECTION RESULTS",
            bold: true,
            size: 32, // 16pt = 32 half-points
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: { after: 400 },
      })
    )

    // Generation date
    content.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `Generated on: ${generationDate.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            })}`,
            size: 20,
          }),
        ],
        alignment: AlignmentType.RIGHT,
        spacing: { after: 400 },
      })
    )

    // Statistics section
    content.push(
      new Paragraph({
        children: [
          new TextRun({
            text: "ELECTION STATISTICS",
            bold: true,
            size: 24,
          }),
        ],
        spacing: { after: 200 },
      })
    )

    // Statistics table
    const statsRows = [
      new TableRow({
        children: [
          new TableCell({
            children: [new Paragraph({ children: [new TextRun({ text: "Total Voters:", bold: true })] })],
            width: { size: 3000, type: WidthType.DXA },
          }),
          new TableCell({
            children: [new Paragraph({ children: [new TextRun({ text: (statistics.totalVoters || 0).toString() })] })],
            width: { size: 2000, type: WidthType.DXA },
          }),
        ],
      }),
      new TableRow({
        children: [
          new TableCell({
            children: [new Paragraph({ children: [new TextRun({ text: "Votes Cast:", bold: true })] })],
          }),
          new TableCell({
            children: [new Paragraph({ children: [new TextRun({ text: (statistics.totalVotesCast || 0).toString() })] })],
          }),
        ],
      }),
      new TableRow({
        children: [
          new TableCell({
            children: [new Paragraph({ children: [new TextRun({ text: "Participation Rate:", bold: true })] })],
          }),
          new TableCell({
            children: [new Paragraph({ children: [new TextRun({ text: `${(statistics.participationRate || 0).toFixed(1)}%` })] })],
          }),
        ],
      }),
      new TableRow({
        children: [
          new TableCell({
            children: [new Paragraph({ children: [new TextRun({ text: "Total Candidates:", bold: true })] })],
          }),
          new TableCell({
            children: [new Paragraph({ children: [new TextRun({ text: (statistics.totalCandidates || 0).toString() })] })],
          }),
        ],
      }),
    ]

    content.push(
      new Table({
        rows: statsRows,
        width: { size: 5000, type: WidthType.DXA },
      })
    )

    content.push(
      new Paragraph({
        children: [new TextRun({ text: "" })],
        spacing: { after: 400 },
      })
    )

    // Results section
    content.push(
      new Paragraph({
        children: [
          new TextRun({
            text: "ELECTION RESULTS",
            bold: true,
            size: 24,
          }),
        ],
        spacing: { after: 200 },
      })
    )

    // Results table
    const resultsRows = [
      // Header row
      new TableRow({
        children: [
          new TableCell({
            children: [new Paragraph({ children: [new TextRun({ text: "Rank", bold: true })] })],
            width: { size: 1000, type: WidthType.DXA },
          }),
          new TableCell({
            children: [new Paragraph({ children: [new TextRun({ text: "Municipality", bold: true })] })],
            width: { size: 3000, type: WidthType.DXA },
          }),
          new TableCell({
            children: [new Paragraph({ children: [new TextRun({ text: "District", bold: true })] })],
            width: { size: 2000, type: WidthType.DXA },
          }),
          new TableCell({
            children: [new Paragraph({ children: [new TextRun({ text: "Votes", bold: true })] })],
            width: { size: 1000, type: WidthType.DXA },
          }),
        ],
      }),
    ]

    // Data rows
    if (results && results.length > 0) {
      results.forEach((candidate, index) => {
        resultsRows.push(
          new TableRow({
            children: [
              new TableCell({
                children: [new Paragraph({ children: [new TextRun({ text: (candidate.rank || index + 1).toString() })] })],
              }),
              new TableCell({
                children: [new Paragraph({ children: [new TextRun({ text: candidate.municipalityName || 'Unknown' })] })],
              }),
              new TableCell({
                children: [new Paragraph({ children: [new TextRun({ text: candidate.district || 'Unknown' })] })],
              }),
              new TableCell({
                children: [new Paragraph({ children: [new TextRun({ text: (candidate.voteCount || 0).toString() })] })],
              }),
            ],
          })
        )
      })
    } else {
      resultsRows.push(
        new TableRow({
          children: [
            new TableCell({
              children: [new Paragraph({ children: [new TextRun({ text: "No results available" })] })],
              columnSpan: 4,
            }),
          ],
        })
      )
    }

    content.push(
      new Table({
        rows: resultsRows,
        width: { size: 7000, type: WidthType.DXA },
      })
    )

    return content
  }

  /**
   * Generate complete DOCX document
   */
  async generateDocument(results, statistics) {
    const generationDate = new Date()
    
    // Create header content
    const headerContent = this.createDFPTAHeader()
    
    // Create results content
    const resultsContent = this.createElectionResults(results, statistics, generationDate)
    
    // Combine all content
    const allContent = [...headerContent, ...resultsContent]

    // Create document
    this.doc = new Document({
      sections: [
        {
          properties: {},
          children: allContent,
        },
      ],
    })

    // Generate buffer
    const buffer = await Packer.toBuffer(this.doc)
    return buffer
  }
}

/**
 * Generate DOCX export for election results
 */
export const generateElectionResultsDOCX = async (results, statistics) => {
  try {
    const docxService = new DOCXExportService()
    const docxBuffer = await docxService.generateDocument(results, statistics)
    return docxBuffer
  } catch (error) {
    throw new Error(`DOCX generation failed: ${error.message}`)
  }
}

export default DOCXExportService
