import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { AlertCircle, Calendar, CheckCircle, Clock, Info, Vote } from 'lucide-react'
import { But<PERSON> } from '../../atoms/Button'

export interface VotingStatus {
  votingStatus: 'active' | 'inactive' | 'completed' | 'pending'
  hasVoted: boolean
  votingStartTime?: string
  votingEndTime?: string
  maxCandidatesPerVote?: number
  totalCandidates?: number

  lastVotedAt?: string
}

export interface VotingStatusCardProps {
  status: VotingStatus | null
  loading?: boolean
  onStartVoting?: () => void
  onViewResults?: () => void
  className?: string
}

export function VotingStatusCard({
  status,
  loading = false,
  onStartVoting,
  onViewResults,
  className,
}: VotingStatusCardProps) {
  if (loading || !status) {
    return (
      <Card className={cn('border-l-primary border-l-4 shadow-lg', className)}>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="flex items-center gap-4">
              <div className="rounded-full bg-gray-200 p-3 h-12 w-12"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Determine status info
  const getStatusInfo = () => {
    if (status.hasVoted) {
      return {
        icon: CheckCircle,
        text: 'Vote Submitted',
        description: 'Thank you for participating in the election',
        bgColor: 'bg-success/10',
        color: 'text-success',
        borderColor: 'border-l-success',
        badgeVariant: 'default' as const,
        badgeText: 'Completed',
      }
    }

    switch (status.votingStatus) {
      case 'active':
        return {
          icon: Vote,
          text: 'Voting Active',
          description: 'You can cast your vote now',
          bgColor: 'bg-primary/10',
          color: 'text-primary',
          borderColor: 'border-l-primary',
          badgeVariant: 'default' as const,
          badgeText: 'Active',
        }
      case 'pending':
        return {
          icon: Clock,
          text: 'Voting Pending',
          description: 'Voting will begin soon',
          bgColor: 'bg-warning/10',
          color: 'text-warning',
          borderColor: 'border-l-warning',
          badgeVariant: 'secondary' as const,
          badgeText: 'Pending',
        }
      case 'completed':
        return {
          icon: CheckCircle,
          text: 'Voting Completed',
          description: 'The voting period has ended',
          bgColor: 'bg-success/10',
          color: 'text-success',
          borderColor: 'border-l-success',
          badgeVariant: 'outline' as const,
          badgeText: 'Completed',
        }
      default:
        return {
          icon: AlertCircle,
          text: 'Voting Inactive',
          description: 'Voting is currently not available',
          bgColor: 'bg-destructive/10',
          color: 'text-destructive',
          borderColor: 'border-l-destructive',
          badgeVariant: 'destructive' as const,
          badgeText: 'Inactive',
        }
    }
  }

  const statusInfo = getStatusInfo()
  const StatusIcon = statusInfo.icon

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const canVote = status.votingStatus === 'active' && !status.hasVoted

  return (
    <Card className={cn('border-l-4 shadow-lg', statusInfo.borderColor, className)}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <div className={cn('rounded-full p-3 shadow-sm', statusInfo.bgColor)}>
              <StatusIcon className={cn('h-6 w-6', statusInfo.color)} />
            </div>
            <div>
              <h3 className="text-lg font-semibold">{statusInfo.text}</h3>
              <p className="text-muted-foreground">{statusInfo.description}</p>
            </div>
          </div>
          <Badge variant={statusInfo.badgeVariant}>
            {statusInfo.badgeText}
          </Badge>
        </div>

        {/* Voting Details */}
        {status.votingStatus === 'active' && (
          <div className="mb-4 p-3 bg-muted/50 rounded-lg">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
              {status.maxCandidatesPerVote && (
                <div className="flex items-center gap-2">
                  <Vote className="h-4 w-4 text-muted-foreground" />
                  <span>Max selections: {status.maxCandidatesPerVote}</span>
                </div>
              )}
              {status.totalCandidates && (
                <div className="flex items-center gap-2">
                  <Info className="h-4 w-4 text-muted-foreground" />
                  <span>Total candidates: {status.totalCandidates}</span>
                </div>
              )}
              {status.currentRound && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>Round: {status.currentRound}</span>
                </div>
              )}
              {status.votingEndTime && (
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>Ends: {formatDateTime(status.votingEndTime)}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Last Voted Info */}
        {status.hasVoted && status.lastVotedAt && (
          <div className="mb-4 p-3 bg-success/5 border border-success/20 rounded-lg">
            <div className="flex items-center gap-2 text-sm text-success">
              <CheckCircle className="h-4 w-4" />
              <span>Voted on {formatDateTime(status.lastVotedAt)}</span>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3">
          {canVote && onStartVoting && (
            <Button
              onClick={onStartVoting}
              className="flex-1"
              size="lg"
              leftIcon={<Vote className="h-4 w-4" />}
            >
              Cast Your Vote
            </Button>
          )}

          {onViewResults && (
            <Button
              variant="outline"
              onClick={onViewResults}
              className={canVote ? '' : 'flex-1'}
              size="lg"
              leftIcon={<Info className="h-4 w-4" />}
            >
              View Results
            </Button>
          )}
        </div>

        {/* Progress Indicator for Active Voting */}
        {status.votingStatus === 'active' && status.votingStartTime && status.votingEndTime && (
          <div className="mt-4">
            <div className="flex justify-between text-xs text-muted-foreground mb-1">
              <span>Voting Progress</span>
              <span>
                {Math.round(
                  ((Date.now() - new Date(status.votingStartTime).getTime()) /
                    (new Date(status.votingEndTime).getTime() - new Date(status.votingStartTime).getTime())) *
                    100
                )}%
              </span>
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${Math.min(
                    100,
                    Math.max(
                      0,
                      ((Date.now() - new Date(status.votingStartTime).getTime()) /
                        (new Date(status.votingEndTime).getTime() - new Date(status.votingStartTime).getTime())) *
                        100
                    )
                  )}%`,
                }}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default VotingStatusCard
