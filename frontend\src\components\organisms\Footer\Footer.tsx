import { BarChart3, Mail, MapPin, Phone, Shield, Users, Vote } from 'lucide-react'
import { Link } from 'react-router-dom'

export interface FooterProps {
  className?: string
}

export function Footer({ className }: FooterProps) {
  const currentYear = new Date().getFullYear()

  return (
    <footer className={`border-t bg-muted/30 ${className || ''}`}>
      <div className='mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8'>
        <div className='grid grid-cols-1 gap-8 md:grid-cols-3'>
          {/* Brand Section */}
          <div className='space-y-4'>
            <div className='flex items-center space-x-3'>
              <div className='flex h-10 w-10 items-center justify-center rounded-full bg-primary/10'>
                <Vote className='h-5 w-5 text-primary' />
              </div>
              <div>
                <h3 className='text-lg font-bold text-foreground'>DFPTA E-Voting System</h3>
                <p className='text-xs text-muted-foreground'>
                  Social Mobilization and Networking Unit
                </p>
              </div>
            </div>
            <p className='text-sm text-muted-foreground leading-relaxed'>
              Secure, transparent, and reliable electronic voting platform for the Division Federated of Parent-Teacher Association.
            </p>
            <div className='flex items-center space-x-4 text-xs text-muted-foreground'>
              <div className='flex items-center space-x-1'>
                <Shield className='h-3 w-3' />
                <span>Secure</span>
              </div>
              <div className='flex items-center space-x-1'>
                <Users className='h-3 w-3' />
                <span>Transparent</span>
              </div>
              <div className='flex items-center space-x-1'>
                <BarChart3 className='h-3 w-3' />
                <span>Reliable</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className='space-y-4'>
            <h4 className='font-semibold text-gray-900'>Quick Links</h4>
            <nav className='flex flex-col space-y-2'>
              <Link to='/' className='text-sm text-gray-600 transition-colors hover:text-gray-900'>
                Home
              </Link>
              <Link
                to='/results'
                className='text-sm text-gray-600 transition-colors hover:text-gray-900'
              >
                Election Results
              </Link>
              <Link
                to='/login'
                className='text-sm text-gray-600 transition-colors hover:text-gray-900'
              >
                Login Portal
              </Link>
            </nav>
          </div>

          {/* Contact Information */}
          <div className='space-y-4'>
            <h4 className='font-semibold text-gray-900'>Contact Information</h4>
            <div className='space-y-2'>
              <div className='flex items-center space-x-2 text-sm text-gray-600'>
                <MapPin className='h-4 w-4' />
                <span>Department of Education - Schools Division Office of Camarines Sur</span>
              </div>
              <div className='flex items-center space-x-2 text-sm text-gray-600'>
                <Mail className='h-4 w-4' />
                <span><EMAIL></span>
              </div>
              <div className='flex items-center space-x-2 text-sm text-gray-600'>
                <Phone className='h-4 w-4' />
                <span> SocMob Phone No. +63 ************</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className='mt-8 border-t pt-8'>
          <div className='flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0'>
            <div className='text-center md:text-left'>
              <p className='text-sm text-gray-600'>
                © {currentYear} DFPTA E-Voting System. All rights reserved.
              </p>
              <p className='mt-1 text-xs text-gray-500'>Department of Education - Schools Division Office of Camarines Sur</p>
            </div>
            <div className='text-center md:text-right'>
              <p className='text-xs text-gray-500'>DFPTA E-Voting System v2.0</p>
              <p className='mt-1 text-xs text-gray-400'>by Gienevieve Bustilla</p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
