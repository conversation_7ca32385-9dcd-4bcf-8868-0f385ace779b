import mongoose from 'mongoose'
import dotenv from 'dotenv'
import axios from 'axios'
import logger from '../utils/logger.js'

// Load environment variables
dotenv.config()

// Test credentials
const TEST_CREDENTIALS = [
  { username: 'admin', password: 'socmob123', role: 'admin' },
  { username: 'balatan', password: 'bala#767', role: 'voter' },
  { username: 'cabusao', password: 'cabu=538', role: 'voter' },
  { username: 'pili', password: 'pili#519', role: 'voter' }
]

const API_BASE_URL = 'http://localhost:5000/api'

/**
 * Test login functionality
 */
const testLogin = async (username, password, expectedRole) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      username,
      password
    })

    if (response.data.success) {
      const user = response.data.data.user
      const token = response.data.data.token
      
      logger.info(`✅ Login successful for ${username}`, {
        username: user.username,
        role: user.role,
        municipality: user.municipality,
        district: user.district,
        isActive: user.isActive,
        hasToken: !!token
      })
      
      // Verify role matches expected
      if (user.role !== expectedRole) {
        logger.warn(`⚠️  Role mismatch for ${username}: expected ${expectedRole}, got ${user.role}`)
        return false
      }
      
      return true
    } else {
      logger.error(`❌ Login failed for ${username}: ${response.data.error}`)
      return false
    }
  } catch (error) {
    logger.error(`❌ Login error for ${username}:`, {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    })
    return false
  }
}

/**
 * Test all credentials
 */
const testAllLogins = async () => {
  try {
    logger.info('🧪 Starting login tests...')
    
    let successCount = 0
    let totalTests = TEST_CREDENTIALS.length
    
    for (const cred of TEST_CREDENTIALS) {
      logger.info(`Testing login for ${cred.username} (${cred.role})...`)
      const success = await testLogin(cred.username, cred.password, cred.role)
      
      if (success) {
        successCount++
      }
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500))
    }
    
    logger.info('📊 Test Results:')
    logger.info(`   ✅ Successful logins: ${successCount}/${totalTests}`)
    logger.info(`   ❌ Failed logins: ${totalTests - successCount}/${totalTests}`)
    
    if (successCount === totalTests) {
      logger.info('🎉 All login tests passed!')
      return true
    } else {
      logger.error('💥 Some login tests failed!')
      return false
    }
    
  } catch (error) {
    logger.error('Test execution failed:', error)
    return false
  }
}

/**
 * Test database connection and user count
 */
const testDatabaseState = async () => {
  try {
    logger.info('🔍 Checking database state...')
    
    // Connect to database
    const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URI
    if (!mongoUri) {
      throw new Error('MongoDB URI not found in environment variables')
    }
    
    await mongoose.connect(mongoUri)
    logger.info('✅ Database connected')
    
    // Import models
    const User = (await import('../models/User.js')).default
    const Candidate = (await import('../models/Candidate.js')).default
    const SystemSettings = (await import('../models/SystemSettings.js')).default
    
    // Count documents
    const userCount = await User.countDocuments()
    const adminCount = await User.countDocuments({ role: 'admin' })
    const voterCount = await User.countDocuments({ role: 'voter' })
    const candidateCount = await Candidate.countDocuments()
    const settingsCount = await SystemSettings.countDocuments()
    
    logger.info('📊 Database Statistics:')
    logger.info(`   👥 Total Users: ${userCount}`)
    logger.info(`   👑 Admin Users: ${adminCount}`)
    logger.info(`   🗳️  Voter Users: ${voterCount}`)
    logger.info(`   🏛️  Candidates: ${candidateCount}`)
    logger.info(`   ⚙️  Settings: ${settingsCount}`)
    
    // Check specific users exist
    const adminUser = await User.findOne({ username: 'admin' })
    const balatanUser = await User.findOne({ username: 'balatan' })
    
    logger.info('🔍 Sample User Check:')
    logger.info(`   Admin user exists: ${!!adminUser}`)
    logger.info(`   Balatan user exists: ${!!balatanUser}`)
    
    if (balatanUser) {
      logger.info(`   Balatan details: ${balatanUser.municipality}, ${balatanUser.district}`)
    }
    
    await mongoose.connection.close()
    logger.info('✅ Database check completed')
    
    return {
      users: userCount,
      admins: adminCount,
      voters: voterCount,
      candidates: candidateCount,
      settings: settingsCount,
      adminExists: !!adminUser,
      balatanExists: !!balatanUser
    }
    
  } catch (error) {
    logger.error('Database check failed:', error)
    throw error
  }
}

/**
 * Main test function
 */
const runTests = async () => {
  try {
    logger.info('🚀 Starting comprehensive login and database tests...')
    
    // Test database state first
    const dbStats = await testDatabaseState()
    
    // Wait a moment for server to be ready
    logger.info('⏳ Waiting for server to be ready...')
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Test logins
    const loginSuccess = await testAllLogins()
    
    logger.info('📋 Final Summary:')
    logger.info(`   Database Users: ${dbStats.users} (${dbStats.admins} admin, ${dbStats.voters} voters)`)
    logger.info(`   Database Candidates: ${dbStats.candidates}`)
    logger.info(`   Database Settings: ${dbStats.settings}`)
    logger.info(`   Login Tests: ${loginSuccess ? 'PASSED' : 'FAILED'}`)
    
    if (loginSuccess && dbStats.users === 36 && dbStats.candidates === 35) {
      logger.info('🎉 All tests passed! Database is properly seeded and login works.')
      process.exit(0)
    } else {
      logger.error('💥 Some tests failed or data is incomplete.')
      process.exit(1)
    }
    
  } catch (error) {
    logger.error('Test execution failed:', error)
    process.exit(1)
  }
}

// Run tests
runTests()
