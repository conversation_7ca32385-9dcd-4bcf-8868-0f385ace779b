import mongoose from 'mongoose'
import dotenv from 'dotenv'
import axios from 'axios'
import logger from '../utils/logger.js'

// Load environment variables
dotenv.config()

const API_BASE_URL = 'http://localhost:5000/api'

// Test credentials
const ADMIN_CREDS = { username: 'admin', password: 'socmob123' }
const VOTER_CREDS = { username: 'balatan', password: 'bala#767' }

/**
 * <PERSON>gin and get token
 */
const login = async (username, password) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      username,
      password
    })

    if (response.data.success) {
      return {
        token: response.data.data.token,
        user: response.data.data.user
      }
    } else {
      throw new Error(response.data.error)
    }
  } catch (error) {
    logger.error(`Login failed for ${username}:`, error.response?.data || error.message)
    throw error
  }
}

/**
 * Get public system settings (election status)
 */
const getElectionStatus = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/system/public-settings`)
    
    if (response.data.success) {
      const settings = response.data.data
      const electionActive = settings.voting_session_active?.value || false
      return electionActive
    } else {
      throw new Error('Failed to get election status')
    }
  } catch (error) {
    logger.error('Failed to get election status:', error.response?.data || error.message)
    throw error
  }
}

/**
 * Toggle election status (admin only)
 */
const toggleElection = async (adminToken) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/system/toggle-election`, {}, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    })

    if (response.data.success) {
      return response.data.data.electionActive
    } else {
      throw new Error(response.data.error)
    }
  } catch (error) {
    logger.error('Failed to toggle election:', error.response?.data || error.message)
    throw error
  }
}

/**
 * Get voting status for a user
 */
const getVotingStatus = async (token) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/voting/vote/status`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    if (response.data.success) {
      return response.data.data
    } else {
      throw new Error(response.data.error)
    }
  } catch (error) {
    logger.error('Failed to get voting status:', error.response?.data || error.message)
    throw error
  }
}

/**
 * Test the complete election flow
 */
const testElectionFlow = async () => {
  try {
    logger.info('🚀 Starting election flow test...')
    
    // Step 1: Login as admin
    logger.info('1️⃣ Logging in as admin...')
    const adminAuth = await login(ADMIN_CREDS.username, ADMIN_CREDS.password)
    logger.info(`✅ Admin logged in: ${adminAuth.user.username}`)
    
    // Step 2: Login as voter
    logger.info('2️⃣ Logging in as voter...')
    const voterAuth = await login(VOTER_CREDS.username, VOTER_CREDS.password)
    logger.info(`✅ Voter logged in: ${voterAuth.user.username} (${voterAuth.user.municipality})`)
    
    // Step 3: Check initial election status
    logger.info('3️⃣ Checking initial election status...')
    let electionStatus = await getElectionStatus()
    logger.info(`📊 Initial election status: ${electionStatus ? 'OPEN' : 'CLOSED'}`)
    
    // Step 4: Check voter's initial voting status
    logger.info('4️⃣ Checking voter\'s initial voting status...')
    let votingStatus = await getVotingStatus(voterAuth.token)
    logger.info(`🗳️ Voter can vote: ${votingStatus.canVote}`)
    logger.info(`📝 Voting status: ${votingStatus.votingStatus}`)
    logger.info(`💬 Message: ${votingStatus.message || 'No message'}`)
    
    // Step 5: If election is closed, open it
    if (!electionStatus) {
      logger.info('5️⃣ Opening election...')
      const newStatus = await toggleElection(adminAuth.token)
      logger.info(`✅ Election toggled. New status: ${newStatus ? 'OPEN' : 'CLOSED'}`)
      
      // Wait a moment for changes to propagate
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Verify election is now open
      electionStatus = await getElectionStatus()
      logger.info(`📊 Verified election status: ${electionStatus ? 'OPEN' : 'CLOSED'}`)
    } else {
      logger.info('5️⃣ Election is already open')
    }
    
    // Step 6: Check voter's voting status after election is opened
    logger.info('6️⃣ Checking voter\'s voting status after election opened...')
    votingStatus = await getVotingStatus(voterAuth.token)
    logger.info(`🗳️ Voter can vote: ${votingStatus.canVote}`)
    logger.info(`📝 Voting status: ${votingStatus.votingStatus}`)
    logger.info(`💬 Message: ${votingStatus.message || 'No message'}`)
    
    // Step 7: Close election
    logger.info('7️⃣ Closing election...')
    const closedStatus = await toggleElection(adminAuth.token)
    logger.info(`✅ Election toggled. New status: ${closedStatus ? 'OPEN' : 'CLOSED'}`)
    
    // Wait a moment for changes to propagate
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Step 8: Check voter's voting status after election is closed
    logger.info('8️⃣ Checking voter\'s voting status after election closed...')
    votingStatus = await getVotingStatus(voterAuth.token)
    logger.info(`🗳️ Voter can vote: ${votingStatus.canVote}`)
    logger.info(`📝 Voting status: ${votingStatus.votingStatus}`)
    logger.info(`💬 Message: ${votingStatus.message || 'No message'}`)
    
    // Step 9: Verify final election status
    logger.info('9️⃣ Verifying final election status...')
    electionStatus = await getElectionStatus()
    logger.info(`📊 Final election status: ${electionStatus ? 'OPEN' : 'CLOSED'}`)
    
    // Summary
    logger.info('📋 Test Summary:')
    logger.info('✅ Admin login: SUCCESS')
    logger.info('✅ Voter login: SUCCESS')
    logger.info('✅ Election status check: SUCCESS')
    logger.info('✅ Election toggle: SUCCESS')
    logger.info('✅ Voting status sync: SUCCESS')
    
    logger.info('🎉 Election flow test completed successfully!')
    
    return true
    
  } catch (error) {
    logger.error('💥 Election flow test failed:', error)
    return false
  }
}

/**
 * Test inactive user access
 */
const testInactiveUserAccess = async () => {
  try {
    logger.info('🔒 Testing inactive user access...')
    
    // Connect to database to create an inactive user for testing
    const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URI
    await mongoose.connect(mongoUri)
    
    const User = (await import('../models/User.js')).default
    
    // Find a user to temporarily deactivate
    const testUser = await User.findOne({ username: 'cabusao' })
    if (!testUser) {
      throw new Error('Test user not found')
    }
    
    const originalStatus = testUser.isActive
    
    // Deactivate user
    testUser.isActive = false
    await testUser.save()
    logger.info(`🔒 Deactivated user: ${testUser.username}`)
    
    try {
      // Try to login as inactive user
      const inactiveAuth = await login('cabusao', 'cabu=538')
      
      // Check voting status
      const votingStatus = await getVotingStatus(inactiveAuth.token)
      logger.info(`🗳️ Inactive user can vote: ${votingStatus.canVote}`)
      logger.info(`📝 Voting status: ${votingStatus.votingStatus}`)
      logger.info(`💬 Message: ${votingStatus.message}`)
      
      if (!votingStatus.canVote && votingStatus.votingStatus === 'user-disabled') {
        logger.info('✅ Inactive user properly blocked from voting')
      } else {
        logger.error('❌ Inactive user was not properly blocked from voting')
      }
      
    } finally {
      // Restore user status
      testUser.isActive = originalStatus
      await testUser.save()
      logger.info(`🔓 Restored user status: ${testUser.username}`)
    }
    
    await mongoose.connection.close()
    
  } catch (error) {
    logger.error('💥 Inactive user test failed:', error)
    throw error
  }
}

/**
 * Main test function
 */
const runTests = async () => {
  try {
    logger.info('🧪 Starting comprehensive election and access tests...')
    
    // Wait for server to be ready
    logger.info('⏳ Waiting for server to be ready...')
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Test election flow
    const electionFlowSuccess = await testElectionFlow()
    
    // Test inactive user access
    await testInactiveUserAccess()
    
    if (electionFlowSuccess) {
      logger.info('🎉 All tests passed!')
      process.exit(0)
    } else {
      logger.error('💥 Some tests failed!')
      process.exit(1)
    }
    
  } catch (error) {
    logger.error('Test execution failed:', error)
    process.exit(1)
  }
}

// Run tests
runTests()
