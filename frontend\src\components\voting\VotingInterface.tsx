import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'
import { useAuth } from '@/hooks/useAuth'
import { useDistrictsAndMunicipalities } from '@/hooks/useSystem'
import { useCandidateSelection, useCandidates, useSubmitVotes } from '@/hooks/useVoting'
import { Candidate } from '@/services/votingService'
import { CheckSquare, Grid, List, RotateCcw, Search } from 'lucide-react'
import { useState } from 'react'
import toast from 'react-hot-toast'
import { CandidateCard, CandidateCardCompact } from './CandidateCard'

interface VotingInterfaceProps {
  maxSelections: number
  onVoteSubmitted?: (submissionData?: any) => void
}

export function VotingInterface({ maxSelections, onVoteSubmitted }: VotingInterfaceProps) {
  // State
  const [searchTerm, setSearchTerm] = useState('')
  const [municipalityFilter, setMunicipalityFilter] = useState('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showDetails, setShowDetails] = useState(false)

  // Hooks
  const { user } = useAuth()
  const { data: candidatesData, isLoading: candidatesLoading } = useCandidates()
  const { data: systemData, isLoading: systemLoading } = useDistrictsAndMunicipalities()
  const {
    selectedCandidates,
    selectedCount,
    remainingSelections,
    canSelectMore,
    validation,
    toggleCandidate,
    clearAll,
    isSelected,
  } = useCandidateSelection(maxSelections, user?.username)

  const submitVotesMutation = useSubmitVotes()

  // Filter and sort candidates alphabetically by municipality name
  const filteredCandidates =
    candidatesData?.candidates?.filter((candidate: Candidate) => {
      if (!candidate) return false

      const matchesSearch =
        candidate.municipalityName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        false ||
        candidate.district?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        false
      const matchesMunicipality =
        municipalityFilter === 'all' ||
        !municipalityFilter ||
        candidate.district === municipalityFilter
      return matchesSearch && matchesMunicipality
    })?.sort((a, b) => {
      // Sort alphabetically by municipality name
      const nameA = a.municipalityName?.toLowerCase() || ''
      const nameB = b.municipalityName?.toLowerCase() || ''
      return nameA.localeCompare(nameB)
    }) || []

  // Handle vote submission with enhanced validation and confirmation
  const handleSubmitVotes = () => {
    // Validation checks
    if (!validation.isValid) {
      toast.error(validation.errors[0] || 'Please select valid candidates before submitting.')
      return
    }

    if (selectedCandidates.length === 0) {
      toast.error('Please select at least one candidate before submitting.')
      return
    }

    if (selectedCandidates.length > maxSelections) {
      toast.error(`You can only select up to ${maxSelections} candidates.`)
      return
    }

    // Confirmation dialog
    const candidateNames = selectedCandidates.map(id => {
      const candidate = candidatesData?.candidates?.find(c => c._id === id)
      return candidate?.municipalityName || 'Unknown'
    }).join(', ')

    const confirmed = window.confirm(
      `Are you sure you want to submit your votes for the following ${selectedCandidates.length} candidates?\n\n${candidateNames}\n\nOnce submitted, your votes cannot be changed.`
    )

    if (!confirmed) {
      return
    }

    // Submit votes with comprehensive error handling
    submitVotesMutation.mutate(selectedCandidates, {
      onSuccess: data => {
        // Show single detailed success message with candidate names
        const submittedCandidateNames = selectedCandidates.map(id => {
          const candidate = candidatesData?.candidates?.find(c => c._id === id)
          return candidate?.municipalityName || 'Unknown'
        }).join(', ')

        toast.success(
          `Successfully submitted ${data.votesCount} votes for: ${submittedCandidateNames}. Thank you for participating in the DFPTA election!`,
          { duration: 8000 }
        )
        onVoteSubmitted?.(data)
      },
      onError: (error: any) => {
        // Enhanced error handling with specific error messages
        let message = 'Failed to submit votes. Please try again.'

        if (error.response?.status === 400) {
          message = error.response.data?.error || 'Invalid vote submission. Please check your selections.'
        } else if (error.response?.status === 403) {
          message = 'You are not authorized to vote or have already voted.'
        } else if (error.response?.status === 409) {
          message = 'You have already submitted your votes for this election.'
        } else if (error.response?.status >= 500) {
          message = 'Server error occurred. Please try again later or contact support.'
        }

        toast.error(message, { duration: 6000 })

        // Log error for debugging
        console.error('Vote submission error:', error)
      },
    })
  }

  // Select All functionality removed as per requirements

  if (candidatesLoading) {
    return (
      <div className='space-y-6'>
        <div className='flex items-center justify-between'>
          <Skeleton className='h-8 w-48' />
          <Skeleton className='h-10 w-32' />
        </div>
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
          {Array.from({ length: 6 }).map((_, i) => (
            <Skeleton key={i} className='h-48' />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className='space-y-2'>
      {/* Compact Header with Progress */}
      <Card className='border-l-primary border-l-2'>
        <CardHeader className='pb-2 pt-3'>
          <div className='flex items-center justify-between'>
            <CardTitle className='text-base'>Select Your Candidates</CardTitle>
            <div className='flex items-center space-x-2'>
              <Badge
                variant={selectedCount === maxSelections ? 'default' : 'secondary'}
                className='px-2 py-0 text-xs h-5'
              >
                {selectedCount} / {maxSelections}
              </Badge>
              {remainingSelections > 0 && (
                <span className='text-muted-foreground text-xs'>
                  {remainingSelections} left
                </span>
              )}
            </div>
          </div>

          {/* Compact Progress Bar */}
          <div className='mt-2'>
            <div className='h-1 w-full rounded-full bg-gray-200'>
              <div
                className='bg-primary h-1 rounded-full transition-all duration-300 ease-in-out'
                style={{ width: `${(selectedCount / maxSelections) * 100}%` }}
              />
            </div>
            {selectedCount === maxSelections && (
              <p className='mt-1 flex items-center gap-1 text-xs text-green-600'>
                <CheckSquare className='h-4 w-4' />
                Ready to submit your votes!
              </p>
            )}
          </div>
        </CardHeader>
        <CardContent className='py-3'>
          {/* Compact Search and filters */}
          <div className='flex flex-col space-y-2 sm:flex-row sm:space-x-2 sm:space-y-0'>
            <div className='flex-1'>
              <div className='relative'>
                <Search className='text-muted-foreground absolute left-2 top-1/2 h-3 w-3 -translate-y-1/2' />
                <Input
                  placeholder='Search candidates...'
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className='pl-7 h-8 text-sm'
                />
              </div>
            </div>

            <Select value={municipalityFilter} onValueChange={setMunicipalityFilter}>
              <SelectTrigger className='w-full sm:w-40 h-8 text-sm'>
                <SelectValue placeholder='Filter by district' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All districts</SelectItem>
                {systemData?.municipalities?.map((municipality: string) => (
                  <SelectItem key={municipality} value={municipality}>
                    {municipality}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Compact Action buttons */}
          <div className='mt-2 flex items-center justify-between gap-2'>
            <div className='flex items-center space-x-1'>
              <Button variant='outline' size='sm' onClick={clearAll} disabled={selectedCount === 0} className='h-7 px-2 text-xs'>
                <RotateCcw className='mr-1 h-3 w-3' />
                Clear
              </Button>
            </div>

            <div className='flex items-center space-x-1'>
              <div className='flex items-center rounded-md border'>
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size='sm'
                  onClick={() => setViewMode('grid')}
                  className='rounded-r-none h-7 px-2'
                >
                  <Grid className='h-3 w-3' />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size='sm'
                  onClick={() => setViewMode('list')}
                  className='rounded-l-none h-7 px-2'
                >
                  <List className='h-3 w-3' />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Candidates grid/list */}
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <h3 className='text-lg font-semibold'>Candidates ({filteredCandidates.length})</h3>
          {searchTerm || municipalityFilter ? (
            <Button
              variant='ghost'
              size='sm'
              onClick={() => {
                setSearchTerm('')
                setMunicipalityFilter('')
              }}
            >
              <RotateCcw className='mr-2 h-4 w-4' />
              Clear Filters
            </Button>
          ) : null}
        </div>

        {filteredCandidates.length === 0 ? (
          <Card>
            <CardContent className='py-8 text-center'>
              <p className='text-muted-foreground'>No candidates found matching your criteria.</p>
            </CardContent>
          </Card>
        ) : (
          <div
            className={
              viewMode === 'grid'
                ? 'grid gap-2 grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6'
                : 'space-y-2'
            }
          >
            {filteredCandidates.map(candidate =>
              viewMode === 'grid' ? (
                <CandidateCard
                  key={candidate._id}
                  candidate={candidate}
                  isSelected={isSelected(candidate._id)}
                  onToggle={(candidateId) => toggleCandidate(candidateId, candidate)}
                  disabled={!canSelectMore && !isSelected(candidate._id)}
                  showDetails={showDetails}
                />
              ) : (
                <CandidateCardCompact
                  key={candidate._id}
                  candidate={candidate}
                  isSelected={isSelected(candidate._id)}
                  onToggle={(candidateId) => toggleCandidate(candidateId, candidate)}
                  disabled={!canSelectMore && !isSelected(candidate._id)}
                />
              )
            )}
          </div>
        )}
      </div>

      {/* Submit button */}
      {selectedCount > 0 && (
        <Card className='sticky bottom-4 shadow-lg'>
          <CardContent className='py-4'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='font-medium'>
                  {selectedCount} candidate{selectedCount !== 1 ? 's' : ''} selected
                </p>
                {!validation.isValid && (
                  <p className='text-destructive mt-1 text-sm'>{validation.errors[0]}</p>
                )}
              </div>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    size='lg'
                    disabled={!validation.isValid || submitVotesMutation.isLoading}
                    className='min-w-32'
                  >
                    {submitVotesMutation.isLoading ? 'Submitting...' : 'Submit Votes'}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent className='max-w-md'>
                  <AlertDialogHeader>
                    <AlertDialogTitle className='flex items-center gap-2'>
                      <CheckSquare className='text-primary h-5 w-5' />
                      Confirm Your Votes
                    </AlertDialogTitle>
                    <AlertDialogDescription asChild>
                      <div className='space-y-3'>
                        <div>
                          You are about to submit votes for {selectedCount} candidate
                          {selectedCount !== 1 ? 's' : ''}:
                        </div>
                        <div className='max-h-32 overflow-y-auto rounded-lg bg-gray-50 p-3'>
                          <ul className='space-y-1 text-sm'>
                            {selectedCandidates.map(candidateId => {
                              const candidate = candidatesData?.candidates?.find(
                                c => c._id === candidateId
                              )
                              return candidate ? (
                                <li key={candidateId} className='flex items-center gap-2'>
                                  <CheckSquare className='h-3 w-3 text-green-600' />
                                  <span className='font-medium'>{candidate.municipalityName}</span>
                                  <span className='text-gray-500'>({candidate.district})</span>
                                </li>
                              ) : null
                            })}
                          </ul>
                        </div>
                        <div className='text-sm font-medium text-amber-600'>
                          ⚠️ This action cannot be undone. Please review your selections carefully.
                        </div>
                      </div>
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Review Again</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleSubmitVotes}
                      className='bg-primary hover:bg-primary/90'
                    >
                      {submitVotesMutation.isLoading ? (
                        <>
                          <div className='mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white' />
                          Submitting...
                        </>
                      ) : (
                        'Confirm & Submit Votes'
                      )}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
