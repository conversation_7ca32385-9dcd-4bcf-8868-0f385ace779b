import dfptaLogo from '@/assets/images/dfptalogo.png'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/hooks/useAuth'
import { BarChart3, Home, LogOut, Menu, User, Vote } from 'lucide-react'
import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'

export interface HeaderProps {
  className?: string
}

export function Header({ className }: HeaderProps) {
  const { user, logout } = useAuth()
  const navigate = useNavigate()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const handleLogout = async () => {
    await logout()
    navigate('/')
  }

  return (
    <header className={`border-b bg-white shadow-sm ${className || ''}`} role="banner">
      <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
        <div className='flex h-16 items-center justify-between'>
          {/* Logo */}
          <div className='flex items-center'>
            <Link
              to='/'
              className='flex items-center space-x-3 transition-opacity hover:opacity-80'
            >
              <img src={dfptaLogo} alt='DFPTA Logo' className='h-10 w-10 object-contain' />
              <div className='flex flex-col'>
                <span className='font-heading text-lg font-semibold text-gray-900'>
                  DFPTA E-Voting System
                </span>
                <span className='text-xs text-gray-500'>Schools Division Office of Camarines Sur</span>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className='hidden items-center space-x-4 md:flex' role="navigation" aria-label="Main navigation">
            <Button variant='ghost' size='sm' asChild>
              <Link to='/' className='flex items-center gap-2' aria-label="Go to home page">
                <Home className='h-4 w-4' aria-hidden="true" />
                Home
              </Link>
            </Button>
            <Button variant='ghost' size='sm' asChild>
              <Link to='/results' className='flex items-center gap-2' aria-label="View election results">
                <BarChart3 className='h-4 w-4' aria-hidden="true" />
                Results
              </Link>
            </Button>

            {user ? (
              <div className='flex items-center space-x-4'>
                {/* Dashboard Button (Admin) */}
                {user.role === 'admin' && (
                  <Button variant='ghost' size='sm' asChild>
                    <Link to='/admin' className='flex items-center gap-2' aria-label="Go to admin dashboard">
                      <Vote className='h-4 w-4' aria-hidden="true" />
                      Dashboard
                    </Link>
                  </Button>
                )}

                {/* Vote Button (Voter) */}
                {user.role === 'voter' && (
                  <Button variant='ghost' size='sm' asChild>
                    <Link to='/vote' className='flex items-center gap-2' aria-label="Go to voting interface">
                      <Vote className='h-4 w-4' aria-hidden="true" />
                      Vote
                    </Link>
                  </Button>
                )}

                {/* Welcome Label */}
                <div className='flex items-center space-x-3 px-3 py-2 bg-muted/50 rounded-lg'>
                  <User className='h-4 w-4 text-muted-foreground' />
                  <div className='flex flex-col'>
                    <span className='text-sm font-medium text-foreground'>
                      Welcome, {user.municipality || user.username}
                    </span>
                    <span className='text-xs capitalize text-muted-foreground'>
                      {user.role} account
                    </span>
                  </div>
                </div>

                {/* Logout Button */}
                <Button
                  variant='outline'
                  size='sm'
                  onClick={handleLogout}
                  className='flex items-center gap-2'
                  aria-label="Logout from your account"
                >
                  <LogOut className='h-4 w-4' aria-hidden="true" />
                  Logout
                </Button>
              </div>
            ) : (
              <Button size='sm' asChild>
                <Link to='/login' className='flex items-center gap-2'>
                  <Vote className='h-4 w-4' />
                  Login
                </Link>
              </Button>
            )}
          </nav>

          {/* Mobile Navigation Toggle */}
          <div className='md:hidden'>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label={isMobileMenuOpen ? "Close mobile menu" : "Open mobile menu"}
              aria-expanded={isMobileMenuOpen}
            >
              <Menu className='h-5 w-5' aria-hidden="true" />
            </Button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div className='mt-4 border-t pb-4 pt-4 md:hidden'>
            <div className='flex flex-col space-y-2'>
              <Button
                variant='ghost'
                className='justify-start'
                asChild
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Link to='/' className='flex items-center gap-2'>
                  <Home className='h-4 w-4' />
                  Home
                </Link>
              </Button>
              <Button
                variant='ghost'
                className='justify-start'
                asChild
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Link to='/results' className='flex items-center gap-2'>
                  <BarChart3 className='h-4 w-4' />
                  Results
                </Link>
              </Button>

              {user ? (
                <>
                  <div className='border-b px-3 py-2'>
                    <p className='text-sm font-medium'>{user.municipality || user.username}</p>
                    <p className='text-xs capitalize text-gray-500'>{user.role} Account</p>
                  </div>

                  {user.role === 'admin' && (
                    <Button
                      variant='ghost'
                      className='justify-start'
                      asChild
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <Link to='/admin' className='flex items-center gap-2'>
                        <Vote className='h-4 w-4' />
                        Admin Dashboard
                      </Link>
                    </Button>
                  )}

                  {user.role === 'voter' && (
                    <Button
                      variant='ghost'
                      className='justify-start'
                      asChild
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <Link to='/vote' className='flex items-center gap-2'>
                        <Vote className='h-4 w-4' />
                        Voting Portal
                      </Link>
                    </Button>
                  )}

                  <Button
                    variant='ghost'
                    className='justify-start text-red-600'
                    onClick={() => {
                      handleLogout()
                      setIsMobileMenuOpen(false)
                    }}
                  >
                    <LogOut className='mr-2 h-4 w-4' />
                    Logout
                  </Button>
                </>
              ) : (
                <Button
                  className='justify-start'
                  asChild
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <Link to='/login' className='flex items-center gap-2'>
                    <Vote className='h-4 w-4' />
                    Login
                  </Link>
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
