import dotenv from 'dotenv'
import mongoose from 'mongoose'
import logger from '../utils/logger.js'

// Load environment variables
dotenv.config()

// Import models
import Candidate from '../models/Candidate.js'
import SystemSettings from '../models/SystemSettings.js'
import User from '../models/User.js'

// MongoDB connection
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URI
    if (!mongoUri) {
      throw new Error('MongoDB URI not found in environment variables')
    }
    const conn = await mongoose.connect(mongoUri)
    logger.info(`MongoDB Connected: ${conn.connection.host}`)
  } catch (error) {
    logger.error('Database connection error:', error)
    process.exit(1)
  }
}

// Municipality data from credentials.md
const MUNICIPALITY_DATA = [
  // 1st Congressional District
  { username: 'cabusao', municipality: 'Cabusao', district: '1st District', password: 'cabu=538' },
  { username: 'delgallego', municipality: 'Del Gallego', district: '1st District', password: 'del #230' },
  { username: 'lupi', municipality: 'Lupi', district: '1st District', password: 'lupi&171' },
  { username: 'ragay', municipality: 'Ragay', district: '1st District', password: 'raga*649' },
  { username: 'sipocot', municipality: 'Sipocot', district: '1st District', password: 'sipo@508' },

  // 2nd Congressional District
  { username: 'gainza', municipality: 'Gainza', district: '2nd District', password: 'gain#464' },
  { username: 'libmanan', municipality: 'Libmanan', district: '2nd District', password: 'libm%957' },
  { username: 'milaor', municipality: 'Milaor', district: '2nd District', password: 'mila#385' },
  { username: 'minalabac', municipality: 'Minalabac', district: '2nd District', password: 'mina&583' },
  { username: 'pamplona', municipality: 'Pamplona', district: '2nd District', password: 'pamp$409' },
  { username: 'pasacao', municipality: 'Pasacao', district: '2nd District', password: 'pasa*841' },
  { username: 'sanfernando', municipality: 'San Fernando', district: '2nd District', password: 'san +279' },

  // 3rd Congressional District
  { username: 'bombon', municipality: 'Bombon', district: '3rd District', password: 'bomb=387' },
  { username: 'calabanga', municipality: 'Calabanga', district: '3rd District', password: 'cala@618' },
  { username: 'camaligan', municipality: 'Camaligan', district: '3rd District', password: 'cama=886' },
  { username: 'canaman', municipality: 'Canaman', district: '3rd District', password: 'cana*744' },
  { username: 'magarao', municipality: 'Magarao', district: '3rd District', password: 'maga#295' },
  { username: 'ocampo', municipality: 'Ocampo', district: '3rd District', password: 'ocam#158' },
  { username: 'pili', municipality: 'Pili', district: '3rd District', password: 'pili#519' },

  // 4th Congressional District
  { username: 'caramoan', municipality: 'Caramoan', district: '4th District', password: 'cara+819' },
  { username: 'garchitorena', municipality: 'Garchitorena', district: '4th District', password: 'garc#461' },
  { username: 'goa', municipality: 'Goa', district: '4th District', password: 'goax&682' },
  { username: 'lagonoy', municipality: 'Lagonoy', district: '4th District', password: 'lago+708' },
  { username: 'parubcan', municipality: 'Parubcan', district: '4th District', password: 'paru$046' },
  { username: 'sagnay', municipality: 'Sagnay', district: '4th District', password: 'sagn*399' },
  { username: 'sanjose', municipality: 'San Jose', district: '4th District', password: 'san &172' },
  { username: 'siruma', municipality: 'Siruma', district: '4th District', password: 'siru&835' },
  { username: 'tigaon', municipality: 'Tigaon', district: '4th District', password: 'tiga+813' },
  { username: 'tinambac', municipality: 'Tinambac', district: '4th District', password: 'tina#882' },

  // 5th Congressional District
  { username: 'baao', municipality: 'Baao', district: '5th District', password: 'baao@414' },
  { username: 'balatan', municipality: 'Balatan', district: '5th District', password: 'bala#767' },
  { username: 'bato', municipality: 'Bato', district: '5th District', password: 'bato#471' },
  { username: 'buhi', municipality: 'Buhi', district: '5th District', password: 'buhi$098' },
  { username: 'bula', municipality: 'Bula', district: '5th District', password: 'bula$845' },
  { username: 'nabua', municipality: 'Nabua', district: '5th District', password: 'nabu+794' },
]

// Admin user data
const ADMIN_DATA = {
  username: 'admin',
  password: 'socmob123',
  role: 'admin'
}

/**
 * Clear all existing data from the database
 */
const clearDatabase = async () => {
  try {
    logger.info('🗑️  Clearing existing database data...')

    // Drop all collections
    const collections = await mongoose.connection.db.listCollections().toArray()

    for (const collection of collections) {
      await mongoose.connection.db.collection(collection.name).drop()
      logger.info(`   ✅ Dropped collection: ${collection.name}`)
    }

    logger.info('✅ Database cleared successfully')
  } catch (error) {
    if (error.message.includes('ns not found')) {
      logger.info('✅ Database was already empty')
    } else {
      throw error
    }
  }
}

/**
 * Create admin user
 */
const createAdminUser = async () => {
  try {
    logger.info('👑 Creating admin user...')

    const adminUser = new User({
      username: ADMIN_DATA.username,
      password: ADMIN_DATA.password,
      role: ADMIN_DATA.role,
      isActive: true,
      hasVoted: false
    })

    await adminUser.save()
    logger.info(`   ✅ Admin user created: ${ADMIN_DATA.username}`)

    return adminUser
  } catch (error) {
    logger.error('Failed to create admin user:', error)
    throw error
  }
}

/**
 * Create municipality users
 */
const createMunicipalityUsers = async () => {
  try {
    logger.info('🏛️  Creating municipality users...')

    const users = []

    for (const userData of MUNICIPALITY_DATA) {
      const user = new User({
        username: userData.username,
        municipality: userData.municipality,
        district: userData.district,
        password: userData.password,
        role: 'voter',
        isActive: true,
        hasVoted: false
      })

      await user.save()
      users.push(user)
      logger.info(`   ✅ Created user: ${userData.username} (${userData.municipality})`)
    }

    logger.info(`✅ Created ${users.length} municipality users`)
    return users
  } catch (error) {
    logger.error('Failed to create municipality users:', error)
    throw error
  }
}

/**
 * Create candidates for each municipality
 */
const createCandidates = async () => {
  try {
    logger.info('🗳️  Creating candidates...')

    const candidates = []

    for (const userData of MUNICIPALITY_DATA) {
      const candidate = new Candidate({
        municipalityName: userData.municipality,
        district: userData.district,
        totalVotes: 0,
        isActive: true
      })

      await candidate.save()
      candidates.push(candidate)
      logger.info(`   ✅ Created candidate: ${userData.municipality}`)
    }

    logger.info(`✅ Created ${candidates.length} candidates`)
    return candidates
  } catch (error) {
    logger.error('Failed to create candidates:', error)
    throw error
  }
}

/**
 * Initialize system settings
 */
const initializeSystemSettings = async () => {
  try {
    logger.info('⚙️  Initializing system settings...')

    const defaultSettings = [
      {
        key: 'voting_session_active',
        value: false,
        type: 'boolean',
        description: 'Controls whether voting is currently active system-wide',
        category: 'voting'
      },
      {
        key: 'public_results_enabled',
        value: false,
        type: 'boolean',
        description: 'Controls whether election results are visible to the public',
        category: 'display'
      },
      {
        key: 'max_candidates_per_vote',
        value: 15,
        type: 'number',
        description: 'Maximum number of candidates a voter can select',
        category: 'voting'
      },
      {
        key: 'system_maintenance_mode',
        value: false,
        type: 'boolean',
        description: 'Enables maintenance mode to prevent system access',
        category: 'system'
      }
    ]

    for (const settingData of defaultSettings) {
      const setting = new SystemSettings(settingData)
      await setting.save()
      logger.info(`   ✅ Created setting: ${settingData.key} = ${settingData.value}`)
    }

    logger.info('✅ System settings initialized')
  } catch (error) {
    logger.error('Failed to initialize system settings:', error)
    throw error
  }
}

/**
 * Verify the seeded data
 */
const verifySeededData = async () => {
  try {
    logger.info('🔍 Verifying seeded data...')

    // Count users
    const totalUsers = await User.countDocuments()
    const adminUsers = await User.countDocuments({ role: 'admin' })
    const voterUsers = await User.countDocuments({ role: 'voter' })

    // Count candidates
    const totalCandidates = await Candidate.countDocuments()
    const activeCandidates = await Candidate.countDocuments({ isActive: true })

    // Count settings
    const totalSettings = await SystemSettings.countDocuments()

    logger.info('📊 Database Statistics:')
    logger.info(`   👥 Total Users: ${totalUsers}`)
    logger.info(`   👑 Admin Users: ${adminUsers}`)
    logger.info(`   🗳️  Voter Users: ${voterUsers}`)
    logger.info(`   🏛️  Total Candidates: ${totalCandidates}`)
    logger.info(`   ✅ Active Candidates: ${activeCandidates}`)
    logger.info(`   ⚙️  System Settings: ${totalSettings}`)

    // Verify expected counts
    const expectedUsers = MUNICIPALITY_DATA.length + 1 // municipalities + admin
    const expectedCandidates = MUNICIPALITY_DATA.length
    const expectedSettings = 4

    if (totalUsers !== expectedUsers) {
      throw new Error(`Expected ${expectedUsers} users, but found ${totalUsers}`)
    }

    if (totalCandidates !== expectedCandidates) {
      throw new Error(`Expected ${expectedCandidates} candidates, but found ${totalCandidates}`)
    }

    if (totalSettings !== expectedSettings) {
      throw new Error(`Expected ${expectedSettings} settings, but found ${totalSettings}`)
    }

    logger.info('✅ Data verification passed')

    return {
      users: { total: totalUsers, admin: adminUsers, voters: voterUsers },
      candidates: { total: totalCandidates, active: activeCandidates },
      settings: totalSettings
    }
  } catch (error) {
    logger.error('Data verification failed:', error)
    throw error
  }
}

/**
 * Main reset and seed function
 */
const resetAndSeedDatabase = async () => {
  try {
    logger.info('🚀 Starting database reset and seeding process...')

    // Connect to database
    await connectDB()

    // Clear existing data
    await clearDatabase()

    // Create users
    await createAdminUser()
    await createMunicipalityUsers()

    // Create candidates
    await createCandidates()

    // Initialize system settings
    await initializeSystemSettings()

    // Verify the seeded data
    const stats = await verifySeededData()

    logger.info('🎉 Database reset and seeding completed successfully!')
    logger.info('📋 Summary:')
    logger.info(`   • ${stats.users.total} users created (${stats.users.admin} admin, ${stats.users.voters} voters)`)
    logger.info(`   • ${stats.candidates.total} candidates created`)
    logger.info(`   • ${stats.settings} system settings initialized`)
    logger.info('')
    logger.info('🔑 Login Credentials:')
    logger.info('   Admin: admin / socmob123')
    logger.info('   Example Municipality: balatan / bala#767')

  } catch (error) {
    logger.error('Database reset and seeding failed:', error)
    throw error
  } finally {
    await mongoose.connection.close()
    logger.info('Database connection closed')
  }
}

// Command line interface
const command = process.argv[2]

if (command === 'reset-and-seed' || !command) {
  resetAndSeedDatabase()
    .then(() => {
      logger.info('Database reset and seeding process completed successfully')
      process.exit(0)
    })
    .catch((error) => {
      logger.error('Database reset and seeding process failed:', error)
      process.exit(1)
    })
} else {
  console.log('Usage:')
  console.log('  node src/scripts/resetAndSeedDatabase.js [reset-and-seed]')
  console.log('')
  console.log('Commands:')
  console.log('  reset-and-seed  - Clear database and seed with fresh data (default)')
  process.exit(1)
}
