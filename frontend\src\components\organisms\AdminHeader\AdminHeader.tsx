import dfptaLogo from '@/assets/images/dfptalogo.png'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/hooks/useAuth'
import {
  Activity,
  Archive,
  BarChart3,
  LogOut,
  Menu,
  Settings,
  Shield,
  Users,
  X
} from 'lucide-react'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'

export interface AdminHeaderProps {
  currentSection?: string
  onSectionChange?: (section: string) => void
  className?: string
}

export function AdminHeader({ currentSection, onSectionChange, className }: AdminHeaderProps) {
  const { user, logout } = useAuth()
  const navigate = useNavigate()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const handleLogout = async () => {
    await logout()
    navigate('/')
  }

  const handleSectionClick = (sectionId: string, item?: any) => {
    // Handle external navigation for Public Results
    if (item?.isExternal && item?.href) {
      window.open(item.href, '_blank')
      return
    }

    // Update current section
    onSectionChange?.(sectionId)

    // Smooth scroll to section
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      })
    }
  }

  const navigationItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: BarChart3,
      description: 'Overview and statistics',
      ariaLabel: 'Navigate to dashboard overview'
    },
    {
      id: 'archive',
      label: 'Archive',
      icon: Archive,
      description: 'Historical data',
      ariaLabel: 'Navigate to archive management'
    },
    {
      id: 'logs',
      label: 'Logs',
      icon: Activity,
      description: 'Activity monitoring',
      ariaLabel: 'Navigate to activity logs'
    },
    {
      id: 'users',
      label: 'Users',
      icon: Users,
      description: 'Manage user accounts',
      ariaLabel: 'Navigate to user management'
    },
    {
      id: 'results',
      label: 'Results',
      icon: BarChart3,
      description: 'View voting results',
      ariaLabel: 'Navigate to results and analytics'
    },
    {
      id: 'public-results',
      label: 'Public Results',
      icon: BarChart3,
      description: 'View public results page',
      ariaLabel: 'Navigate to public results page',
      isExternal: true,
      href: '/results'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      description: 'System configuration',
      ariaLabel: 'Navigate to system settings'
    }
  ]

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin':
        return 'destructive'
      case 'execom':
        return 'default'
      case 'tie-breaker':
        return 'secondary'
      default:
        return 'outline'
    }
  }

  return (
    <header className={`border-b bg-gradient-to-r from-white to-gray-50/50 shadow-md backdrop-blur-sm ${className || ''}`} role="banner">
      <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
        <div className='flex h-16 items-center justify-between'>
          {/* Logo and Title */}
          <div className='flex items-center'>
            <div className='flex items-center space-x-3'>
              <img src={dfptaLogo} alt='DFPTA Logo' className='h-10 w-10 object-contain' />
              <div className='flex flex-col'>
                <span className='font-heading text-lg font-semibold text-gray-900'>
                  DFPTA E-Voting System
                </span>
                <span className='text-xs text-gray-500'>Schools Division Office of Camarines Sur</span>
              </div>
            </div>
          </div>

          {/* Desktop Navigation - Enhanced Design */}
          <nav className='hidden items-center space-x-1 lg:flex' role="navigation" aria-label="Admin navigation">
            {navigationItems.map((item) => {
              const Icon = item.icon
              const isActive = currentSection === item.id

              return (
                <Button
                  key={item.id}
                  variant={isActive ? "default" : "ghost"}
                  size="sm"
                  onClick={() => handleSectionClick(item.id, item)}
                  className={`
                    flex items-center gap-2 px-3 py-2 rounded-md transition-all duration-200
                    hover:scale-105 hover:shadow-sm focus:ring-2 focus:ring-primary/20
                    ${isActive
                      ? 'bg-primary text-primary-foreground shadow-md'
                      : 'hover:bg-muted/80 hover:text-foreground'
                    }
                  `}
                  aria-label={item.ariaLabel}
                  aria-current={isActive ? "page" : undefined}
                  title={item.description}
                >
                  <Icon className={`h-4 w-4 transition-transform duration-200 ${isActive ? 'scale-110' : ''}`} aria-hidden="true" />
                  <span className="hidden xl:inline font-medium">{item.label}</span>
                </Button>
              )
            })}
          </nav>

          {/* User Info and Logout - Enhanced Design */}
          <div className='flex items-center space-x-3'>
            {/* Enhanced User Info */}
            {user && (
              <div className='hidden items-center space-x-3 px-4 py-2 bg-gradient-to-r from-muted/60 to-muted/40 rounded-lg border border-border/50 shadow-sm md:flex hover:shadow-md transition-shadow duration-200'>
                <div className='p-1.5 bg-primary/10 rounded-full'>
                  <Shield className='h-4 w-4 text-primary' />
                </div>
                <div className='flex flex-col'>
                  <span className='text-sm font-semibold text-foreground'>
                    {user.municipality || user.username}
                  </span>
                  <Badge
                    variant={getRoleBadgeVariant(user.role)}
                    className="text-xs w-fit font-medium shadow-sm"
                  >
                    Administrator
                  </Badge>
                </div>
              </div>
            )}

            {/* Enhanced Logout Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleLogout}
              className="hidden items-center gap-2 px-4 py-2 border-destructive/30 text-destructive hover:text-destructive-foreground hover:bg-destructive hover:border-destructive hover:shadow-md transition-all duration-200 hover:scale-105 focus:ring-2 focus:ring-destructive/20 md:flex"
              aria-label="Logout from admin account"
              title="Sign out of your admin account"
            >
              <LogOut className="h-4 w-4 transition-transform duration-200 hover:rotate-12" aria-hidden="true" />
              <span className="font-medium">Logout</span>
            </Button>

            {/* Enhanced Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden p-2 hover:bg-muted/80 focus:ring-2 focus:ring-primary/20 transition-all duration-200"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label={isMobileMenuOpen ? "Close mobile menu" : "Open mobile menu"}
              aria-expanded={isMobileMenuOpen}
              title={isMobileMenuOpen ? "Close menu" : "Open menu"}
            >
              {isMobileMenuOpen ? (
                <X className="h-5 w-5 transition-transform duration-200 rotate-90" />
              ) : (
                <Menu className="h-5 w-5 transition-transform duration-200 hover:scale-110" />
              )}
            </Button>
          </div>
        </div>

        {/* Enhanced Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className='border-t bg-gradient-to-b from-white to-gray-50/30 py-4 md:hidden shadow-inner'>
            <div className='space-y-3 px-2'>
              {/* Enhanced User Info Mobile */}
              {user && (
                <div className='border-b border-border/50 px-4 py-3 bg-muted/30 rounded-lg mb-3'>
                  <div className='flex items-center space-x-3'>
                    <div className='p-1.5 bg-primary/10 rounded-full'>
                      <Shield className='h-4 w-4 text-primary' />
                    </div>
                    <div>
                      <p className='text-sm font-semibold text-foreground'>{user.municipality || user.username}</p>
                      <Badge variant={getRoleBadgeVariant(user.role)} className="text-xs mt-1 font-medium">
                        Administrator
                      </Badge>
                    </div>
                  </div>
                </div>
              )}

              {/* Enhanced Navigation Items Mobile */}
              {navigationItems.map((item) => {
                const Icon = item.icon
                const isActive = currentSection === item.id

                return (
                  <Button
                    key={item.id}
                    variant={isActive ? "default" : "ghost"}
                    className={`
                      w-full justify-start px-4 py-3 rounded-lg transition-all duration-200
                      hover:scale-[1.02] hover:shadow-sm focus:ring-2 focus:ring-primary/20
                      ${isActive
                        ? 'bg-primary text-primary-foreground shadow-md'
                        : 'hover:bg-muted/80'
                      }
                    `}
                    onClick={() => {
                      handleSectionClick(item.id, item)
                      setIsMobileMenuOpen(false)
                    }}
                    aria-label={item.ariaLabel}
                    title={item.description}
                  >
                    <Icon className={`h-4 w-4 mr-3 transition-transform duration-200 ${isActive ? 'scale-110' : ''}`} />
                    <span className="font-medium">{item.label}</span>
                  </Button>
                )
              })}

              {/* Enhanced Logout Mobile */}
              <div className="pt-2 mt-2 border-t border-border/50">
                <Button
                  variant="ghost"
                  className="w-full justify-start px-4 py-3 rounded-lg text-destructive hover:text-destructive-foreground hover:bg-destructive hover:shadow-md transition-all duration-200 hover:scale-[1.02] focus:ring-2 focus:ring-destructive/20"
                  onClick={() => {
                    handleLogout()
                    setIsMobileMenuOpen(false)
                  }}
                  aria-label="Logout from admin account"
                  title="Sign out of your admin account"
                >
                  <LogOut className="h-4 w-4 mr-3 transition-transform duration-200 hover:rotate-12" />
                  <span className="font-medium">Logout</span>
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}

export default AdminHeader
