# DFPTA Vote Counting Mechanism Documentation

## Overview
The DFPTA voting system uses a comprehensive vote counting mechanism with multiple layers of data storage, validation, and aggregation to ensure accuracy and auditability.

## 1. Vote Storage Architecture

### Primary Vote Storage: Vote Collection
**Location**: `backend/src/models/Vote.js`

Each individual vote is stored as a separate document in the `votes` collection with the following structure:

```javascript
{
  _id: ObjectId,
  voterId: ObjectId,           // Reference to User who voted
  candidateId: ObjectId,       // Reference to Candidate voted for
  voteType: 'regular',         // Type of vote (currently only 'regular')
  round: 1,                    // Voting round (currently single round)
  sessionId: ObjectId,         // Reference to VotingSession (optional)
  voterMunicipality: String,   // Municipality of voter (for analytics)
  candidateMunicipality: String, // Municipality of candidate
  ipAddress: String,           // Voter's IP (for audit)
  userAgent: String,           // V<PERSON>'s browser (for audit)
  batchId: String,             // Groups votes from same submission
  batchPosition: Number,       // Position within batch
  totalBatchSize: Number,      // Total votes in batch
  voteHash: String,            // SHA256 hash for verification
  isVerified: Boolean,         // Vote verification status
  timestamp: Date              // When vote was cast
}
```

### Secondary Vote Storage: Candidate Aggregation
**Location**: `backend/src/models/Candidate.js`

Each candidate maintains aggregated vote counts:

```javascript
{
  totalVotes: Number,          // Total votes received across all rounds
  votesByRound: [{
    round: Number,             // Voting round
    voteType: String,          // Type of vote
    votes: Number              // Vote count for this round/type
  }]
}
```

### User Voting Status
**Location**: `backend/src/models/User.js`

Each user tracks their voting participation:

```javascript
{
  hasVoted: Boolean,           // Whether user has voted
  lastVotedAt: Date,           // When user last voted
  votingRounds: {
    hasVotedInRound: {
      round1: Boolean          // Voted in specific round
    },
    lastVotedRound: Number,    // Last round user voted in
    lastVotedAt: Date          // Last voting timestamp
  }
}
```

## 2. Vote Submission Process

### Step-by-Step Vote Submission
**Controller**: `backend/src/controllers/votingController.js` - `submitVotes()`

1. **Validation**:
   - Check if election is active via `SystemSettings.voting_session_active`
   - Verify user hasn't already voted (`user.hasVoted`)
   - Validate candidate selection (1-15 candidates)
   - Ensure all candidates are valid and active

2. **Transaction Processing**:
   ```javascript
   // Start MongoDB transaction for atomicity
   const session = await mongoose.startSession()
   session.startTransaction()
   
   try {
     // Create individual vote records
     const votes = candidateIds.map((candidateId, index) => ({
       voterId: userId,
       candidateId,
       voteType: 'regular',
       round: 1,
       batchId: generateBatchId(),
       batchPosition: index + 1,
       // ... other fields
     }))
     
     // Insert all votes atomically
     await Vote.insertMany(votes, { session })
     
     // Update candidate vote counts
     await Promise.all(candidates.map(candidate => 
       candidate.addVoteForRound(1, 'regular', 1)
     ))
     
     // Mark user as voted
     user.hasVoted = true
     user.lastVotedAt = new Date()
     await user.save({ session })
     
     // Commit transaction
     await session.commitTransaction()
   } catch (error) {
     await session.abortTransaction()
     throw error
   }
   ```

## 3. Vote Counting and Aggregation

### Real-time Vote Counting
**Method**: `Vote.getVoteCountsByCandidate()`

```javascript
// Aggregation pipeline for vote counting
[
  { $match: { voteType: 'regular', isVerified: true } },
  { $group: {
      _id: '$candidateId',
      voteCount: { $sum: 1 },
      voters: { $push: '$voterMunicipality' }
  }},
  { $lookup: {
      from: 'candidates',
      localField: '_id',
      foreignField: '_id',
      as: 'candidate'
  }},
  { $sort: { voteCount: -1 } }
]
```

### Vote Statistics
**Method**: `Vote.getVotingStatistics()`

Calculates:
- Total votes cast
- Unique voter count
- Participating municipalities
- Voting duration
- Participation rate

## 4. Double Voting Prevention

### Database Constraints
```javascript
// Unique index prevents duplicate votes
voteSchema.index(
  { voterId: 1, candidateId: 1 },
  {
    unique: true,
    partialFilterExpression: { voteType: 'regular' }
  }
)
```

### Application-Level Checks
1. **User Status Check**: `user.hasVoted` flag
2. **Transaction Atomicity**: All vote operations in single transaction
3. **Validation**: Pre-submission checks in controller

## 5. Vote Reset Mechanism

### Admin Vote Reset
**Controller**: `backend/src/controllers/adminController.js` - `resetUserVote()`

```javascript
// Reset user's voting status
await User.findByIdAndUpdate(userId, {
  hasVoted: false,
  lastVotedAt: null,
  'votingRounds.hasVotedInRound.round1': false,
  'votingRounds.lastVotedRound': null,
  'votingRounds.lastVotedAt': null
})

// Remove user's vote records
await Vote.deleteMany({ voterId: userId })

// Recalculate candidate vote counts
// (This requires aggregation from remaining votes)
```

## 6. Vote Verification and Audit

### Vote Hash Generation
Each vote gets a SHA256 hash for integrity verification:

```javascript
const voteData = `${voterId}-${candidateId}-${round}-${voteType}-${timestamp}`
const voteHash = crypto.createHash('sha256').update(voteData).digest('hex')
```

### Audit Trail
- IP address and user agent logged
- Batch ID links related votes
- Timestamps for all operations
- Vote verification status

## 7. Results Calculation

### Public Results
**Controller**: `backend/src/controllers/publicController.js`

1. **Vote Aggregation**: Uses `Vote.getVoteCountsByCandidate()`
2. **Statistics**: Excludes admin users from participation calculations
3. **Ranking**: Sorts by vote count (descending)
4. **Percentages**: Calculates based on total verified votes

### Admin Results
**Controller**: `backend/src/controllers/adminController.js`

- Detailed vote breakdowns
- Municipality-wise statistics
- Participation rates by district
- Audit information

## 8. Data Consistency

### Transaction Guarantees
- All vote operations use MongoDB transactions
- Atomic updates to User, Vote, and Candidate collections
- Rollback on any failure

### Consistency Checks
- Vote counts in Candidate model match Vote collection aggregations
- User voting status matches Vote records
- Batch integrity maintained

## 9. Performance Optimizations

### Database Indexes
```javascript
// Vote collection indexes
{ voterId: 1, candidateId: 1, voteType: 1 }
{ candidateId: 1, voteType: 1 }
{ voterMunicipality: 1, timestamp: 1 }
{ batchId: 1, batchPosition: 1 }
```

### Aggregation Pipelines
- Optimized for real-time vote counting
- Efficient sorting and grouping
- Minimal data transfer

## 10. Security Measures

### Vote Anonymization
- Vote records don't expose voter identity in public results
- Only municipality information available for analytics
- IP addresses hidden in public APIs

### Integrity Verification
- Vote hashes prevent tampering
- Verification flags for audit
- Transaction logs for accountability

This comprehensive vote counting mechanism ensures accuracy, prevents fraud, maintains auditability, and provides real-time results while protecting voter privacy.
