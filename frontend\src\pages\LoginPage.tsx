import { BackendStatusChecker } from '@/components/BackendStatusChecker'
import { LoginForm } from '@/components/organisms/LoginForm'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

const LoginPage = () => {
  return (
    <div className='flex min-h-screen items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50 p-4'>
      <div className='w-full max-w-md space-y-6'>
        {/* Backend Status Checker */}
        <BackendStatusChecker />

        {/* Login Form */}
        <Card>
          <CardHeader className='text-center'>
            <CardTitle className='text-2xl font-bold'>DFPTA E-Voting</CardTitle>
            <p className='text-muted-foreground'>Sign in to your account</p>
          </CardHeader>
          <CardContent>
            <LoginForm />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default LoginPage
