import dfptaLogo from '@/assets/images/dfptalogo.png'
//import { BackendStatusChecker } from '@/components/BackendStatusChecker'
import { LoginForm } from '@/components/organisms/LoginForm'
import { useAuth } from '@/hooks/useAuth'
import { Shield, Users, Vote } from 'lucide-react'
import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'

const LoginPage = () => {
  const { user } = useAuth()
  const navigate = useNavigate()

  useEffect(() => {
    if (user) {
      // Redirect based on user role
      if (user.role === 'admin') {
        navigate('/admin', { replace: true })
      } else {
        navigate('/voting', { replace: true })
      }
    }
  }, [user, navigate])

  return (
    <div className='min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden'>
      {/* Background Pattern */}
      <div className='absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] -z-10' />

      {/* Floating Elements */}
      <div className='absolute top-20 left-10 w-20 h-20 bg-blue-200/30 rounded-full blur-xl animate-pulse' />
      <div className='absolute bottom-20 right-10 w-32 h-32 bg-indigo-200/30 rounded-full blur-xl animate-pulse delay-1000' />
      <div className='absolute top-1/2 left-1/4 w-16 h-16 bg-slate-200/30 rounded-full blur-xl animate-pulse delay-500' />

      <div className='relative z-10 min-h-screen flex'>
        {/* Left Side - Branding & Information */}
        <div className='hidden lg:flex lg:w-1/2 xl:w-3/5 bg-gradient-to-br from-primary/95 to-primary text-white p-12 items-center justify-center'>
          <div className='max-w-lg space-y-8'>
            {/* Logo and Title */}
            <div className='text-center space-y-6'>
              <div className='mx-auto w-24 h-24 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/20'>
                <img
                  src={dfptaLogo}
                  alt='DFPTA Logo'
                  className='w-16 h-16 object-contain drop-shadow-lg'
                />
              </div>
              <div className='space-y-3'>
                <h1 className='text-4xl font-bold tracking-tight'>
                  DFPTA E-Voting System
                </h1>
                <p className='text-xl text-blue-100 font-medium'>
                  Schools Division Office of Camarines Sur
                </p>
                <p className='text-blue-200 text-lg'>
                  Secure • Transparent • Democratic
                </p>
              </div>
            </div>

            {/* Features */}
            <div className='space-y-6'>
              <div className='flex items-start gap-4'>
                <div className='w-12 h-12 bg-white/10 rounded-lg flex items-center justify-center backdrop-blur-sm'>
                  <Shield className='w-6 h-6 text-blue-100' />
                </div>
                <div>
                  <h3 className='text-lg font-semibold mb-2'>Secure Voting</h3>
                  <p className='text-blue-100 text-sm leading-relaxed'>
                    Advanced encryption and authentication ensure your vote remains private and secure.
                  </p>
                </div>
              </div>

              <div className='flex items-start gap-4'>
                <div className='w-12 h-12 bg-white/10 rounded-lg flex items-center justify-center backdrop-blur-sm'>
                  <Vote className='w-6 h-6 text-blue-100' />
                </div>
                <div>
                  <h3 className='text-lg font-semibold mb-2'>Easy Process</h3>
                  <p className='text-blue-100 text-sm leading-relaxed'>
                    Intuitive interface makes voting simple and accessible for all participants.
                  </p>
                </div>
              </div>

              <div className='flex items-start gap-4'>
                <div className='w-12 h-12 bg-white/10 rounded-lg flex items-center justify-center backdrop-blur-sm'>
                  <Users className='w-6 h-6 text-blue-100' />
                </div>
                <div>
                  <h3 className='text-lg font-semibold mb-2'>Transparent Results</h3>
                  <p className='text-blue-100 text-sm leading-relaxed'>
                    Real-time results and comprehensive audit trails ensure complete transparency.
                  </p>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className='pt-8 border-t border-white/20'>
              <p className='text-blue-100 text-sm text-center'>
                Social Mobilization and Networking Unit
              </p>
            </div>
          </div>
        </div>

        {/* Right Side - Login Form */}
        <div className='w-full lg:w-1/2 xl:w-2/5 flex items-center justify-center p-6 lg:p-12'>
          <div className='w-full max-w-md space-y-8'>
            {/* Mobile Logo */}
            <div className='lg:hidden text-center space-y-4'>
              <div className='mx-auto w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center'>
                <img
                  src={dfptaLogo}
                  alt='DFPTA Logo'
                  className='w-14 h-14 object-contain'
                />
              </div>
              <div>
                <h1 className='text-2xl font-bold text-gray-900 mb-1'>
                  DFPTA E-Voting System
                </h1>
                <p className='text-gray-600 text-sm'>
                  Schools Division Office of Camarines Sur
                </p>
              </div>
            </div>

            {/* Backend Status Checker */}
           {/* <BackendStatusChecker className='mx-auto' />*/}

            {/* Login Form */}
            <LoginForm />

            {/* Additional Info for Mobile */}
            <div className='lg:hidden text-center pt-6 border-t border-gray-200'>
              <p className='text-xs text-gray-500 mb-2'>
                Secure • Transparent • Democratic
              </p>
              <p className='text-xs text-gray-400'>
                Social Mobilization and Networking Unit
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LoginPage
