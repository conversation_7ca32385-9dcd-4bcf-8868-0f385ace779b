import axios from 'axios'
import dotenv from 'dotenv'
import logger from '../utils/logger.js'

// Load environment variables
dotenv.config({ path: './backend/.env' })

const API_BASE_URL = 'http://localhost:5000/api'

/**
 * Comprehensive system audit and functionality verification
 */
const comprehensiveSystemAudit = async () => {
  try {
    logger.info('🔍 Starting comprehensive system audit...')
    
    // Test 1: Authentication System
    logger.info('📋 Testing Authentication System...')
    
    // Login as admin
    const adminLogin = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'socmob123'
    })
    
    const adminToken = adminLogin.data.data.token
    logger.info('✅ Admin login successful')
    
    // Login as voter (balatan)
    const voterLogin = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'bala<PERSON>',
      password: 'bala#767'
    })
    
    const voterToken = voterLogin.data.data.token
    logger.info('✅ Voter login successful (balatan - 5th District)')
    
    // Test 2: Voting Page Functionality
    logger.info('📋 Testing Voting Page Functionality...')
    
    // Get candidates for voting
    const candidatesResponse = await axios.get(`${API_BASE_URL}/candidates`, {
      headers: { 'Authorization': `Bearer ${voterToken}` }
    })
    
    const candidates = candidatesResponse.data.data.candidates
    logger.info(`✅ Retrieved ${candidates.length} candidates (including inactive)`)
    
    // Check for inactive candidates
    const inactiveCandidates = candidates.filter(c => !c.isActive)
    logger.info(`✅ Found ${inactiveCandidates.length} inactive candidates (should show as "Absent")`)
    
    // Get voting status
    const votingStatus = await axios.get(`${API_BASE_URL}/voting/status`, {
      headers: { 'Authorization': `Bearer ${voterToken}` }
    })
    
    logger.info(`✅ Voting status: ${votingStatus.data.data.votingStatus}`)
    logger.info(`✅ Can vote: ${votingStatus.data.data.canVote}`)
    logger.info(`✅ Has voted: ${votingStatus.data.data.hasVoted}`)
    
    // Test 3: Admin Dashboard Features
    logger.info('📋 Testing Admin Dashboard Features...')
    
    // Test municipality names toggle
    const municipalityToggleTest = await axios.put(`${API_BASE_URL}/system/settings/show_municipality_names`, {
      value: true
    }, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    })
    
    if (municipalityToggleTest.data.success) {
      logger.info('✅ Municipality names toggle working')
      
      // Restore original value
      await axios.put(`${API_BASE_URL}/system/settings/show_municipality_names`, {
        value: false
      }, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })
    }
    
    // Test district results toggle
    const districtToggleTest = await axios.put(`${API_BASE_URL}/system/settings/show_district_results`, {
      value: false
    }, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    })
    
    if (districtToggleTest.data.success) {
      logger.info('✅ District results toggle working')
      
      // Restore original value
      await axios.put(`${API_BASE_URL}/system/settings/show_district_results`, {
        value: true
      }, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })
    }
    
    // Test election archives
    const archivesResponse = await axios.get(`${API_BASE_URL}/admin/election/archives`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    })
    
    logger.info(`✅ Election archives accessible (${archivesResponse.data.data.length} archives)`)
    
    // Test user management
    const usersResponse = await axios.get(`${API_BASE_URL}/admin/users`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    })
    
    logger.info(`✅ User management accessible (${usersResponse.data.data.users.length} users)`)
    
    // Test 4: Results Page Improvements
    logger.info('📋 Testing Results Page Improvements...')
    
    const publicResults = await axios.get(`${API_BASE_URL}/results`)
    
    if (publicResults.data.success) {
      const { statistics, results } = publicResults.data.data
      
      // Check statistics corrections
      logger.info(`✅ Total Candidates: ${statistics.totalCandidates}`)
      logger.info(`✅ Absent Participants: ${statistics.totalAbsentParticipants}`)
      logger.info(`✅ Participation Rate: ${statistics.participationRate}%`)
      
      // Check display controls
      logger.info(`✅ Show Municipality Names: ${results.showMunicipalityNames}`)
      logger.info(`✅ Show District Results: ${results.showDistrictResults}`)
      
      // Check unified results structure
      logger.info(`✅ Unified results count: ${results.unifiedResults.length}`)
    }
    
    // Test 5: System Settings
    logger.info('📋 Testing System Settings...')
    
    const systemSettings = await axios.get(`${API_BASE_URL}/admin/settings`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    })
    
    logger.info(`✅ System settings accessible (${Object.keys(systemSettings.data.data).length} settings)`)
    
    // Test 6: Health Check
    logger.info('📋 Testing System Health...')
    
    const healthCheck = await axios.get(`${API_BASE_URL}/health`)
    logger.info(`✅ System health: ${healthCheck.data.status}`)
    
    // Final Summary
    logger.info('🎉 COMPREHENSIVE SYSTEM AUDIT COMPLETED!')
    logger.info('📊 AUDIT RESULTS SUMMARY:')
    logger.info('✅ Authentication System: Working')
    logger.info('✅ Voting Functionality: Working (with inactive candidate handling)')
    logger.info('✅ Admin Dashboard: All features accessible')
    logger.info('✅ Toggle Switches: Municipality names and district results working')
    logger.info('✅ Election Archives: Accessible')
    logger.info('✅ User Management: Working')
    logger.info('✅ Results Page: Statistics corrected, display controls working')
    logger.info('✅ System Settings: All accessible')
    logger.info('✅ Health Check: System healthy')
    
    logger.info('🚀 SYSTEM IS READY FOR COMPREHENSIVE TESTING!')
    
  } catch (error) {
    logger.error('💥 System audit failed:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data
    })
  }
}

// Run comprehensive audit
comprehensiveSystemAudit()
