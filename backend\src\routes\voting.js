import express from 'express'
import {
    getCandidates,
    getResults,
    getVotingHistory,
    getVotingStatus,
    submitVotes,
} from '../controllers/votingController.js'
import { activeUserRequired } from '../middleware/activeUserRequired.js'
import { protect, voterOnly } from '../middleware/auth.js'
import { validateVoteSubmission } from '../middleware/validation.js'
const router = express.Router()

// Public routes
router.get('/results', getResults)

// Protected routes - require authentication
router.use(protect)

// Get all active candidates (requires active user)
router.get('/candidates', activeUserRequired, getCandidates)

// Get voting status for current user (requires active user)
router.get('/vote/status', activeUserRequired, getVotingStatus)

// Get user's voting history (requires active user)
router.get('/vote/history', activeUserRequired, getVotingHistory)

// Submit votes (voters only, requires active user)
router.post('/vote', activeUserRequired, voterOnly, validateVoteSubmission, submitVotes)

export default router
