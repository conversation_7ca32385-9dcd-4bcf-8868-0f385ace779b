import mongoose from 'mongoose'

const systemSettingsSchema = new mongoose.Schema(
  {
    // Setting key (unique identifier)
    key: {
      type: String,
      required: [true, 'Setting key is required'],
      unique: true,
      index: true,
    },

    // Setting value (flexible type)
    value: {
      type: mongoose.Schema.Types.Mixed,
      required: [true, 'Setting value is required'],
    },

    // Setting type for validation
    type: {
      type: String,
      enum: {
        values: ['boolean', 'string', 'number', 'object', 'array'],
        message: 'Invalid setting type',
      },
      required: [true, 'Setting type is required'],
    },

    // Human-readable description
    description: {
      type: String,
      required: [true, 'Setting description is required'],
      maxlength: [500, 'Description cannot exceed 500 characters'],
    },

    // Category for grouping settings
    category: {
      type: String,
      required: [true, 'Setting category is required'],
      enum: {
        values: ['system', 'voting', 'display', 'security', 'notifications'],
        message: 'Invalid setting category',
      },
      index: true,
    },

    // Whether this setting is editable via admin interface
    isEditable: {
      type: Boolean,
      default: true,
    },

    // Whether this setting requires system restart
    requiresRestart: {
      type: Boolean,
      default: false,
    },

    // Last admin who modified this setting
    lastModifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      index: true,
    },

    // Modification metadata
    lastModifiedAt: {
      type: Date,
      default: Date.now,
    },

    // Default value (for reset functionality)
    defaultValue: {
      type: mongoose.Schema.Types.Mixed,
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function (doc, ret) {
        delete ret.__v
        return ret
      },
    },
  }
)

// Indexes for performance
systemSettingsSchema.index({ category: 1, key: 1 })
systemSettingsSchema.index({ isEditable: 1 })

// Static method to get setting by key
systemSettingsSchema.statics.getSetting = async function (key) {
  const setting = await this.findOne({ key })
  return setting ? setting.value : null
}

// Static method to set setting value
systemSettingsSchema.statics.setSetting = async function (key, value, adminId = null) {
  const setting = await this.findOne({ key })

  if (setting) {
    setting.value = value
    setting.lastModifiedBy = adminId
    setting.lastModifiedAt = new Date()
    await setting.save()
    return setting
  } else {
    throw new Error(`Setting with key '${key}' not found`)
  }
}

// Static method to get all settings by category
systemSettingsSchema.statics.getSettingsByCategory = function (category) {
  return this.find({ category }).sort({ key: 1 })
}

// Static method to get all editable settings
systemSettingsSchema.statics.getEditableSettings = function () {
  return this.find({ isEditable: true }).sort({ category: 1, key: 1 })
}

// Static method to reset setting to default
systemSettingsSchema.statics.resetSetting = async function (key, adminId = null) {
  const setting = await this.findOne({ key })

  if (setting && setting.defaultValue !== undefined) {
    setting.value = setting.defaultValue
    setting.lastModifiedBy = adminId
    setting.lastModifiedAt = new Date()
    await setting.save()
    return setting
  } else {
    throw new Error(`Setting with key '${key}' not found or has no default value`)
  }
}

// Static method to initialize default settings
systemSettingsSchema.statics.initializeDefaults = async function () {
  const defaultSettings = [
    {
      key: 'public_results_enabled',
      value: false,
      type: 'boolean',
      description: 'Enable public viewing of election results',
      category: 'display',
      isEditable: true,
      requiresRestart: false,
      defaultValue: false,
    },
    {
      key: 'voting_session_active',
      value: false,
      type: 'boolean',
      description: 'Whether voting session is currently active',
      category: 'voting',
      isEditable: true,
      requiresRestart: false,
      defaultValue: false,
    },
    {
      key: 'max_candidates_per_vote',
      value: 15,
      type: 'number',
      description: 'Maximum number of candidates a voter can select',
      category: 'voting',
      isEditable: true,
      requiresRestart: false,
      defaultValue: 15,
    },
    {
      key: 'system_maintenance_mode',
      value: false,
      type: 'boolean',
      description: 'Enable system maintenance mode',
      category: 'system',
      isEditable: true,
      requiresRestart: false,
      defaultValue: false,
    },
    {
      key: 'show_municipality_names',
      value: false,
      type: 'boolean',
      description: 'Show municipality names in public results',
      category: 'display',
      isEditable: true,
      requiresRestart: false,
      defaultValue: false,
    },
    {
      key: 'show_district_results',
      value: false,
      type: 'boolean',
      description: 'Show district-based results section in public results',
      category: 'display',
      isEditable: true,
      requiresRestart: false,
      defaultValue: true,
    },
  ]

  for (const settingData of defaultSettings) {
    const existingSetting = await this.findOne({ key: settingData.key })
    if (!existingSetting) {
      await this.create(settingData)
    }
  }
}

// Instance method to validate value type
systemSettingsSchema.methods.validateValueType = function () {
  const { type, value } = this

  switch (type) {
    case 'boolean':
      return typeof value === 'boolean'
    case 'string':
      return typeof value === 'string'
    case 'number':
      return typeof value === 'number'
    case 'object':
      return typeof value === 'object' && value !== null && !Array.isArray(value)
    case 'array':
      return Array.isArray(value)
    default:
      return false
  }
}

// Pre-save middleware for validation
systemSettingsSchema.pre('save', function (next) {
  if (!this.validateValueType()) {
    return next(new Error(`Value type mismatch. Expected ${this.type}, got ${typeof this.value}`))
  }
  next()
})

const SystemSettings = mongoose.model('SystemSettings', systemSettingsSchema)

export default SystemSettings
