import axios from 'axios'
import logger from '../utils/logger.js'

const API_BASE_URL = 'http://localhost:5000/api'

/**
 * Test specific credentials mentioned in requirements
 */
const verifySpecificCredentials = async () => {
  try {
    logger.info('🔑 Verifying specific credentials from requirements...')
    
    // Test credentials from requirements
    const testCredentials = [
      { username: 'admin', password: 'socmob123', expectedRole: 'admin' },
      { username: 'nabua', password: 'nabu+794', expectedRole: 'voter' },
      { username: 'balatan', password: 'bala#767', expectedRole: 'voter' },
      { username: 'cabusao', password: 'cabu=538', expectedRole: 'voter' },
      { username: 'pili', password: 'pili#519', expectedRole: 'voter' }
    ]
    
    let successCount = 0
    
    for (const cred of testCredentials) {
      try {
        logger.info(`Testing ${cred.username} / ${cred.password}...`)
        
        const response = await axios.post(`${API_BASE_URL}/auth/login`, {
          username: cred.username,
          password: cred.password
        })
        
        if (response.data.success) {
          const user = response.data.data.user
          
          logger.info(`✅ Login successful for ${cred.username}`)
          logger.info(`   Role: ${user.role}`)
          logger.info(`   Municipality: ${user.municipality || 'N/A'}`)
          logger.info(`   District: ${user.district || 'N/A'}`)
          logger.info(`   Active: ${user.isActive}`)
          
          if (user.role === cred.expectedRole) {
            logger.info(`   ✅ Role matches expected: ${cred.expectedRole}`)
            successCount++
          } else {
            logger.error(`   ❌ Role mismatch: expected ${cred.expectedRole}, got ${user.role}`)
          }
        } else {
          logger.error(`❌ Login failed for ${cred.username}: ${response.data.error}`)
        }
        
      } catch (error) {
        logger.error(`❌ Login error for ${cred.username}:`, error.response?.data || error.message)
      }
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500))
    }
    
    logger.info('📊 Credential Verification Results:')
    logger.info(`   ✅ Successful logins: ${successCount}/${testCredentials.length}`)
    logger.info(`   ❌ Failed logins: ${testCredentials.length - successCount}/${testCredentials.length}`)
    
    if (successCount === testCredentials.length) {
      logger.info('🎉 All specific credentials verified successfully!')
      return true
    } else {
      logger.error('💥 Some credential verifications failed!')
      return false
    }
    
  } catch (error) {
    logger.error('Credential verification failed:', error)
    return false
  }
}

// Run verification
verifySpecificCredentials()
  .then(success => {
    if (success) {
      logger.info('🎉 All specific credentials from requirements are working!')
      process.exit(0)
    } else {
      logger.error('💥 Some credentials from requirements are not working!')
      process.exit(1)
    }
  })
  .catch(error => {
    logger.error('Verification execution failed:', error)
    process.exit(1)
  })
