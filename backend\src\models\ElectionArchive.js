import mongoose from 'mongoose'

const electionArchiveSchema = new mongoose.Schema(
  {
    year: {
      type: Number,
      required: false, // Keep for backward compatibility
      unique: false, // Remove unique constraint
      min: 2020,
      max: 2100,
    },
    title: {
      type: String,
      required: true,
      minlength: 3,
      maxlength: 100,
      trim: true,
    },
    description: {
      type: String,
      default: '',
      maxlength: 500,
    },
    results: [
      {
        rank: {
          type: Number,
          required: true,
        },
        municipalityName: {
          type: String,
          required: true,
        },
        district: {
          type: String,
          required: true,
        },
        voteCount: {
          type: Number,
          required: true,
          min: 0,
        },
      },
    ],
    statistics: {
      totalVoters: {
        type: Number,
        required: true,
        min: 0,
      },
      totalVotesCast: {
        type: Number,
        required: true,
        min: 0,
      },
      participationRate: {
        type: Number,
        required: true,
        min: 0,
        max: 100,
      },
      totalCandidates: {
        type: Number,
        required: true,
        min: 0,
      },
      totalActiveCandidates: {
        type: Number,
        default: 0,
        min: 0,
      },
      totalInactiveCandidates: {
        type: Number,
        default: 0,
        min: 0,
      },
      electionStartTime: {
        type: Date,
      },
      electionEndTime: {
        type: Date,
      },
    },
    // Anonymized vote records for audit purposes
    voteRecords: [
      {
        voterDistrict: {
          type: String,
          required: true,
        },
        voterMunicipality: {
          type: String,
          required: true,
        },
        votedAt: {
          type: Date,
          required: true,
        },
      },
    ],
    // Complete candidate list at time of archiving
    candidateList: [
      {
        municipalityName: {
          type: String,
          required: true,
        },
        district: {
          type: String,
          required: true,
        },
        totalVotes: {
          type: Number,
          required: true,
          min: 0,
        },
        isActive: {
          type: Boolean,
          required: true,
        },
      },
    ],
    // System settings at time of election
    systemSettings: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
    },
    // Archive filename in format "Month DD, YYYY"
    filename: {
      type: String,
      required: true,
    },
    archivedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    archivedAt: {
      type: Date,
      required: true,
      default: Date.now,
    },
  },
  {
    timestamps: true,
    collection: 'election_archives',
  }
)

// Indexes
electionArchiveSchema.index({ year: 1 })
electionArchiveSchema.index({ archivedAt: -1 })
electionArchiveSchema.index({ archivedBy: 1 })

// Static methods
electionArchiveSchema.statics.getArchivesByYear = function (startYear, endYear) {
  const query = {}
  if (startYear) query.year = { $gte: startYear }
  if (endYear) query.year = { ...query.year, $lte: endYear }

  return this.find(query)
    .populate('archivedBy', 'username municipality')
    .sort({ year: -1 })
}

electionArchiveSchema.statics.getLatestArchive = function () {
  return this.findOne({})
    .populate('archivedBy', 'username municipality')
    .sort({ year: -1 })
}

electionArchiveSchema.statics.getArchiveByYear = function (year) {
  return this.findOne({ year })
    .populate('archivedBy', 'username municipality')
}

// Instance methods
electionArchiveSchema.methods.getTopCandidates = function (limit = 35) {
  return this.results
    .sort((a, b) => a.rank - b.rank)
    .slice(0, limit)
}

electionArchiveSchema.methods.getResultsByDistrict = function () {
  return this.results.reduce((acc, result) => {
    if (!acc[result.district]) {
      acc[result.district] = []
    }
    acc[result.district].push(result)
    return acc
  }, {})
}

electionArchiveSchema.methods.getWinnersByDistrict = function () {
  const resultsByDistrict = this.getResultsByDistrict()
  const winners = {}

  Object.keys(resultsByDistrict).forEach(district => {
    const districtResults = resultsByDistrict[district]
    winners[district] = districtResults.sort((a, b) => a.rank - b.rank)[0]
  })

  return winners
}

// Virtual for formatted year
electionArchiveSchema.virtual('formattedYear').get(function () {
  return `${this.year} Election`
})

// Virtual for summary
electionArchiveSchema.virtual('summary').get(function () {
  return {
    year: this.year,
    description: this.description,
    totalCandidates: this.statistics.totalCandidates,
    totalVotesCast: this.statistics.totalVotesCast,
    participationRate: this.statistics.participationRate,
    archivedAt: this.archivedAt,
    archivedBy: this.archivedBy
  }
})

// Ensure virtuals are included in JSON output
electionArchiveSchema.set('toJSON', { virtuals: true })
electionArchiveSchema.set('toObject', { virtuals: true })

const ElectionArchive = mongoose.model('ElectionArchive', electionArchiveSchema)

export default ElectionArchive
