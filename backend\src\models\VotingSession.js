import mongoose from 'mongoose'

const votingSessionSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, 'Session name is required'],
      trim: true,
      maxlength: [100, 'Session name cannot exceed 100 characters'],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, 'Description cannot exceed 500 characters'],
    },
    currentRound: {
      type: Number,
      required: [true, 'Current round is required'],
      min: [1, 'Round must be at least 1'],
      default: 1,
      index: true,
    },
    status: {
      type: String,
      enum: {
        values: ['not-started', 'round1', 'tiebreaker', 'completed', 'cancelled', 'paused'],
        message: 'Invalid session status'
      },
      default: 'not-started',
      index: true,
    },
    // Timing information
    startedAt: {
      type: Date,
      index: true,
    },
    endedAt: {
      type: Date,
      index: true,
    },
    round1StartedAt: {
      type: Date,
    },
    round1EndedAt: {
      type: Date,
    },
    tieBreakerStartedAt: {
      type: Date,
    },
    tieBreakerEndedAt: {
      type: Date,
    },
    // Voting configuration
    maxCandidatesPerVote: {
      type: Number,
      default: 15,
      min: [1, 'Must allow at least 1 candidate selection'],
      max: [50, 'Cannot exceed 50 candidate selections'],
    },
    totalVoters: {
      type: Number,
      required: [true, 'Total voters is required'],
      min: [1, 'Must have at least 1 voter'],
    },
    votesSubmitted: {
      type: Number,
      default: 0,
      min: [0, 'Votes submitted cannot be negative'],
    },
    // Results configuration
    topCandidatesCount: {
      type: Number,
      default: 15,
      min: [1, 'Must select at least 1 top candidate'],
    },
    // Tie-breaker configuration
    tieBreakerEnabled: {
      type: Boolean,
      default: true,
    },

    // Admin controls
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Creator is required'],
    },
    managedBy: [{
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
      },
      role: {
        type: String,
        enum: ['admin', 'moderator'],
        default: 'admin',
      },
      addedAt: {
        type: Date,
        default: Date.now,
      },
    }],
    // Session settings
    settings: {
      allowVoteChanges: {
        type: Boolean,
        default: false,
      },
      showLiveResults: {
        type: Boolean,
        default: false,
      },
      anonymizeResults: {
        type: Boolean,
        default: true,
      },
      requireAllVoters: {
        type: Boolean,
        default: false,
      },

    },
    // Statistics
    statistics: {
      totalVotesCast: {
        type: Number,
        default: 0,
      },
      participationRate: {
        type: Number,
        default: 0,
        min: 0,
        max: 100,
      },
      averageVotesPerVoter: {
        type: Number,
        default: 0,
      },
      votingDuration: {
        type: Number, // in milliseconds
        default: 0,
      },

    },
    // Audit trail
    auditLog: [{
      action: {
        type: String,
        required: true,
      },
      performedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
      },
      timestamp: {
        type: Date,
        default: Date.now,
      },
      details: {
        type: mongoose.Schema.Types.Mixed,
      },
      ipAddress: String,
    }],
  },
  {
    timestamps: true,
    toJSON: {
      transform: function(doc, ret) {
        delete ret.__v
        delete ret.auditLog
        return ret
      },
    },
  }
)

// Indexes
votingSessionSchema.index({ status: 1, createdAt: -1 })
votingSessionSchema.index({ createdBy: 1 })
votingSessionSchema.index({ startedAt: 1, endedAt: 1 })

// Virtual for session duration
votingSessionSchema.virtual('duration').get(function() {
  if (this.startedAt && this.endedAt) {
    return this.endedAt.getTime() - this.startedAt.getTime()
  }
  return null
})

// Virtual for current participation rate
votingSessionSchema.virtual('currentParticipationRate').get(function() {
  if (this.totalVoters === 0) return 0
  return Math.round((this.votesSubmitted / this.totalVoters) * 100)
})

// Virtual for is active
votingSessionSchema.virtual('isActive').get(function() {
  return ['round1', 'tiebreaker'].includes(this.status)
})

// Instance method to start session
votingSessionSchema.methods.startSession = function(startedBy) {
  this.status = 'round1'
  this.startedAt = new Date()
  this.round1StartedAt = new Date()
  this.currentRound = 1

  this.addAuditLog('session_started', startedBy, { round: 1 })

  return this.save()
}



// Instance method to complete tie-breaker
votingSessionSchema.methods.completeTieBreaker = function(tieBreakerRef, completedBy) {
  const tieBreakerRound = this.tieBreakerRounds.find(tb =>
    tb.tieBreakerRef && tb.tieBreakerRef.toString() === tieBreakerRef.toString()
  )

  if (tieBreakerRound) {
    tieBreakerRound.status = 'completed'
    tieBreakerRound.completedAt = new Date()
  }

  this.addAuditLog('tiebreaker_completed', completedBy, { tieBreakerRef })

  return this.save()
}

// Instance method to complete session
votingSessionSchema.methods.completeSession = function(completedBy) {
  this.status = 'completed'
  this.endedAt = new Date()
  this.tieBreakerEndedAt = new Date()

  // Calculate final statistics
  this.statistics.participationRate = this.currentParticipationRate
  if (this.startedAt && this.endedAt) {
    this.statistics.votingDuration = this.endedAt.getTime() - this.startedAt.getTime()
  }

  this.addAuditLog('session_completed', completedBy)

  return this.save()
}

// Instance method to add audit log entry
votingSessionSchema.methods.addAuditLog = function(action, performedBy, details = {}, ipAddress = null) {
  this.auditLog.push({
    action,
    performedBy,
    timestamp: new Date(),
    details,
    ipAddress,
  })
}

// Instance method to update statistics
votingSessionSchema.methods.updateStatistics = async function() {
  const Vote = mongoose.model('Vote')

  // Get voting statistics
  const stats = await Vote.getVotingStatistics(this.currentRound, 'regular')

  if (stats.length > 0) {
    const stat = stats[0]
    this.statistics.totalVotesCast = stat.totalVotes
    this.statistics.participationRate = Math.round((stat.uniqueVoterCount / this.totalVoters) * 100)
    this.statistics.averageVotesPerVoter = stat.totalVotes / stat.uniqueVoterCount
    this.votesSubmitted = stat.uniqueVoterCount
  }

  return this.save()
}



// Static method to get current active session
votingSessionSchema.statics.getCurrentSession = function() {
  return this.findOne({
    status: 'round1'
  }).populate('createdBy', 'username municipality')
}

// Static method to create new session
votingSessionSchema.statics.createSession = function(data) {
  const {
    name,
    description,
    createdBy,
    totalVoters,
    maxCandidatesPerVote = 15,
    topCandidatesCount = 15,
    settings = {},
  } = data

  return this.create({
    name,
    description,
    createdBy,
    totalVoters,
    maxCandidatesPerVote,
    topCandidatesCount,
    settings: {
      allowVoteChanges: false,
      showLiveResults: false,
      anonymizeResults: true,
      requireAllVoters: false,
      ...settings,
    },
    managedBy: [{
      userId: createdBy,
      role: 'admin',
    }],
  })
}

// Static method to get session statistics
votingSessionSchema.statics.getSessionStatistics = function(sessionId) {
  return this.findById(sessionId)
    .populate('createdBy', 'username municipality')
    .populate('managedBy.userId', 'username municipality')
}

// Pre-save middleware to update statistics
votingSessionSchema.pre('save', function(next) {
  // Update participation rate
  if (this.totalVoters > 0) {
    this.statistics.participationRate = Math.round((this.votesSubmitted / this.totalVoters) * 100)
  }

  next()
})

const VotingSession = mongoose.model('VotingSession', votingSessionSchema)

export default VotingSession
