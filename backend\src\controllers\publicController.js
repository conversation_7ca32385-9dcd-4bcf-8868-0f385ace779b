import Candidate from '../models/Candidate.js'
import SystemSettings from '../models/SystemSettings.js'
import User from '../models/User.js'
import logger from '../utils/logger.js'

/**
 * @desc    Get public results status
 * @route   GET /api/results/status
 * @access  Public
 */
export const getPublicResultsStatus = async (req, res) => {
  try {
    const enabled = await SystemSettings.getSetting('public_results_enabled')

    res.status(200).json({
      success: true,
      data: {
        enabled: enabled || false,
      },
    })

  } catch (error) {
    logger.error('Get public results status error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error fetching public results status',
    })
  }
}

/**
 * @desc    Get public voting results (if enabled)
 * @route   GET /api/results
 * @access  Public
 */
export const getPublicResults = async (req, res) => {
  try {
    const enabled = await SystemSettings.getSetting('public_results_enabled')

    if (!enabled) {
      return res.status(200).json({
        success: true,
        data: {
          available: false,
          message: 'Results will be available later',
          statusMessage: 'Results are not currently available to the public'
        }
      })
    }

    // Check display settings
    const [showMunicipalityNames, showDistrictResults] = await Promise.all([
      SystemSettings.getSetting('show_municipality_names'),
      SystemSettings.getSetting('show_district_results')
    ])

    // Get all 35 candidates (both active and inactive)
    const allCandidates = await Candidate.find({})
      .select('municipalityName district totalVotes isActive')
      .sort({ totalVotes: -1, municipalityName: 1 })

    // Get inactive users to set their vote counts to zero
    const inactiveUsers = await User.find({
      role: 'voter',
      isActive: false
    }).select('municipality')

    const inactiveMunicipalities = new Set(inactiveUsers.map(user => user.municipality))

    // Process candidates to show zero votes for inactive users
    const candidates = allCandidates.map(candidate => ({
      ...candidate.toObject(),
      totalVotes: inactiveMunicipalities.has(candidate.municipalityName) ? 0 : candidate.totalVotes
    })).sort((a, b) => b.totalVotes - a.totalVotes || a.municipalityName.localeCompare(b.municipalityName))

    // Create unified ranking (1-35 based on total votes)
    const unifiedResults = candidates.map((candidate, index) => ({
      municipalityName: showMunicipalityNames ? candidate.municipalityName : `Candidate ${index + 1}`,
      district: candidate.district,
      voteCount: candidate.totalVotes,
      rank: index + 1,
      candidateName: showMunicipalityNames ? candidate.municipalityName : undefined,
    }))

    // Group results by district
    const resultsByDistrict = candidates.reduce((acc, candidate, index) => {
      const district = candidate.district
      if (!acc[district]) {
        acc[district] = []
      }

      const result = {
        municipalityName: showMunicipalityNames ? candidate.municipalityName : `Candidate ${index + 1}`,
        district: candidate.district,
        voteCount: candidate.totalVotes,
        rank: index + 1, // Overall rank
        candidateName: showMunicipalityNames ? candidate.municipalityName : undefined,
      }

      acc[district].push(result)
      return acc
    }, {})

    // Get comprehensive statistics
    const [totalVoters, totalVotesCast] = await Promise.all([
      User.countDocuments({ role: 'voter' }),
      User.countDocuments({ role: 'voter', hasVoted: true })
    ])

    const totalCandidates = candidates.length // All 35 candidates
    const totalActiveCandidates = candidates.filter(c => !inactiveMunicipalities.has(c.municipalityName)).length
    const totalInactiveCandidates = candidates.filter(c => inactiveMunicipalities.has(c.municipalityName)).length

    const participationRate = totalVoters > 0 ? (totalVotesCast / totalVoters) * 100 : 0

    res.status(200).json({
      success: true,
      data: {
        available: true,
        message: 'Results are now available',
        statusMessage: 'Election results are publicly available',
        statistics: {
          participationRate: Math.round(participationRate * 100) / 100,
          totalVotesCast,
          totalRegisteredVoters: totalVoters,
          totalCandidates: totalCandidates, // All 35 candidates
          totalActiveCandidates: totalActiveCandidates,
          totalAbsentParticipants: totalInactiveCandidates
        },
        results: {
          unifiedResults,
          resultsByDistrict,
          showMunicipalityNames,
          showDistrictResults
        }
      }
    })

  } catch (error) {
    logger.error('Get public results error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error fetching public results',
    })
  }
}
