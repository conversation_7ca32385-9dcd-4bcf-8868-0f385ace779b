import { InactiveUserMessage } from '@/components/InactiveUserMessage'
import { Header } from '@/components/organisms/Header'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { VoteReceipt } from '@/components/voting/VoteReceipt'
import { VotingInstructions } from '@/components/voting/VotingInstructions'
import { VotingInterface } from '@/components/voting/VotingInterface'
import { useAuth } from '@/hooks/useAuth'
import { useElectionAccess } from '@/hooks/useSystemSettings'
import { useVotingEligibility, useVotingStatus } from '@/hooks/useVoting'
import { VoterDashboard } from '@/pages/voter/VoterDashboard'
import { AlertCircle, CheckCircle, Clock, Lock, Users } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'

// Removed inline VotingNavigation component - using standardized Header component instead

const VotingPage = () => {
  const navigate = useNavigate()
  const { user } = useAuth()
  const { data: votingStatus, isLoading: statusLoading } = useVotingStatus()
  const eligibility = useVotingEligibility()
  const { canAccessVoting, isLoading: electionLoading } = useElectionAccess()
  const [showDashboard, setShowDashboard] = useState(false)
  const [voteReceiptData, setVoteReceiptData] = useState<any>(null)

  // Redirect if not authenticated or not a voter
  useEffect(() => {
    if (!user) {
      navigate('/login')
      return
    }

    if (user.role !== 'voter' && user.role !== 'admin' && user.role !== 'execom') {
      navigate('/')
      return
    }
  }, [user, navigate])

  // Logout is now handled by the Header component

  // Check if user is inactive - block voting access
  if (user && !user.isActive) {
    return <InactiveUserMessage onGoHome={() => navigate('/')} />
  }

  // Show vote receipt if vote was submitted
  if (voteReceiptData) {
    return (
      <VoteReceipt
        voteData={voteReceiptData}
        onViewResults={() => navigate('/results')}
        onGoHome={() => setVoteReceiptData(null)}
      />
    )
  }

  // Handle successful vote submission
  const handleVoteSubmitted = (submissionData?: any) => {
    // Create receipt data from submission response
    const receiptData = {
      submittedAt: submissionData?.submittedAt || new Date().toISOString(),
      candidateCount: submissionData?.votesCount || 0,
      batchId: submissionData?.batchId || 'N/A',
      votedCandidates: submissionData?.votedCandidates || [],
      voterInfo: {
        username: user?.username || '',
        municipality: user?.municipality || '',
        district: user?.district || ''
      }
    }

    setVoteReceiptData(receiptData)
  }

  if (statusLoading || electionLoading || !votingStatus) {
    return (
      <div className='min-h-screen bg-background py-8'>
        <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
          <div className='space-y-6'>
            <Skeleton className='h-12 w-64' />
            <Skeleton className='h-32 w-full' />
            <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
              {Array.from({ length: 6 }).map((_, i) => (
                <Skeleton key={i} className='h-48' />
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Show election closed message if voting is not accessible
  if (!canAccessVoting) {
    return (
      <div className='min-h-screen bg-background py-8'>
        <div className='mx-auto max-w-4xl px-4 sm:px-6 lg:px-8'>
          <div className='space-y-6'>


            {/* Election Closed Card */}
            <Card className='mx-auto max-w-2xl border-red-200 bg-red-50'>
              <CardHeader className='text-center'>
                <div className='mb-4 flex justify-center'>
                  <Lock className='h-16 w-16 text-red-600' />
                </div>
                <CardTitle className='text-xl text-red-800'>Election Currently Closed</CardTitle>
              </CardHeader>
              <CardContent className='space-y-4 text-center'>
                <p className='text-red-700'>
                  Voting is currently disabled by the system administrator. Please check back later
                  or contact your administrator for more information.
                </p>

                {/* User info */}
                <div className='rounded-lg bg-white p-4'>
                  <div className='mb-2 flex items-center justify-center space-x-2'>
                    <Users className='h-4 w-4' />
                    <span className='font-medium'>{user?.municipality}</span>
                  </div>
                  <Badge variant='secondary'>{user?.role}</Badge>
                </div>

                {/* Action buttons */}
                <div className='flex flex-col justify-center gap-3 sm:flex-row'>
                  <Button variant='outline' onClick={() => navigate('/results')}>
                    View Results
                  </Button>

                  <Button variant='outline' onClick={() => navigate('/')}>
                    Back to Home
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  // Show voting status card if user cannot vote
  if (!eligibility.canVote) {
    return (
      <div className='min-h-screen bg-background py-8'>
        <div className='mx-auto max-w-4xl px-4 sm:px-6 lg:px-8'>
          <div className='space-y-6'>


            {/* Status Card */}
            <Card className='mx-auto max-w-2xl'>
              <CardHeader className='text-center'>
                <div className='mb-4 flex justify-center'>
                  {votingStatus.hasVoted ? (
                    <CheckCircle className='h-16 w-16 text-green-600' />
                  ) : votingStatus.votingStatus === 'no-active-session' ? (
                    <Clock className='h-16 w-16 text-orange-600' />
                  ) : (
                    <AlertCircle className='h-16 w-16 text-red-600' />
                  )}
                </div>
                <CardTitle className='text-xl'>
                  {votingStatus.hasVoted ? 'Vote Submitted' : 'Voting Not Available'}
                </CardTitle>
              </CardHeader>
              <CardContent className='space-y-4 text-center'>
                <p className='text-muted-foreground'>{eligibility.reason}</p>

                {/* User info */}
                <div className='rounded-lg bg-muted p-4'>
                  <div className='mb-2 flex items-center justify-center space-x-2'>
                    <Users className='h-4 w-4' />
                    <span className='font-medium'>{user?.municipality}</span>
                  </div>
                  <Badge variant='secondary'>{user?.role}</Badge>
                </div>

                {/* Action buttons */}
                <div className='flex flex-col justify-center gap-3 sm:flex-row'>
                  <Button variant='outline' onClick={() => navigate('/results')}>
                    View Results
                  </Button>

                  <Button variant='outline' onClick={() => navigate('/')}>
                    Back to Home
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }



  // Show dashboard if requested, otherwise show voting interface directly
  if (showDashboard) {
    return <VoterDashboard onStartVoting={() => setShowDashboard(false)} />
  }

  // Main voting interface with standardized header
  return (
    <div className='min-h-screen bg-background'>
      <Header />

      <div className='py-8'>
        <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
          <div className='space-y-6'>
            {/* Always show voting interface - remove inactive voting cards */}
                {/* Enhanced Voting Instructions with Auto-Dismiss */}
                <VotingInstructions
                  maxCandidatesPerVote={votingStatus.maxCandidatesPerVote}
                  autoHideAfter={7}
                />

                {/* Voting Interface */}
                <VotingInterface
                  maxSelections={votingStatus.maxCandidatesPerVote}
                  onVoteSubmitted={handleVoteSubmitted}
                />
          </div>
        </div>
      </div>
    </div>
  )
}

export default VotingPage
