import dotenv from 'dotenv'
import mongoose from 'mongoose'
import logger from '../utils/logger.js'

// Load environment variables
dotenv.config()

// MongoDB connection
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URI
    if (!mongoUri) {
      throw new Error('MongoDB URI not found in environment variables')
    }
    const conn = await mongoose.connect(mongoUri)
    logger.info(`MongoDB Connected: ${conn.connection.host}`)
  } catch (error) {
    logger.error('Database connection error:', error)
    process.exit(1)
  }
}

/**
 * Migration script to remove isPresent field from User documents
 * This script updates the database schema to match the simplified user status model
 */
const migrateUserSchema = async () => {
  try {
    logger.info('Starting user schema migration...')

    // Connect to database
    await connectDB()

    // Get the users collection directly
    const db = mongoose.connection.db
    const usersCollection = db.collection('users')

    // Check how many users have the isPresent field
    const usersWithPresent = await usersCollection.countDocuments({
      isPresent: { $exists: true }
    })

    logger.info(`Found ${usersWithPresent} users with isPresent field`)

    if (usersWithPresent === 0) {
      logger.info('✅ No users found with isPresent field. Migration not needed.')
      return
    }

    // Remove isPresent field from all user documents
    const result = await usersCollection.updateMany(
      { isPresent: { $exists: true } },
      { $unset: { isPresent: "" } }
    )

    logger.info(`✅ Migration completed successfully`)
    logger.info(`- Documents modified: ${result.modifiedCount}`)
    logger.info(`- Documents matched: ${result.matchedCount}`)

    // Verify the migration
    const remainingUsers = await usersCollection.countDocuments({
      isPresent: { $exists: true }
    })

    if (remainingUsers === 0) {
      logger.info('✅ Verification passed: No users have isPresent field')
    } else {
      logger.warn(`⚠️  Warning: ${remainingUsers} users still have isPresent field`)
    }

  } catch (error) {
    logger.error('Migration failed:', error)
    throw error
  } finally {
    await mongoose.connection.close()
    logger.info('Database connection closed')
  }
}

/**
 * Rollback function to restore isPresent field (if needed for testing)
 */
const rollbackMigration = async () => {
  try {
    logger.info('Starting migration rollback...')

    // Connect to database
    await connectDB()

    // Get the users collection directly
    const db = mongoose.connection.db
    const usersCollection = db.collection('users')

    // Add isPresent field back to all users (default to true)
    const result = await usersCollection.updateMany(
      { isPresent: { $exists: false } },
      { $set: { isPresent: true } }
    )

    logger.info(`✅ Rollback completed successfully`)
    logger.info(`- Documents modified: ${result.modifiedCount}`)
    logger.info(`- Documents matched: ${result.matchedCount}`)

  } catch (error) {
    logger.error('Rollback failed:', error)
    throw error
  } finally {
    await mongoose.connection.close()
    logger.info('Database connection closed')
  }
}

/**
 * Display current user schema status
 */
const checkSchemaStatus = async () => {
  try {
    logger.info('Checking user schema status...')

    // Connect to database
    await connectDB()

    // Get the users collection directly
    const db = mongoose.connection.db
    const usersCollection = db.collection('users')

    const totalUsers = await usersCollection.countDocuments()
    const usersWithPresent = await usersCollection.countDocuments({
      isPresent: { $exists: true }
    })
    const usersWithoutPresent = totalUsers - usersWithPresent

    logger.info('📊 User Schema Status:')
    logger.info(`- Total users: ${totalUsers}`)
    logger.info(`- Users with isPresent field: ${usersWithPresent}`)
    logger.info(`- Users without isPresent field: ${usersWithoutPresent}`)

    if (usersWithPresent === 0) {
      logger.info('✅ Schema is up to date (no isPresent fields)')
    } else {
      logger.info('⚠️  Schema needs migration (isPresent fields found)')
    }

  } catch (error) {
    logger.error('Schema check failed:', error)
    throw error
  } finally {
    await mongoose.connection.close()
    logger.info('Database connection closed')
  }
}

// Command line interface
const command = process.argv[2]

switch (command) {
  case 'migrate':
    migrateUserSchema()
      .then(() => {
        logger.info('User schema migration completed successfully')
        process.exit(0)
      })
      .catch((error) => {
        logger.error('User schema migration failed:', error)
        process.exit(1)
      })
    break

  case 'rollback':
    rollbackMigration()
      .then(() => {
        logger.info('Migration rollback completed successfully')
        process.exit(0)
      })
      .catch((error) => {
        logger.error('Migration rollback failed:', error)
        process.exit(1)
      })
    break

  case 'status':
    checkSchemaStatus()
      .then(() => {
        logger.info('Schema status check completed')
        process.exit(0)
      })
      .catch((error) => {
        logger.error('Schema status check failed:', error)
        process.exit(1)
      })
    break

  default:
    console.log('Usage:')
    console.log('  node src/scripts/migrateUserSchema.js migrate   - Remove isPresent field from all users')
    console.log('  node src/scripts/migrateUserSchema.js rollback  - Add isPresent field back to all users')
    console.log('  node src/scripts/migrateUserSchema.js status    - Check current schema status')
    process.exit(1)
}
