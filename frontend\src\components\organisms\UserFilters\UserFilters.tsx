import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { UserFilters as UserFiltersType } from '@/schemas/user'
import { Filter, RotateCcw } from 'lucide-react'
import { Button } from '../../atoms/Button'
import { SearchBox } from '../../molecules/SearchBox'

export interface UserFiltersProps {
  filters: UserFiltersType
  onFiltersChange: (filters: Partial<UserFiltersType>) => void
  onReset: () => void
  districts?: string[]
  municipalities?: string[]
  loading?: boolean
  className?: string
}

export function UserFilters({
  filters,
  onFiltersChange,
  onReset,
  districts = [],
  municipalities = [],
  loading = false,
  className,
}: UserFiltersProps) {
  const handleSearchChange = (search: string) => {
    onFiltersChange({ search, page: 1 })
  }

  const handleFilterChange = (key: keyof UserFiltersType, value: any) => {
    onFiltersChange({ [key]: value === 'all' ? undefined : value, page: 1 })
  }

  const hasActiveFilters = !!(
    filters.search ||
    filters.role ||
    filters.municipality ||
    filters.district ||
    filters.hasVoted !== undefined ||
    filters.isActive !== undefined
  )

  return (
    <div className={`space-y-4 ${className || ''}`}>
      {/* Search and Reset */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex-1 max-w-md">
          <SearchBox
            placeholder="Search users by name, municipality..."
            value={filters.search || ''}
            onSearch={handleSearchChange}
            disabled={loading}
            loading={loading}
          />
        </div>

        {hasActiveFilters && (
          <Button
            variant="outline"
            size="sm"
            onClick={onReset}
            disabled={loading}
            leftIcon={<RotateCcw className="h-4 w-4" />}
          >
            Reset Filters
          </Button>
        )}
      </div>

      {/* Filter Controls */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {/* Role Filter - Simplified to only admin and voter */}
        <Select
          value={filters.role || 'all'}
          onValueChange={(value) => handleFilterChange('role', value)}
          disabled={loading}
        >
          <SelectTrigger>
            <SelectValue placeholder="Filter by role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All roles</SelectItem>
            <SelectItem value="voter">Voter</SelectItem>
            <SelectItem value="admin">Admin</SelectItem>
          </SelectContent>
        </Select>

        {/* Voting Status Filter */}
        <Select
          value={filters.hasVoted?.toString() || 'all'}
          onValueChange={(value) =>
            handleFilterChange('hasVoted', value === 'all' ? undefined : value === 'true')
          }
          disabled={loading}
        >
          <SelectTrigger>
            <SelectValue placeholder="Voting status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All users</SelectItem>
            <SelectItem value="true">Voted</SelectItem>
            <SelectItem value="false">Not voted</SelectItem>
          </SelectContent>
        </Select>

        {/* Active Status Filter */}
        <Select
          value={filters.isActive?.toString() || 'all'}
          onValueChange={(value) =>
            handleFilterChange('isActive', value === 'all' ? undefined : value === 'true')
          }
          disabled={loading}
        >
          <SelectTrigger>
            <SelectValue placeholder="Account status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All accounts</SelectItem>
            <SelectItem value="true">Active</SelectItem>
            <SelectItem value="false">Inactive</SelectItem>
          </SelectContent>
        </Select>

        {/* District Filter */}
        <Select
          value={filters.district || 'all'}
          onValueChange={(value) => handleFilterChange('district', value)}
          disabled={loading}
        >
          <SelectTrigger>
            <SelectValue placeholder="Filter by district" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All districts</SelectItem>
            {districts.map((district) => (
              <SelectItem key={district} value={district}>
                {district}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>


      </div>

      {/* Sort Controls - Simplified */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Sort by:</span>
        </div>

        <div className="flex gap-2">
          <Select
            value={filters.sort || 'username'}
            onValueChange={(value) => handleFilterChange('sort', value)}
            disabled={loading}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="username">Username</SelectItem>
              <SelectItem value="municipality">Municipality</SelectItem>
              <SelectItem value="lastLogin">Last Login</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.order || 'asc'}
            onValueChange={(value) => handleFilterChange('order', value)}
            disabled={loading}
          >
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="asc">A-Z</SelectItem>
              <SelectItem value="desc">Z-A</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  )
}

export default UserFilters
