import { validationResult } from 'express-validator'
import jwt from 'jsonwebtoken'
import User from '../models/User.js'
import ActivityLogService from '../services/activityLogService.js'
import logger from '../utils/logger.js'

// Helper function to send token response
const sendTokenResponse = async (user, statusCode, res) => {
  try {
    // Generate tokens
    const token = user.generateAuthToken()
    const refreshToken = user.generateRefreshToken()

    // Save refresh token to user
    user.refreshTokens.push({ token: refreshToken })

    // Keep only last 5 refresh tokens
    if (user.refreshTokens.length > 5) {
      user.refreshTokens = user.refreshTokens.slice(-5)
    }

    await user.save()

    // Set refresh token as httpOnly cookie
    const cookieOptions = {
      expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
    }

    res.cookie('refreshToken', refreshToken, cookieOptions)

    res.status(statusCode).json({
      success: true,
      data: {
        user: {
          id: user._id,
          username: user.username,
          municipality: user.municipality,
          district: user.district,
          role: user.role,
          hasVoted: user.hasVoted,
          isActive: user.isActive,
          email: user.email,
          lastLogin: user.lastLogin,
        },
        token,
      },
    })
  } catch (error) {
    logger.error('Error in sendTokenResponse:', error)
    res.status(500).json({
      success: false,
      error: 'Server error during authentication',
    })
  }
}

/**
 * @desc    Login user
 * @route   POST /api/auth/login
 * @access  Public
 */
export const login = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array(),
      })
    }

    const { username, password } = req.body

    // Find user and validate credentials
    const user = await User.findByCredentials(username, password)

    logger.info(`User ${user.username} logged in successfully`)

    // Log successful login activity
    await ActivityLogService.logLogin(user, req.ip, req.get('User-Agent'), true)

    sendTokenResponse(user, 200, res)
  } catch (error) {
    logger.error('Login error:', error.message)

    if (error.message.includes('Invalid login credentials')) {
      return res.status(401).json({
        success: false,
        error: 'Invalid username or password',
      })
    }

    if (error.message.includes('Account temporarily locked')) {
      return res.status(423).json({
        success: false,
        error:
          'Account temporarily locked due to too many failed login attempts. Please try again later.',
      })
    }

    res.status(500).json({
      success: false,
      error: 'Server error during login',
    })
  }
}

/**
 * @desc    Get current logged in user
 * @route   GET /api/auth/me
 * @access  Private
 */
export const getMe = async (req, res) => {
  try {
    const user = await User.findById(req.user.id)

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      })
    }

    res.status(200).json({
      success: true,
      data: {
        user: {
          id: user._id,
          username: user.username,
          municipality: user.municipality,
          role: user.role,
          hasVoted: user.hasVoted,
          lastLogin: user.lastLogin,
          createdAt: user.createdAt,
        },
      },
    })
  } catch (error) {
    logger.error('Get me error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error',
    })
  }
}

/**
 * @desc    Refresh access token
 * @route   POST /api/auth/refresh
 * @access  Public
 */
export const refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.cookies

    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        error: 'Refresh token not provided',
      })
    }

    // Verify refresh token
    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET)

    // Find user and check if refresh token exists
    const user = await User.findById(decoded.id)
    if (!user || !user.refreshTokens.some(tokenObj => tokenObj.token === refreshToken)) {
      return res.status(401).json({
        success: false,
        error: 'Invalid refresh token',
      })
    }

    // Generate new access token
    const newToken = user.generateAuthToken()

    res.status(200).json({
      success: true,
      data: {
        token: newToken,
      },
    })
  } catch (error) {
    logger.error('Refresh token error:', error)
    res.status(401).json({
      success: false,
      error: 'Invalid refresh token',
    })
  }
}

/**
 * @desc    Logout user
 * @route   POST /api/auth/logout
 * @access  Private
 */
export const logout = async (req, res) => {
  try {
    const { refreshToken } = req.cookies

    if (refreshToken) {
      // Remove refresh token from user's tokens array
      const user = await User.findById(req.user.id)
      if (user) {
        user.refreshTokens = user.refreshTokens.filter(tokenObj => tokenObj.token !== refreshToken)
        await user.save()
      }
    }

    // Clear refresh token cookie
    res.clearCookie('refreshToken')

    logger.info(`User ${req.user.username} logged out`)

    res.status(200).json({
      success: true,
      message: 'Logged out successfully',
    })
  } catch (error) {
    logger.error('Logout error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error during logout',
    })
  }
}

/**
 * @desc    Logout from all devices
 * @route   POST /api/auth/logout-all
 * @access  Private
 */
export const logoutAll = async (req, res) => {
  try {
    const user = await User.findById(req.user.id)
    user.refreshTokens = []
    await user.save()

    res.clearCookie('refreshToken')

    logger.info(`User ${req.user.username} logged out from all devices`)

    res.status(200).json({
      success: true,
      message: 'Logged out from all devices successfully',
    })
  } catch (error) {
    logger.error('Logout all error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error',
    })
  }
}

/**
 * @desc    Change password
 * @route   PUT /api/auth/change-password
 * @access  Private
 */
export const changePassword = async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array(),
      })
    }

    const { currentPassword, newPassword } = req.body

    const user = await User.findById(req.user.id).select('+password')

    // Check current password
    const isMatch = await user.comparePassword(currentPassword)
    if (!isMatch) {
      return res.status(400).json({
        success: false,
        error: 'Current password is incorrect',
      })
    }

    // Update password
    user.password = newPassword
    await user.save()

    logger.info(`User ${user.username} changed password`)

    res.status(200).json({
      success: true,
      message: 'Password changed successfully',
    })
  } catch (error) {
    logger.error('Change password error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error',
    })
  }
}

/**
 * @desc    Admin reset user password
 * @route   PUT /api/auth/admin/reset-password/:userId
 * @access  Admin
 */
export const adminResetPassword = async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors.array().map(err => err.msg),
      })
    }

    const { userId } = req.params
    const { newPassword } = req.body
    const adminId = req.user?.id

    // Find the user to reset password for
    const user = await User.findById(userId)
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      })
    }

    // Prevent admin from resetting another admin's password
    if (user.role === 'admin' && user._id.toString() !== adminId) {
      return res.status(403).json({
        success: false,
        error: 'Cannot reset password for another admin user',
      })
    }

    // Update password
    user.password = newPassword
    await user.save()

    logger.info(`Admin ${req.user.username} reset password for user ${user.username}`, {
      component: 'AuthController',
      action: 'adminResetPassword',
      adminId,
      targetUserId: userId,
      targetUsername: user.username,
    })

    res.status(200).json({
      success: true,
      message: `Password reset successfully for user ${user.username}`,
    })
  } catch (error) {
    logger.error('Admin reset password error:', error)
    res.status(500).json({
      success: false,
      error: 'Server error',
    })
  }
}
