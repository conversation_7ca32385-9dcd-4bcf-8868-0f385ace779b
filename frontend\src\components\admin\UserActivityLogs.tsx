import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { apiClient } from '@/services/apiClient'
import {
    Activity,
    LogIn,
    LogOut,
    RefreshCw,
    Search,
    Settings,
    UserPlus,
    Vote
} from 'lucide-react'
import { useEffect, useState } from 'react'

interface ActivityLog {
  id: string
  userId: string
  username: string
  municipality: string
  action: 'login' | 'logout' | 'vote' | 'user_created' | 'settings_changed' | 'data_export'
  description: string
  timestamp: Date
  ipAddress: string
  userAgent: string
}

interface UserActivityLogsProps {
  className?: string
}

export function UserActivityLogs({ className }: UserActivityLogsProps) {
  const [logs, setLogs] = useState<ActivityLog[]>([])
  const [filteredLogs, setFilteredLogs] = useState<ActivityLog[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [actionFilter, setActionFilter] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const ITEMS_PER_PAGE = 7

  // Fetch real activity logs from API
  useEffect(() => {
    fetchActivityLogs()
  }, [])

  const fetchActivityLogs = async () => {
    try {
      setIsLoading(true)
      const response = await apiClient.get('/admin/activity-logs?limit=100')

      if (response.data.success) {
        const activities = response.data.data.activities || []
        setLogs(activities)
      } else {
        console.warn('Failed to fetch activity logs:', response.data.error)
        setLogs([])
      }
    } catch (error) {
      console.error('Error fetching activity logs:', error)
      setLogs([])
    } finally {
      setIsLoading(false)
    }
  }

  // Filter logs based on search and action filter
  useEffect(() => {
    let filtered = logs

    if (searchTerm) {
      filtered = filtered.filter(log =>
        log.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.municipality.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (actionFilter !== 'all') {
      filtered = filtered.filter(log => log.action === actionFilter)
    }

    setFilteredLogs(filtered)
    setTotalPages(Math.ceil(filtered.length / ITEMS_PER_PAGE))
    setCurrentPage(1) // Reset to first page when filters change
  }, [logs, searchTerm, actionFilter, ITEMS_PER_PAGE])

  const refreshLogs = async () => {
    await fetchActivityLogs()
  }

  const getActionIcon = (action: ActivityLog['action']) => {
    switch (action) {
      case 'login':
        return <LogIn className="h-4 w-4 text-green-600" />
      case 'logout':
        return <LogOut className="h-4 w-4 text-red-600" />
      case 'vote':
        return <Vote className="h-4 w-4 text-blue-600" />
      case 'user_created':
        return <UserPlus className="h-4 w-4 text-purple-600" />
      case 'settings_changed':
        return <Settings className="h-4 w-4 text-orange-600" />
      case 'data_export':
        return <Activity className="h-4 w-4 text-indigo-600" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  const getActionBadge = (action: ActivityLog['action']) => {
    const variants = {
      login: 'default',
      logout: 'secondary',
      vote: 'default',
      user_created: 'secondary',
      settings_changed: 'outline',
      data_export: 'outline',
    } as const

    return (
      <Badge variant={variants[action] || 'outline'} className="text-xs">
        {action.replace('_', ' ').toUpperCase()}
      </Badge>
    )
  }

  const formatTimestamp = (timestamp: Date | string) => {
    try {
      // Convert to Date object if it's a string
      const timestampDate = typeof timestamp === 'string' ? new Date(timestamp) : timestamp

      // Check if the date is valid
      if (isNaN(timestampDate.getTime())) {
        return 'Invalid date'
      }

      const now = new Date()
      const diff = now.getTime() - timestampDate.getTime()
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (minutes < 60) {
        return `${minutes}m ago`
      } else if (hours < 24) {
        return `${hours}h ago`
      } else {
        return `${days}d ago`
      }
    } catch (error) {
      console.error('Error formatting timestamp:', error)
      return 'Invalid date'
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            User Activity Logs
            <Badge variant="secondary">{filteredLogs.length}</Badge>
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={refreshLogs}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="flex gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by username, municipality, or action..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Select value={actionFilter} onValueChange={setActionFilter}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Actions</SelectItem>
              <SelectItem value="login">Login</SelectItem>
              <SelectItem value="logout">Logout</SelectItem>
              <SelectItem value="vote">Vote</SelectItem>
              <SelectItem value="user_created">User Created</SelectItem>
              <SelectItem value="settings_changed">Settings</SelectItem>
              <SelectItem value="data_export">Data Export</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Activity Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Action</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Municipality</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Time</TableHead>
                <TableHead>IP Address</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLogs.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                    {logs.length === 0 ? 'No activities yet' : 'No activity logs found'}
                  </TableCell>
                </TableRow>
              ) : (
                filteredLogs
                  .slice((currentPage - 1) * ITEMS_PER_PAGE, currentPage * ITEMS_PER_PAGE)
                  .map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getActionIcon(log.action)}
                          {getActionBadge(log.action)}
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{log.username}</TableCell>
                      <TableCell>{log.municipality}</TableCell>
                      <TableCell className="max-w-xs truncate">{log.description}</TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {formatTimestamp(log.timestamp)}
                      </TableCell>
                      <TableCell className="font-mono text-xs">{log.ipAddress}</TableCell>
                    </TableRow>
                  ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="mt-4 flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Showing {((currentPage - 1) * ITEMS_PER_PAGE) + 1} to {Math.min(currentPage * ITEMS_PER_PAGE, filteredLogs.length)} of {filteredLogs.length} activities
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="text-sm">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
