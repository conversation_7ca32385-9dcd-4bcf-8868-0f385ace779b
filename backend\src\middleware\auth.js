import jwt from 'jsonwebtoken'
import User from '../models/User.js'
import logger from '../utils/logger.js'

/**
 * Protect routes - require authentication
 */
export const protect = async (req, res, next) => {
  try {
    let token

    // Check for token in Authorization header
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1]
    }

    // Make sure token exists
    if (!token) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to access this route',
      })
    }

    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET)

      // Get user from token
      const user = await User.findById(decoded.id)

      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'No user found with this token',
        })
      }

      // Note: Removed isActive check from protect middleware
      // Inactive users can now login but will be blocked from voting by activeUserRequired middleware

      // Add user to request object
      req.user = {
        id: user._id,
        username: user.username,
        municipality: user.municipality,
        role: user.role,
        hasVoted: user.hasVoted,
        isActive: user.isActive,
      }

      next()
    } catch (error) {
      logger.error('Token verification error:', error.message)
      return res.status(401).json({
        success: false,
        error: 'Not authorized to access this route',
      })
    }
  } catch (error) {
    logger.error('Auth middleware error:', error)
    return res.status(500).json({
      success: false,
      error: 'Server error in authentication',
    })
  }
}

/**
 * Grant access to specific roles
 * @param {...string} roles - Allowed roles
 */
export const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to access this route',
      })
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: `User role '${req.user.role}' is not authorized to access this route`,
      })
    }

    next()
  }
}

/**
 * Check if user has voted (for voting routes)
 */
export const checkVotingStatus = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id)

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found',
      })
    }

    // Add voting status to request
    req.user.hasVoted = user.hasVoted
    req.user.canVote = !user.hasVoted && user.isActive

    next()
  } catch (error) {
    logger.error('Check voting status error:', error)
    return res.status(500).json({
      success: false,
      error: 'Server error checking voting status',
    })
  }
}

/**
 * Prevent voting if user has already voted
 */
export const preventDoubleVoting = (req, res, next) => {
  if (req.user.hasVoted) {
    return res.status(403).json({
      success: false,
      error: 'You have already cast your vote',
    })
  }

  next()
}

/**
 * Admin only middleware
 */
export const adminOnly = authorize('admin')

/**
 * Voter or admin middleware
 */
export const voterOrAdmin = authorize('voter', 'admin')

/**
 * Executive committee or admin middleware
 */
export const execomOrAdmin = authorize('execom', 'admin')

/**
 * Tie-breaker or admin middleware
 */
export const tieBreakerOrAdmin = authorize('tie-breaker', 'admin')

/**
 * Rate limiting for authentication routes
 */
export const authRateLimit = (req, res, next) => {
  // This will be handled by express-rate-limit in app.js
  // but we can add additional logic here if needed
  next()
}

/**
 * Optional authentication - doesn't fail if no token
 */
export const optionalAuth = async (req, res, next) => {
  try {
    let token

    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1]
    }

    if (token) {
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET)
        const user = await User.findById(decoded.id)

        if (user && user.isActive) {
          req.user = {
            id: user._id,
            username: user.username,
            municipality: user.municipality,
            role: user.role,
            hasVoted: user.hasVoted,
            isActive: user.isActive,
          }
        }
      } catch (error) {
        // Token invalid, but continue without user
        logger.warn('Invalid token in optional auth:', error.message)
      }
    }

    next()
  } catch (error) {
    logger.error('Optional auth middleware error:', error)
    next()
  }
}
