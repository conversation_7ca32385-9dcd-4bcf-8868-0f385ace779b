import { useCallback } from 'react'
import toast from 'react-hot-toast'
import { logger } from '@/utils/logger'
import { 
  AppError, 
  ERROR_MESSAGES, 
  ERROR_SEVERITY, 
  createNetworkError, 
  createAuthError, 
  createServerError,
  createValidationError 
} from '@/constants/errors'

interface UseErrorHandlerReturn {
  handleError: (error: any, context?: string) => void
  handleApiError: (error: any, context?: string) => void
  showError: (message: string, severity?: keyof typeof ERROR_SEVERITY) => void
  clearErrors: () => void
}

export const useErrorHandler = (): UseErrorHandlerReturn => {
  const handleError = useCallback((error: any, context?: string) => {
    let appError: AppError

    // Handle different error types
    if (error?.response) {
      // API response error
      const status = error.response.status
      const message = error.response.data?.error || error.response.data?.message || error.message

      switch (status) {
        case 401:
          appError = createAuthError(ERROR_MESSAGES.UNAUTHORIZED)
          break
        case 403:
          appError = createAuthError(ERROR_MESSAGES.UNAUTHORIZED)
          break
        case 423:
          appError = createAuthError(ERROR_MESSAGES.ACCOUNT_LOCKED)
          break
        case 429:
          appError = createNetworkError(ERROR_MESSAGES.RATE_LIMITED)
          break
        case 400:
          appError = createValidationError(message || ERROR_MESSAGES.VALIDATION_FAILED)
          break
        case 404:
          appError = createServerError('Resource not found')
          break
        case 500:
        case 502:
        case 503:
        case 504:
          appError = createServerError(ERROR_MESSAGES.SERVER_ERROR)
          break
        default:
          appError = createServerError(message || ERROR_MESSAGES.UNEXPECTED_ERROR)
      }
    } else if (error?.code === 'ERR_NETWORK' || error?.message === 'Network Error') {
      appError = createNetworkError(ERROR_MESSAGES.NETWORK_ERROR)
    } else if (error?.message?.includes('CORS')) {
      appError = createNetworkError(ERROR_MESSAGES.CORS_ERROR)
    } else {
      appError = createServerError(error?.message || ERROR_MESSAGES.UNEXPECTED_ERROR)
    }

    // Log the error
    logger.error('Error handled by useErrorHandler', error, {
      component: 'useErrorHandler',
      action: 'handleError',
      context,
      appError,
    })

    // Show toast based on severity
    showErrorToast(appError)

    return appError
  }, [])

  const handleApiError = useCallback((error: any, context?: string) => {
    // Specific handling for API errors
    return handleError(error, context)
  }, [handleError])

  const showError = useCallback((message: string, severity: keyof typeof ERROR_SEVERITY = 'MEDIUM') => {
    showErrorToast({
      type: 'USER',
      message,
      severity: ERROR_SEVERITY[severity],
    } as AppError)
  }, [])

  const clearErrors = useCallback(() => {
    toast.dismiss()
  }, [])

  return {
    handleError,
    handleApiError,
    showError,
    clearErrors,
  }
}

// Helper function to show toast based on error severity
const showErrorToast = (error: AppError) => {
  const duration = getDurationBySeverity(error.severity)
  
  switch (error.severity) {
    case ERROR_SEVERITY.CRITICAL:
      toast.error(error.message, {
        duration,
        style: {
          background: '#dc2626',
          color: '#fff',
        },
      })
      break
    case ERROR_SEVERITY.HIGH:
      toast.error(error.message, {
        duration,
        style: {
          background: '#ea580c',
          color: '#fff',
        },
      })
      break
    case ERROR_SEVERITY.MEDIUM:
      toast.error(error.message, { duration })
      break
    case ERROR_SEVERITY.LOW:
      toast(error.message, {
        duration,
        style: {
          background: '#f59e0b',
          color: '#fff',
        },
      })
      break
    default:
      toast.error(error.message, { duration })
  }
}

const getDurationBySeverity = (severity: string): number => {
  switch (severity) {
    case ERROR_SEVERITY.CRITICAL:
      return 8000
    case ERROR_SEVERITY.HIGH:
      return 6000
    case ERROR_SEVERITY.MEDIUM:
      return 4000
    case ERROR_SEVERITY.LOW:
      return 3000
    default:
      return 4000
  }
}
