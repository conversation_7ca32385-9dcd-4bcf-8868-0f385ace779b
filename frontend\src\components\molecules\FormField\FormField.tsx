import { cn } from '@/lib/utils'
import { forwardRef } from 'react'
import { Control, FieldPath, FieldValues, useController } from 'react-hook-form'
import { Input, InputProps } from '../../atoms/Input'

export interface FormFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> extends Omit<InputProps, 'name' | 'error' | 'success'> {
  name: TName
  control: Control<TFieldValues>
  rules?: Parameters<typeof useController>[0]['rules']
  shouldUnregister?: boolean
  showSuccess?: boolean
}

const FormField = forwardRef<
  HTMLInputElement,
  FormFieldProps<any, any>
>(
  (
    {
      name,
      control,
      rules,
      shouldUnregister = false,
      showSuccess = false,
      className,
      ...inputProps
    },
    ref
  ) => {
    const {
      field,
      fieldState: { error, isDirty, isValid },
    } = useController({
      name,
      control,
      rules,
      shouldUnregister,
    })

    // Determine validation state
    const hasError = !!error
    const hasSuccess = showSuccess && isDirty && isValid && !hasError

    return (
      <Input
        {...inputProps}
        {...field}
        ref={ref}
        className={cn(className)}
        error={hasError ? error.message : undefined}
        success={hasSuccess ? 'Valid input' : undefined}
      />
    )
  }
)

FormField.displayName = 'FormField'

export { FormField }
export type { FormFieldProps }

