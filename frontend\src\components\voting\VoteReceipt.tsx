import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { BarChart3, Calendar, CheckCircle, Clock, Download, Home, MapPin, User } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

interface VoteReceiptProps {
  voteData: {
    submittedAt: string
    candidateCount: number
    batchId: string
    votedCandidates?: Array<{
      id: string
      municipalityName: string
      district: string
    }>
    voterInfo: {
      username: string
      municipality: string
      district: string
    }
  }
  onViewResults?: () => void
  onGoHome?: () => void
}

export function VoteReceipt({ voteData, onViewResults, onGoHome }: VoteReceiptProps) {
  const navigate = useNavigate()

  const handleViewResults = () => {
    if (onViewResults) {
      onViewResults()
    } else {
      navigate('/results')
    }
  }

  const handleGoHome = () => {
    if (onGoHome) {
      onGoHome()
    } else {
      navigate('/')
    }
  }

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return {
      date: date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      time: date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
  }

  const { date, time } = formatDateTime(voteData.submittedAt)

  return (
    <div className="min-h-screen bg-background py-8">
      <div className="mx-auto max-w-2xl px-4 sm:px-6 lg:px-8">
        <div className="space-y-6">
          {/* Success Header */}
          <Card className="border-green-200 bg-green-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
                <h1 className="text-2xl font-bold text-green-800 mb-2">
                  Vote Successfully Submitted!
                </h1>
                <p className="text-green-700">
                  Your vote has been recorded and cannot be changed. Thank you for participating in the DFPTA election.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Vote Receipt Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span>Vote Receipt</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Submission Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <span>Submission Date</span>
                  </div>
                  <p className="font-medium">{date}</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    <span>Submission Time</span>
                  </div>
                  <p className="font-medium">{time}</p>
                </div>
              </div>

              {/* Voter Information */}
              <div className="border-t pt-4">
                <h3 className="font-semibold mb-3 flex items-center space-x-2">
                  <User className="h-4 w-4" />
                  <span>Voter Information</span>
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-muted-foreground">Municipality</label>
                    <div className="flex items-center space-x-2 mt-1">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{voteData.voterInfo.municipality}</span>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm text-muted-foreground">Congressional District</label>
                    <div className="mt-1">
                      <Badge variant="secondary">{voteData.voterInfo.district}</Badge>
                    </div>
                  </div>
                </div>
              </div>

              {/* Vote Summary */}
              <div className="border-t pt-4">
                <h3 className="font-semibold mb-3">Vote Summary</h3>
                <div className="bg-muted/50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Candidates Selected</span>
                    <span className="text-2xl font-bold text-primary">{voteData.candidateCount}</span>
                  </div>
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-sm text-muted-foreground">Batch ID</span>
                    <code className="text-xs bg-background px-2 py-1 rounded border">
                      {voteData.batchId}
                    </code>
                  </div>
                </div>

                {/* Voted Candidates List */}
                {voteData.votedCandidates && voteData.votedCandidates.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-medium mb-2 text-sm text-muted-foreground">Your Voted Candidates:</h4>
                    <div className="space-y-2">
                      {voteData.votedCandidates.map((candidate, index) => (
                        <div key={candidate.id} className="flex items-center justify-between bg-background rounded-lg p-3 border">
                          <div className="flex items-center space-x-3">
                            <div className="flex items-center justify-center w-6 h-6 bg-primary text-primary-foreground rounded-full text-xs font-medium">
                              {index + 1}
                            </div>
                            <div>
                              <p className="font-medium text-sm">{candidate.municipalityName}</p>
                              <p className="text-xs text-muted-foreground">{candidate.district}</p>
                            </div>
                          </div>
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Important Notice */}
              <div className="border-t pt-4">
                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                  <h4 className="font-medium text-amber-800 mb-2">Important Notice</h4>
                  <ul className="text-sm text-amber-700 space-y-1">
                    <li>• Your vote has been securely recorded and encrypted</li>
                    <li>• You cannot change or resubmit your vote</li>
                    <li>• Keep this receipt for your records</li>
                    <li>• Results will be available when announced by administrators</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="outline"
              onClick={handleViewResults}
              className="flex items-center space-x-2"
            >
              <BarChart3 className="h-4 w-4" />
              <span>View Results</span>
            </Button>

            <Button
              variant="outline"
              onClick={handleGoHome}
              className="flex items-center space-x-2"
            >
              <Home className="h-4 w-4" />
              <span>Back to Home</span>
            </Button>

            <Button
              variant="outline"
              onClick={() => window.print()}
              className="flex items-center space-x-2"
            >
              <Download className="h-4 w-4" />
              <span>Print Receipt</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
