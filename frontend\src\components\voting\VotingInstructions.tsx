import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Vote, X, Clock } from 'lucide-react'
import { cn } from '@/lib/utils'

interface VotingInstructionsProps {
  maxCandidatesPerVote: number
  autoHideAfter?: number // seconds, default 7
  className?: string
}

export function VotingInstructions({ 
  maxCandidatesPerVote, 
  autoHideAfter = 7,
  className 
}: VotingInstructionsProps) {
  const [isVisible, setIsVisible] = useState(true)
  const [countdown, setCountdown] = useState(autoHideAfter)

  useEffect(() => {
    if (!isVisible) return

    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          setIsVisible(false)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [isVisible])

  const handleManualClose = () => {
    setIsVisible(false)
  }

  if (!isVisible) {
    return null
  }

  return (
    <div className={cn(
      "transition-all duration-500 ease-in-out transform",
      isVisible ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-4",
      className
    )}>
      <Card className='mx-auto max-w-4xl border-blue-200 bg-blue-50'>
        <CardHeader className='pb-3'>
          <div className="flex items-center justify-between">
            <CardTitle className='flex items-center space-x-2 text-blue-900'>
              <Vote className='h-5 w-5' />
              <span>Cast Your Vote</span>
            </CardTitle>
            
            <div className="flex items-center space-x-2">
              {/* Countdown indicator */}
              <div className="flex items-center space-x-1 text-sm text-blue-700">
                <Clock className="h-4 w-4" />
                <span>Auto-hide in {countdown}s</span>
              </div>
              
              {/* Manual close button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleManualClose}
                className="h-8 w-8 p-0 text-blue-700 hover:bg-blue-100"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className='rounded-lg bg-blue-100 p-4'>
            <h3 className='mb-3 font-semibold text-blue-900'>Voting Instructions:</h3>
            <ul className='space-y-2 text-sm text-blue-800'>
              <li className="flex items-start space-x-2">
                <span className="text-blue-600 font-bold">•</span>
                <span>Select up to <strong>{maxCandidatesPerVote}</strong> candidates from the list below</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-blue-600 font-bold">•</span>
                <span>Use the search and filter options to find specific candidates</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-blue-600 font-bold">•</span>
                <span>Review your selections carefully before submitting</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-blue-600 font-bold">•</span>
                <span><strong>Important:</strong> Once submitted, your votes cannot be changed</span>
              </li>
            </ul>
            
            <div className="mt-4 p-3 bg-blue-200 rounded-lg">
              <p className="text-xs text-blue-900 font-medium">
                💡 <strong>Tip:</strong> This notification will automatically disappear in {countdown} seconds, 
                or you can close it manually using the × button above.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
