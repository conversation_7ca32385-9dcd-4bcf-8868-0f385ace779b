import {
    AuthResponse,
    AuthUser,
    ChangePasswordFormData,
    ForgotPasswordFormData,
    LoginFormData,
    ResetPasswordFormData,
    validateAuthResponse,
    validateChangePasswordData,
    validateForgotPasswordData,
    validateLoginData,
    validateResetPasswordData,
} from '../schemas/auth'
import { UserResponse, validateUserResponse } from '../schemas/user'
import { logAuth, logger } from '../utils/logger'
import { apiClient } from './apiClient'
import { votingService } from './votingService'

// Enhanced authentication service with better security
export class AuthService {
  private static instance: AuthService
  private tokenRefreshPromise: Promise<string> | null = null

  private constructor() {}

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService()
    }
    return AuthService.instance
  }

  /**
   * Login with enhanced validation and security
   */
  async login(credentials: LoginFormData): Promise<AuthResponse> {
    try {
      logger.info('Login attempt started', {
        component: 'AuthService',
        action: 'login',
        userId: credentials.username,
      })

      // Validate input data
      const validatedCredentials = validateLoginData(credentials)

      // Add client-side security headers
      const response = await apiClient.post('/auth/login', validatedCredentials, {
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'X-Client-Version': '2.0.0',
        },
      })

      // Extract the nested data structure to match frontend schema
      let responseData = response.data
      if (responseData.success && responseData.data) {
        // Backend returns nested structure: { success: true, data: { user, token, ... } }
        // Frontend expects flat structure: { success: true, user, token, ... }
        responseData = {
          success: responseData.success,
          ...responseData.data,
        }
      }

      // Validate response structure
      let authResponse
      try {
        authResponse = validateAuthResponse(responseData)
      } catch (error) {
        console.error('🔍 Validation error details:', error)
        console.error(
          '🔍 Response data that failed validation:',
          JSON.stringify(responseData, null, 2)
        )
        throw error
      }

      // Store tokens securely
      if (authResponse.success) {
        this.storeTokens(authResponse.token, null) // refreshToken is handled via cookies
        this.storeUserData(authResponse.user)

        // Note: expiresAt and refreshToken are handled by the backend via cookies
        // No need to schedule token refresh as it's handled server-side

        // Log successful login
        logAuth.login(credentials.username, true)
      }

      return authResponse
    } catch (error: any) {
      // Log failed login attempt
      logAuth.login(credentials.username, false)

      // Enhanced error handling
      if (error.response?.status === 423) {
        logger.warn('Login failed - account locked', {
          component: 'AuthService',
          action: 'login',
          userId: credentials.username,
          metadata: { reason: 'account_locked' },
        })
        throw new Error('Account temporarily locked due to too many failed login attempts')
      }
      if (error.response?.status === 401) {
        logger.warn('Login failed - invalid credentials', {
          component: 'AuthService',
          action: 'login',
          userId: credentials.username,
          metadata: { reason: 'invalid_credentials' },
        })
        throw new Error('Invalid username or password')
      }

      logger.error('Login failed - unexpected error', error, {
        component: 'AuthService',
        action: 'login',
        userId: credentials.username,
      })
      throw new Error(error.response?.data?.error || 'Login failed')
    }
  }

  /**
   * Logout with proper cleanup
   */
  async logout(): Promise<void> {
    const userData = this.getUserData()
    const username = userData?.username || 'unknown'

    try {
      logger.info('Logout initiated', {
        component: 'AuthService',
        action: 'logout',
        userId: username,
      })

      // Notify server to invalidate tokens
      await apiClient.post('/auth/logout', {
        refreshToken: this.getRefreshToken(),
      })

      logAuth.logout(username)
    } catch (error) {
      // Continue with logout even if server call fails
      logger.warn('Server logout failed, continuing with local cleanup', {
        component: 'AuthService',
        action: 'logout',
        userId: username,
      })
    } finally {
      // Clear all stored data including draft votes
      this.clearAuthData()
      votingService.clearAllDraftVotes()
      logger.info('Logout completed - local data and draft votes cleared', {
        component: 'AuthService',
        action: 'logout',
        userId: username,
      })
    }
  }

  /**
   * Get current user with validation
   */
  async getCurrentUser(): Promise<UserResponse> {
    try {
      const response = await apiClient.get('/auth/me')
      return validateUserResponse(response.data.data)
    } catch (error: any) {
      if (error.response?.status === 401) {
        this.clearAuthData()
        throw new Error('Session expired')
      }
      throw new Error(error.response?.data?.error || 'Failed to get user data')
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(): Promise<string> {
    // Prevent multiple simultaneous refresh requests
    if (this.tokenRefreshPromise) {
      return this.tokenRefreshPromise
    }

    this.tokenRefreshPromise = this.performTokenRefresh()

    try {
      const newToken = await this.tokenRefreshPromise
      return newToken
    } finally {
      this.tokenRefreshPromise = null
    }
  }

  private async performTokenRefresh(): Promise<string> {
    const userData = this.getUserData()
    const username = userData?.username || 'unknown'

    try {
      logger.debug('Token refresh initiated', {
        component: 'AuthService',
        action: 'token_refresh',
        userId: username,
      })

      const refreshToken = this.getRefreshToken()
      if (!refreshToken) {
        throw new Error('No refresh token available')
      }

      const response = await apiClient.post('/auth/refresh', { refreshToken })
      const { token, refreshToken: newRefreshToken, expiresAt } = response.data.data

      // Store new tokens
      this.storeTokens(token, newRefreshToken)

      // Schedule next refresh
      this.scheduleTokenRefresh(expiresAt)

      logAuth.tokenRefresh(username, true)
      return token
    } catch (error: any) {
      // Refresh failed, clear auth data
      logAuth.tokenRefresh(username, false)
      logger.error('Token refresh failed - clearing auth data', error, {
        component: 'AuthService',
        action: 'token_refresh',
        userId: username,
      })
      this.clearAuthData()
      throw new Error('Session expired')
    }
  }

  /**
   * Change password with validation
   */
  async changePassword(data: ChangePasswordFormData): Promise<void> {
    try {
      const validatedData = validateChangePasswordData(data)
      await apiClient.post('/auth/change-password', validatedData)
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to change password')
    }
  }

  /**
   * Request password reset
   */
  async forgotPassword(data: ForgotPasswordFormData): Promise<void> {
    try {
      const validatedData = validateForgotPasswordData(data)
      await apiClient.post('/auth/forgot-password', validatedData)
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to send reset email')
    }
  }

  /**
   * Reset password with token
   */
  async resetPassword(data: ResetPasswordFormData): Promise<void> {
    try {
      const validatedData = validateResetPasswordData(data)
      await apiClient.post('/auth/reset-password', validatedData)
    } catch (error: any) {
      throw new Error(error.response?.data?.error || 'Failed to reset password')
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = this.getAccessToken()
    const user = this.getUserData()
    return !!(token && user)
  }

  /**
   * Get stored access token
   */
  getAccessToken(): string | null {
    return localStorage.getItem('accessToken')
  }

  /**
   * Get stored refresh token
   */
  getRefreshToken(): string | null {
    return localStorage.getItem('refreshToken')
  }

  /**
   * Get stored user data
   */
  getUserData(): UserResponse | null {
    try {
      const userData = localStorage.getItem('userData')
      if (userData) {
        return JSON.parse(userData)
      }
    } catch (error) {
      console.error('Failed to parse user data:', error)
      this.clearAuthData()
    }
    return null
  }

  /**
   * Store tokens securely
   */
  private storeTokens(accessToken: string, refreshToken?: string | null): void {
    localStorage.setItem('accessToken', accessToken)
    if (refreshToken) {
      localStorage.setItem('refreshToken', refreshToken)
    }
    localStorage.setItem('tokenTimestamp', Date.now().toString())
  }

  /**
   * Store user data
   */
  private storeUserData(user: AuthUser | UserResponse): void {
    localStorage.setItem('userData', JSON.stringify(user))
  }

  /**
   * Clear all authentication data
   */
  private clearAuthData(): void {
    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('userData')
    localStorage.removeItem('tokenTimestamp')
  }

  /**
   * Schedule automatic token refresh
   */
  private scheduleTokenRefresh(expiresAt: string): void {
    const expiryTime = new Date(expiresAt).getTime()
    const currentTime = Date.now()
    const timeUntilExpiry = expiryTime - currentTime

    // Refresh token 5 minutes before expiry
    const refreshTime = Math.max(timeUntilExpiry - 5 * 60 * 1000, 60 * 1000)

    setTimeout(() => {
      if (this.isAuthenticated()) {
        this.refreshToken().catch(console.error)
      }
    }, refreshTime)
  }
}

// Export singleton instance
export const authService = AuthService.getInstance()
