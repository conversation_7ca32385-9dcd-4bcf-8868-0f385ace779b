import axios from 'axios'
import logger from '../utils/logger.js'

const API_BASE_URL = 'http://localhost:5000/api'

/**
 * Simple test to debug the election flow
 */
const simpleTest = async () => {
  try {
    logger.info('🧪 Starting simple election test...')

    // Test 1: Login as admin
    logger.info('1️⃣ Testing admin login...')
    const adminResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'socmob123'
    })

    if (adminResponse.data.success) {
      logger.info('✅ Admin login successful')
      const adminToken = adminResponse.data.data.token

      // Test 2: Get public settings with admin token
      logger.info('2️⃣ Testing public settings with admin token...')
      try {
        const settingsResponse = await axios.get(`${API_BASE_URL}/system/public-settings`, {
          headers: { 'Authorization': `Bearer ${adminToken}` }
        })

        if (settingsResponse.data.success) {
          logger.info('✅ Public settings retrieved successfully')
          logger.info('📊 Settings:', settingsResponse.data.data)

          const electionActive = settingsResponse.data.data.voting_session_active?.value
          logger.info(`🗳️ Election currently: ${electionActive ? 'OPEN' : 'CLOSED'}`)

          // Test 3: Toggle election
          logger.info('3️⃣ Testing election toggle...')
          const toggleResponse = await axios.post(`${API_BASE_URL}/system/toggle-election`, {}, {
            headers: { 'Authorization': `Bearer ${adminToken}` }
          })

          if (toggleResponse.data.success) {
            logger.info('✅ Election toggle successful')
            logger.info('📊 New status:', toggleResponse.data.data)
          } else {
            logger.error('❌ Election toggle failed:', toggleResponse.data)
          }

        } else {
          logger.error('❌ Public settings failed:', settingsResponse.data)
        }
      } catch (settingsError) {
        logger.error('❌ Public settings error:', {
          status: settingsError.response?.status,
          data: settingsError.response?.data,
          message: settingsError.message
        })
      }

    } else {
      logger.error('❌ Admin login failed:', adminResponse.data)
    }

    // Test 4: Login as voter
    logger.info('4️⃣ Testing voter login...')
    const voterResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'balatan',
      password: 'bala#767'
    })

    if (voterResponse.data.success) {
      logger.info('✅ Voter login successful')
      const voterToken = voterResponse.data.data.token

      // Test 5: Get public settings with voter token
      logger.info('5️⃣ Testing public settings with voter token...')
      try {
        const voterSettingsResponse = await axios.get(`${API_BASE_URL}/system/public-settings`, {
          headers: { 'Authorization': `Bearer ${voterToken}` }
        })

        if (voterSettingsResponse.data.success) {
          logger.info('✅ Voter can access public settings')
          const electionActive = voterSettingsResponse.data.data.voting_session_active?.value
          logger.info(`🗳️ Election status for voter: ${electionActive ? 'OPEN' : 'CLOSED'}`)

          // Test 6: Get voting status
          logger.info('6️⃣ Testing voting status...')
          const votingStatusResponse = await axios.get(`${API_BASE_URL}/voting/status`, {
            headers: { 'Authorization': `Bearer ${voterToken}` }
          })

          if (votingStatusResponse.data.success) {
            logger.info('✅ Voting status retrieved')
            logger.info('📊 Voting status:', votingStatusResponse.data.data)
          } else {
            logger.error('❌ Voting status failed:', votingStatusResponse.data)
          }

        } else {
          logger.error('❌ Voter public settings failed:', voterSettingsResponse.data)
        }
      } catch (voterSettingsError) {
        logger.error('❌ Voter public settings error:', {
          status: voterSettingsError.response?.status,
          data: voterSettingsError.response?.data,
          message: voterSettingsError.message
        })
      }

    } else {
      logger.error('❌ Voter login failed:', voterResponse.data)
    }

    logger.info('🎉 Simple test completed')

  } catch (error) {
    logger.error('💥 Test failed:', error.message)
  }
}

// Run test
simpleTest()
