import { Trending<PERSON><PERSON>, User<PERSON>he<PERSON>, Users, Vote } from 'lucide-react'
import { StatCard } from '../../atoms/StatCard'

export interface StatisticsData {
  totalUsers: number
  totalVoters: number
  totalActiveVoters: number
  votedUsers: number
  pendingVoters: number
  activeUsers: number
  votingProgress: number
  usersByRole?: Record<string, number>
}

export interface StatisticsCardsProps {
  data: StatisticsData | null
  loading?: boolean
  className?: string
  onCardClick?: (cardType: string) => void
}

export function StatisticsCards({
  data,
  loading = false,
  className,
  onCardClick
}: StatisticsCardsProps) {
  // Calculate derived statistics
  const votingProgressPercent = data ? Math.round(data.votingProgress) : 0

  const handleCardClick = (cardType: string) => {
    onCardClick?.(cardType)
  }

  return (
    <div className={`grid gap-4 md:grid-cols-2 lg:grid-cols-4 ${className || ''}`}>
      {/* Total Users - 36 (35 candidates + 1 admin) */}
      <StatCard
        title="Total Users"
        value={data?.totalUsers || 0}
        subtitle="35 candidates + 1 admin"
        icon={<Users className="h-4 w-4" />}
        loading={loading}
        variant="default"
        onClick={() => handleCardClick('total-users')}
      />

      {/* Voters/Candidates Registered - 35 Municipalities */}
      <StatCard
        title="Voters/Candidates Registered"
        value={data?.totalVoters || 0}
        subtitle="35 Municipalities"
        icon={<UserCheck className="h-4 w-4" />}
        loading={loading}
        variant="info"
        onClick={() => handleCardClick('voters')}
      />

      {/* Pending Voters - Number of candidates eligible to vote */}
      <StatCard
        title="Pending Voters"
        value={data?.pendingVoters || 0}
        subtitle="Eligible to vote"
        icon={<Vote className="h-4 w-4" />}
        loading={loading}
        variant="warning"
        onClick={() => handleCardClick('pending-votes')}
      />

      {/* Voting Progress - Percentage calculated as (votes cast / 35 total registered) × 100% */}
      <StatCard
        title="Voting Progress"
        value={`${votingProgressPercent}%`}
        subtitle={`${data?.votedUsers || 0} of 35 registered`}
        icon={<TrendingUp className="h-4 w-4" />}
        loading={loading}
        variant="success"
        onClick={() => handleCardClick('voting-progress')}
      />
    </div>
  )
}

export default StatisticsCards
