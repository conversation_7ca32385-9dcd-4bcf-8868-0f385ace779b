import express from 'express'
import { activeUserRequired } from '../middleware/activeUserRequired.js'
import { protect } from '../middleware/auth.js'
import Candidate from '../models/Candidate.js'
import SystemSettings from '../models/SystemSettings.js'
import User from '../models/User.js'
import logger from '../utils/logger.js'

const router = express.Router()

// @desc    Get all candidates (active and inactive for display) with proper User status checking
// @route   GET /api/candidates
// @access  Private (requires active user)
router.get('/candidates', protect, activeUserRequired, async (_req, res) => {
  try {
    // Get all candidates (both active and inactive for display purposes)
    const candidates = await Candidate.find({})
      .select('municipalityName district totalVotes currentRank finalPosition isWinner isEliminated isActive')
      .sort({ isActive: -1, municipalityName: 1 }) // Active candidates first, then inactive

    // Get disabled/absent users to filter out their municipalities
    const disabledUsers = await User.find({
      role: 'voter',
      isActive: false
    }).select('municipality')

    const disabledMunicipalities = new Set(disabledUsers.map(user => user.municipality))

    // Filter out candidates from disabled/absent municipalities
    const availableCandidates = candidates.map(candidate => ({
      ...candidate.toObject(),
      isDisabled: disabledMunicipalities.has(candidate.municipalityName),
      disabledReason: disabledMunicipalities.has(candidate.municipalityName) ? 'ABSENT' : null
    }))

    res.status(200).json({
      success: true,
      data: {
        candidates: availableCandidates,
        count: availableCandidates.length
      }
    })
  } catch (error) {
    logger.error('Error fetching candidates:', error.message)
    res.status(500).json({
      success: false,
      error: 'Server error while fetching candidates'
    })
  }
})

// @desc    Get voting status
// @route   GET /api/voting/status
// @access  Private
router.get('/voting/status', protect, async (req, res) => {
  try {
    const user = await User.findById(req.user.id)

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      })
    }

    // Check if election is active
    const electionSetting = await SystemSettings.findOne({ key: 'voting_session_active' })
    const isElectionActive = electionSetting?.value || false

    // Determine if user can vote based on multiple factors
    let canVote = false
    let votingStatus = 'round1'
    let message = ''

    if (!user.isActive) {
      votingStatus = 'user-disabled'
      message = 'Your account has been disabled. Please contact an administrator.'
    } else if (user.hasVoted) {
      votingStatus = 'completed'
      message = 'You have already cast your vote.'
    } else if (!isElectionActive) {
      votingStatus = 'closed-by-admin'
      message = 'Voting is currently disabled by the administrator.'
    } else {
      canVote = true
      message = 'You are eligible to vote.'
    }

    const responseData = {
      canVote,
      hasVoted: user.hasVoted,
      votingStatus,
      message,
      currentRound: 1,
      maxCandidatesPerVote: 15,
      userRole: user.role,
      municipality: user.municipality,
      district: user.district,
      isElectionActive
    }

    res.status(200).json({
      success: true,
      data: responseData
    })
  } catch (error) {
    logger.error('Error fetching voting status:', error.message)
    res.status(500).json({
      success: false,
      error: 'Server error while fetching voting status'
    })
  }
})

// @desc    Submit votes
// @route   POST /api/voting/submit
// @access  Private
router.post('/voting/submit', protect, async (req, res) => {
  try {
    const { candidateIds } = req.body
    const user = await User.findById(req.user.id)

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      })
    }

    if (user.hasVoted) {
      return res.status(400).json({
        success: false,
        error: 'You have already voted'
      })
    }

    if (!candidateIds || !Array.isArray(candidateIds)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid candidate selection'
      })
    }

    if (candidateIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Please select at least one candidate'
      })
    }

    if (candidateIds.length > 15) {
      return res.status(400).json({
        success: false,
        error: 'Maximum 15 candidates can be selected'
      })
    }

    // Verify all candidate IDs are valid
    const validCandidates = await Candidate.find({
      _id: { $in: candidateIds },
      isActive: true
    })

    if (validCandidates.length !== candidateIds.length) {
      return res.status(400).json({
        success: false,
        error: 'One or more invalid candidates selected'
      })
    }

    // Update vote counts for selected candidates
    await Candidate.updateMany(
      { _id: { $in: candidateIds } },
      { $inc: { totalVotes: 1 } }
    )

    // Mark user as voted
    await User.findByIdAndUpdate(req.user.id, {
      hasVoted: true,
      lastVotedAt: new Date()
    })

    logger.info(`User ${user.username} (${user.municipality}) submitted votes for ${candidateIds.length} candidates`)

    // Generate batch ID for tracking
    const batchId = new Date().getTime().toString()

    res.status(200).json({
      success: true,
      data: {
        message: 'Votes submitted successfully',
        votesCount: candidateIds.length,
        batchId,
        submittedAt: new Date(),
        votedCandidates: validCandidates.map(candidate => ({
          id: candidate._id,
          municipalityName: candidate.municipalityName,
          district: candidate.district
        })),
        municipality: user.municipality,
        district: user.district
      }
    })
  } catch (error) {
    logger.error('Error submitting votes:', error.message)
    res.status(500).json({
      success: false,
      error: 'Server error while submitting votes'
    })
  }
})

// @desc    Get voting results (basic)
// @route   GET /api/voting/results
// @access  Private
router.get('/voting/results', protect, async (_req, res) => {
  try {
    const results = await Candidate.find({ isActive: true })
      .select('municipalityName district totalVotes')
      .sort({ totalVotes: -1, municipalityName: 1 })

    // Calculate rankings
    const resultsWithRank = results.map((candidate, index) => ({
      ...candidate.toObject(),
      currentRank: index + 1
    }))

    // Get voting statistics with proper inactive user handling
    const totalRegisteredVoters = await User.countDocuments({ role: 'voter' }) // All registered voters (35)
    const totalActiveVoters = await User.countDocuments({ role: 'voter', isActive: true }) // Active participants
    const totalInactiveVoters = await User.countDocuments({ role: 'voter', isActive: false }) // Absent participants
    const totalVotesCast = await User.countDocuments({ role: 'voter', hasVoted: true, isActive: true })
    const participationRate = totalRegisteredVoters > 0 ? (totalVotesCast / totalRegisteredVoters) * 100 : 0
    const formattedParticipationRate = Math.round(participationRate * 100) / 100

    res.status(200).json({
      success: true,
      data: {
        results: resultsWithRank,
        statistics: {
          totalRegisteredVoters, // All 35 registered voters
          totalActiveVoters, // Active participants only
          totalInactiveVoters, // Absent participants
          totalVotesCast,
          participationRate: formattedParticipationRate,
          totalCandidates: results.length,
          message: `${totalVotesCast || 0} of ${totalRegisteredVoters || 35} voters (${formattedParticipationRate || 0}%)`
        }
      }
    })
  } catch (error) {
    logger.error('Error fetching results:', error.message)
    res.status(500).json({
      success: false,
      error: 'Server error while fetching results'
    })
  }
})

export default router
