import { Button } from '@/components/ui/button'
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { AppError, ERROR_SEVERITY } from '@/constants/errors'
import { AlertTriangle, RefreshCw, WifiOff } from 'lucide-react'
import { ReactNode } from 'react'

interface ErrorDisplayProps {
  error?: AppError | string | null
  title?: string
  showRetry?: boolean
  onRetry?: () => void
  className?: string
  variant?: 'card' | 'inline' | 'banner'
  children?: ReactNode
}

export const ErrorDisplay = ({
  error,
  title,
  showRetry = true,
  onRetry,
  className = '',
  variant = 'card',
  children,
}: ErrorDisplayProps) => {
  if (!error) return null

  const errorMessage = typeof error === 'string' ? error : error.message
  const errorType = typeof error === 'string' ? 'USER' : error.type
  const severity = typeof error === 'string' ? ERROR_SEVERITY.MEDIUM : error.severity

  const getIcon = () => {
    switch (errorType) {
      case 'NETWORK':
        return <WifiOff className='h-5 w-5' />
      case 'AUTH':
        return <AlertTriangle className='h-5 w-5' />
      default:
        return <AlertTriangle className='h-5 w-5' />
    }
  }

  const getColorClasses = () => {
    switch (severity) {
      case ERROR_SEVERITY.CRITICAL:
        return {
          card: 'border-red-500 bg-red-50',
          text: 'text-red-800',
          icon: 'text-red-600',
        }
      case ERROR_SEVERITY.HIGH:
        return {
          card: 'border-orange-400 bg-orange-50',
          text: 'text-orange-800',
          icon: 'text-orange-600',
        }
      case ERROR_SEVERITY.MEDIUM:
        return {
          card: 'border-yellow-400 bg-yellow-50',
          text: 'text-yellow-800',
          icon: 'text-yellow-600',
        }
      case ERROR_SEVERITY.LOW:
        return {
          card: 'border-blue-400 bg-blue-50',
          text: 'text-blue-800',
          icon: 'text-blue-600',
        }
      default:
        return {
          card: 'border-red-400 bg-red-50',
          text: 'text-red-800',
          icon: 'text-red-600',
        }
    }
  }

  const colors = getColorClasses()

  if (variant === 'inline') {
    return (
      <div className={`flex items-center gap-2 text-sm ${colors.text} ${className}`}>
        <span className={colors.icon}>{getIcon()}</span>
        <span>{errorMessage}</span>
        {showRetry && onRetry && (
          <Button
            variant='ghost'
            size='sm'
            onClick={onRetry}
            className='h-auto p-1 text-xs'
          >
            <RefreshCw className='h-3 w-3' />
          </Button>
        )}
      </div>
    )
  }

  if (variant === 'banner') {
    return (
      <div className={`rounded-lg border p-4 ${colors.card} ${className}`}>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-3'>
            <span className={colors.icon}>{getIcon()}</span>
            <div>
              {title && <p className={`font-medium ${colors.text}`}>{title}</p>}
              <p className={`text-sm ${colors.text}`}>{errorMessage}</p>
            </div>
          </div>
          {showRetry && onRetry && (
            <Button
              variant='outline'
              size='sm'
              onClick={onRetry}
              className='ml-4'
            >
              <RefreshCw className='mr-2 h-4 w-4' />
              Retry
            </Button>
          )}
        </div>
        {children}
      </div>
    )
  }

  // Default card variant
  return (
    <Card className={`${colors.card} ${className}`}>
      <CardHeader>
        <CardTitle className={`flex items-center gap-2 ${colors.text}`}>
          <span className={colors.icon}>{getIcon()}</span>
          {title || 'Error'}
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        <p className={`text-sm ${colors.text}`}>{errorMessage}</p>

        {children}

        {showRetry && onRetry && (
          <div className='flex gap-2'>
            <Button onClick={onRetry} variant='outline' size='sm'>
              <RefreshCw className='mr-2 h-4 w-4' />
              Try Again
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Specialized error components for common scenarios
export const NetworkErrorDisplay = ({ onRetry, className }: { onRetry?: () => void; className?: string }) => (
  <ErrorDisplay
    error="Unable to connect to the server. Please check your internet connection."
    title="Connection Error"
    onRetry={onRetry}
    className={className}
    variant="banner"
  />
)

export const LoadingErrorDisplay = ({ onRetry, className }: { onRetry?: () => void; className?: string }) => (
  <ErrorDisplay
    error="Failed to load data. Please try again."
    title="Loading Error"
    onRetry={onRetry}
    className={className}
  />
)

export const AuthErrorDisplay = ({ className }: { className?: string }) => (
  <ErrorDisplay
    error="You are not authorized to access this resource."
    title="Access Denied"
    showRetry={false}
    className={className}
    variant="banner"
  />
)
