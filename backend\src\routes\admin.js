// Enhanced admin routes with activity logs and vote backups
import express from 'express'
import {
    archiveElectionResults,
    bulkUserOperations,
    createUser,
    exportArchiveDOCX,
    exportArchivePDF,
    exportResultsDOCX,
    exportResultsPDF,
    getAccountStats,
    getActivityLogs,
    getAdminResults,
    getDashboardStats,
    getElectionArchiveById,
    getElectionArchives,
    getSystemSettings,
    getUsers,
    getVoteBackups,
    getVoteResets,
    getVoteResetStats,
    resetAllVotes,
    resetElectionSystem,
    resetUserVote,
    restoreVoteBackup,
    togglePublicResults,
    toggleUserStatus,
    updateSystemSetting
} from '../controllers/adminController.js'
import { adminOnly, protect } from '../middleware/auth.js'
import {
    validateBulkOperation,
    validateCreateUser,
    validateObjectId,
    validatePagination,
    validateSearch
} from '../middleware/validation.js'

const router = express.Router()

// Apply admin authentication to all routes
router.use(protect, adminOnly)

/**
 * @swagger
 * /api/admin/dashboard:
 *   get:
 *     summary: Get admin dashboard statistics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard data retrieved successfully
 */
router.get('/dashboard', getDashboardStats)

/**
 * @swagger
 * /api/admin/results:
 *   get:
 *     summary: Get admin voting results with district sorting and toggle functionality
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: showNames
 *         schema:
 *           type: string
 *           enum: [true, false]
 *         description: Whether to show candidate names
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [votes, name]
 *         description: Sort results by votes or name
 *     responses:
 *       200:
 *         description: Voting results retrieved successfully
 */
router.get('/results', getAdminResults)

/**
 * @swagger
 * /api/admin/users:
 *   get:
 *     summary: Get all users with pagination and filtering
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of users per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for username or municipality
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *           enum: [voter, admin, execom, tie-breaker]
 *         description: Filter by role
 *       - in: query
 *         name: municipality
 *         schema:
 *           type: string
 *         description: Filter by municipality
 *       - in: query
 *         name: hasVoted
 *         schema:
 *           type: boolean
 *         description: Filter by voting status
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 *   post:
 *     summary: Create new user
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - username
 *               - municipality
 *               - password
 *             properties:
 *               username:
 *                 type: string
 *               municipality:
 *                 type: string
 *               password:
 *                 type: string
 *               role:
 *                 type: string
 *                 enum: [voter, admin, execom, tie-breaker]
 *               email:
 *                 type: string
 *     responses:
 *       201:
 *         description: User created successfully
 */
router
  .route('/users')
  .get(validatePagination, validateSearch, getUsers)
  .post(validateCreateUser, createUser)

/**
 * @swagger
 * /api/admin/users/bulk:
 *   post:
 *     summary: Perform bulk operations on users
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userIds
 *               - action
 *             properties:
 *               userIds:
 *                 type: array
 *                 items:
 *                   type: string
 *               action:
 *                 type: string
 *                 enum: [activate, deactivate, delete, reset-votes]
 *     responses:
 *       200:
 *         description: Bulk operation completed successfully
 */
router.post('/users/bulk', validateBulkOperation, bulkUserOperations)

/**
 * @swagger
 * /api/admin/users/{id}:
 *   get:
 *     summary: Get user by ID
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     responses:
 *       200:
 *         description: User retrieved successfully
 *   put:
 *     summary: Update user
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               municipality:
 *                 type: string
 *               role:
 *                 type: string
 *                 enum: [voter, admin, execom, tie-breaker]
 *               email:
 *                 type: string
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: User updated successfully
 *   delete:
 *     summary: Delete user
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     responses:
 *       200:
 *         description: User deleted successfully
 */
// User status toggle endpoint
router.put('/users/:id/status', validateObjectId('id'), toggleUserStatus)

/**
 * @swagger
 * /api/admin/users/{id}/reset-vote:
 *   post:
 *     summary: Reset user's vote
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     responses:
 *       200:
 *         description: User vote reset successfully
 */
router.post('/users/:id/reset-vote', validateObjectId('id'), resetUserVote)

// Activity logs routes
router.get('/activity-logs', getActivityLogs)

// Vote backup routes
router.get('/vote-backups', getVoteBackups)
router.post('/vote-backups/:id/restore', validateObjectId('id'), restoreVoteBackup)

// Account statistics route
router.get('/account-stats', getAccountStats)

// Vote reset routes
router.post('/reset-votes', resetAllVotes)
router.get('/vote-resets', getVoteResets)
router.get('/vote-reset-stats', getVoteResetStats)

// User presence routes removed - using only isActive field from User Management

// Election archive and reset routes
router.post('/election/archive', archiveElectionResults)
router.post('/election/reset', resetElectionSystem)
router.get('/election/archives', getElectionArchives)
router.get('/election/archives/:id', validateObjectId('id'), getElectionArchiveById)

// System settings routes
router.get('/settings', getSystemSettings)
router.patch('/settings/:key', updateSystemSetting)
router.patch('/toggle-public-results', togglePublicResults)

// PDF export routes
router.get('/results/export/pdf', exportResultsPDF)
router.get('/archives/:id/export/pdf', validateObjectId('id'), exportArchivePDF)

// DOCX export routes
router.get('/results/export/docx', exportResultsDOCX)
router.get('/archives/:id/export/docx', validateObjectId('id'), exportArchiveDOCX)

export default router
