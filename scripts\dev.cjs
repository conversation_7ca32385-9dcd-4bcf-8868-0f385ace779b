#!/usr/bin/env node

const { spawn } = require("child_process");
const path = require("path");
const fs = require("fs");

console.log("🚀 Starting DFPTA Development Servers...");

const projectRoot = path.join(__dirname, "..");
console.log("📁 Project Root:", projectRoot);

// Check if directories exist
const backendDir = path.join(projectRoot, "backend");
const frontendDir = path.join(projectRoot, "frontend");

if (!fs.existsSync(backendDir)) {
  console.error("❌ Backend directory not found:", backendDir);
  process.exit(1);
}

if (!fs.existsSync(frontendDir)) {
  console.error("❌ Frontend directory not found:", frontendDir);
  process.exit(1);
}

// Start backend server
console.log("🔧 Starting backend server...");
const backendProcess = spawn("npm", ["run", "dev"], {
  cwd: backendDir,
  stdio: "inherit",
  shell: true,
});

// Start frontend server after a delay
setTimeout(() => {
  console.log("⚛️ Starting frontend server...");
  const frontendProcess = spawn("npm", ["run", "dev"], {
    cwd: frontendDir,
    stdio: "inherit",
    shell: true,
  });

  // Handle frontend process exit
  frontendProcess.on("close", (code) => {
    console.log(`\n⚛️ Frontend server exited with code ${code}`);
    backendProcess.kill();
    process.exit(code || 0);
  });

  frontendProcess.on("error", (error) => {
    console.error("❌ Frontend server error:", error.message);
    backendProcess.kill();
    process.exit(1);
  });
}, 3000); // 3 second delay

// Handle backend process exit
backendProcess.on("close", (code) => {
  console.log(`\n🔧 Backend server exited with code ${code}`);
  process.exit(code || 0);
});

backendProcess.on("error", (error) => {
  console.error("❌ Backend server error:", error.message);
  process.exit(1);
});

// Handle graceful shutdown
process.on("SIGINT", () => {
  console.log("\n🛑 Shutting down servers...");
  backendProcess.kill();
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.log("\n🛑 Shutting down servers...");
  backendProcess.kill();
  process.exit(0);
});
