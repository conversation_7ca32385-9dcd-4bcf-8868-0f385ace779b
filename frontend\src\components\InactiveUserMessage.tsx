import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/hooks/useAuth'
import { AlertTriangle, LayoutDashboard, LogOut, Mail, Phone } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

interface InactiveUserMessageProps {
  onGoHome?: () => void
}

export function InactiveUserMessage({ onGoHome }: InactiveUserMessageProps) {
  const { user, logout } = useAuth()
  const navigate = useNavigate()

  const handleGoToDashboard = () => {
    if (onGoHome) {
      onGoHome()
    } else {
      navigate('/voter/dashboard')
    }
  }

  const handleLogout = async () => {
    await logout()
    navigate('/login')
  }

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-amber-100">
            <AlertTriangle className="h-6 w-6 text-amber-600" />
          </div>
          <CardTitle className="text-xl font-semibold">Account Inactive</CardTitle>
        </CardHeader>

        <CardContent className="space-y-4">
          <Alert className="border-amber-200 bg-amber-50">
            <AlertTriangle className="h-4 w-4 text-amber-600" />
            <AlertTitle className="text-amber-800">Access Restricted</AlertTitle>
            <AlertDescription className="text-amber-700">
              Your account is inactive. You cannot participate in voting. Please contact an administrator.
            </AlertDescription>
          </Alert>

          <div className="space-y-3">
            <div className="text-sm text-muted-foreground">
              <p><strong>Account:</strong> {user?.username}</p>
              <p><strong>Municipality:</strong> {user?.municipality}</p>
              <p><strong>District:</strong> {user?.district}</p>
              <p><strong>Status:</strong> <span className="text-amber-600 font-medium">Inactive</span></p>
            </div>
          </div>

          <div className="border-t pt-4">
            <h4 className="font-medium text-sm mb-2">Need Help?</h4>
            <div className="space-y-2 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4" />
                <span>SocMob Phone No. +63 ************</span>
              </div>
            </div>
          </div>

          <div className="flex flex-col space-y-2 pt-4">
            <Button
              onClick={handleGoToDashboard}
              variant="outline"
              className="w-full flex items-center gap-2"
            >
              <LayoutDashboard className="h-4 w-4" />
              Go to Dashboard
            </Button>
            <Button
              onClick={handleLogout}
              className="w-full flex items-center gap-2"
            >
              <LogOut className="h-4 w-4" />
              Logout
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default InactiveUserMessage
