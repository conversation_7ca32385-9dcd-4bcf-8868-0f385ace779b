import PDFDocument from 'pdfkit'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

/**
 * Generate PDF export with specific DFPTA formatting
 */
export class PDFExportService {
  constructor() {
    this.doc = null
    this.pageWidth = 612 // Letter size width
    this.pageHeight = 792 // Letter size height
    this.margin = 72 // 1 inch margins
    this.contentWidth = this.pageWidth - (this.margin * 2)
  }

  /**
   * Create a new PDF document with DFPTA header formatting
   */
  createDocument() {
    this.doc = new PDFDocument({
      size: 'LETTER',
      margins: {
        top: this.margin,
        bottom: this.margin,
        left: this.margin,
        right: this.margin
      }
    })

    return this.doc
  }

  /**
   * Add DFPTA header with exact formatting specifications
   */
  addDFPTAHeader() {
    if (!this.doc) {
      throw new Error('PDF document not initialized')
    }

    const centerX = this.pageWidth / 2
    let currentY = this.margin

    // Line 1: "Republika ng Pilipinas" (Old English Text MT, 12pt, Bold)
    this.doc
      .font('Times-Bold') // Fallback font since Old English Text MT may not be available
      .fontSize(12)
      .text('Republika ng Pilipinas', centerX, currentY, {
        align: 'center',
        width: this.contentWidth
      })

    currentY += 20

    // Line 2: "Kagawaran ng Edukasyon" (Old English Text MT, 17pt, Bold)
    this.doc
      .font('Times-Bold')
      .fontSize(17)
      .text('Kagawaran ng Edukasyon', centerX, currentY, {
        align: 'center',
        width: this.contentWidth
      })

    currentY += 25

    // Line 3: "Rehiyon V" (Trajan Pro, 10pt, Bold)
    this.doc
      .font('Times-Bold') // Fallback font since Trajan Pro may not be available
      .fontSize(10)
      .text('Rehiyon V', centerX, currentY, {
        align: 'center',
        width: this.contentWidth
      })

    currentY += 18

    // Line 4: "TANGGAPAN NG MGA PAARALANG PANSANGAY NG CAMARINES SUR" (Tahoma, 10pt, Bold)
    this.doc
      .font('Helvetica-Bold') // Fallback font since Tahoma may not be available
      .fontSize(10)
      .text('TANGGAPAN NG MGA PAARALANG PANSANGAY NG CAMARINES SUR', centerX, currentY, {
        align: 'center',
        width: this.contentWidth
      })

    currentY += 30

    // Add separator line
    this.doc
      .moveTo(this.margin, currentY)
      .lineTo(this.pageWidth - this.margin, currentY)
      .stroke()

    return currentY + 20
  }

  /**
   * Add election results content
   */
  addElectionResults(results, statistics, generationDate) {
    if (!this.doc) {
      throw new Error('PDF document not initialized')
    }

    let currentY = this.addDFPTAHeader()

    // Title
    this.doc
      .font('Helvetica-Bold')
      .fontSize(16)
      .text('DFPTA ELECTION RESULTS', this.margin, currentY, {
        align: 'center',
        width: this.contentWidth
      })

    currentY += 30

    // Generation date and time
    this.doc
      .font('Helvetica')
      .fontSize(10)
      .text(`Generated on: ${generationDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })}`, this.margin, currentY, {
        align: 'right',
        width: this.contentWidth
      })

    currentY += 25

    // Statistics section
    this.doc
      .font('Helvetica-Bold')
      .fontSize(12)
      .text('ELECTION STATISTICS', this.margin, currentY)

    currentY += 20

    const statsData = [
      ['Total Voters:', statistics.totalVoters?.toString() || '0'],
      ['Votes Cast:', statistics.totalVotesCast?.toString() || '0'],
      ['Participation Rate:', `${statistics.participationRate?.toFixed(1) || '0.0'}%`],
      ['Total Candidates:', statistics.totalCandidates?.toString() || '0']
    ]

    this.doc.font('Helvetica').fontSize(10)
    statsData.forEach(([label, value]) => {
      this.doc.text(label, this.margin, currentY, { continued: true })
      this.doc.text(value, this.margin + 150, currentY)
      currentY += 15
    })

    currentY += 20

    // Results table
    this.doc
      .font('Helvetica-Bold')
      .fontSize(12)
      .text('ELECTION RESULTS', this.margin, currentY)

    currentY += 20

    // Table headers
    const colWidths = [60, 200, 120, 80]
    const colPositions = [
      this.margin,
      this.margin + colWidths[0],
      this.margin + colWidths[0] + colWidths[1],
      this.margin + colWidths[0] + colWidths[1] + colWidths[2]
    ]

    this.doc
      .font('Helvetica-Bold')
      .fontSize(10)
      .text('Rank', colPositions[0], currentY)
      .text('Municipality', colPositions[1], currentY)
      .text('District', colPositions[2], currentY)
      .text('Votes', colPositions[3], currentY)

    currentY += 20

    // Table separator
    this.doc
      .moveTo(this.margin, currentY)
      .lineTo(this.pageWidth - this.margin, currentY)
      .stroke()

    currentY += 10

    // Results data
    this.doc.font('Helvetica').fontSize(9)
    
    if (results && results.length > 0) {
      results.forEach((candidate, index) => {
        // Check if we need a new page
        if (currentY > this.pageHeight - 100) {
          this.doc.addPage()
          currentY = this.margin
        }

        const rank = candidate.rank || (index + 1)
        const municipality = candidate.municipalityName || 'Unknown'
        const district = candidate.district || 'Unknown'
        const votes = candidate.voteCount?.toString() || '0'

        this.doc
          .text(rank.toString(), colPositions[0], currentY)
          .text(municipality, colPositions[1], currentY)
          .text(district, colPositions[2], currentY)
          .text(votes, colPositions[3], currentY)

        currentY += 15
      })
    } else {
      this.doc.text('No results available', this.margin, currentY)
    }

    return currentY
  }

  /**
   * Finalize and save PDF to buffer
   */
  async finalize() {
    if (!this.doc) {
      throw new Error('PDF document not initialized')
    }

    return new Promise((resolve, reject) => {
      const buffers = []
      
      this.doc.on('data', buffers.push.bind(buffers))
      this.doc.on('end', () => {
        const pdfBuffer = Buffer.concat(buffers)
        resolve(pdfBuffer)
      })
      this.doc.on('error', reject)
      
      this.doc.end()
    })
  }
}

/**
 * Generate PDF export for election results
 */
export const generateElectionResultsPDF = async (results, statistics) => {
  try {
    const pdfService = new PDFExportService()
    const doc = pdfService.createDocument()
    
    const generationDate = new Date()
    pdfService.addElectionResults(results, statistics, generationDate)
    
    const pdfBuffer = await pdfService.finalize()
    return pdfBuffer
  } catch (error) {
    throw new Error(`PDF generation failed: ${error.message}`)
  }
}

export default PDFExportService
