import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { apiClient } from '@/services/apiClient'
import { logger } from '@/utils/logger'
import { AlertTriangle, Archive, Database, RotateCcw } from 'lucide-react'
import { useState } from 'react'
import { toast } from 'react-hot-toast'

export function ElectionArchiveControl() {
  const [isArchiving, setIsArchiving] = useState(false)
  const [isResetting, setIsResetting] = useState(false)
  const [showArchiveDialog, setShowArchiveDialog] = useState(false)
  const [showResetDialog, setShowResetDialog] = useState(false)

  // Archive form state
  const [archiveTitle, setArchiveTitle] = useState('')
  const [archiveDescription, setArchiveDescription] = useState('')

  // Reset form state
  const [resetConfirmation, setResetConfirmation] = useState('')
  const [resetReason, setResetReason] = useState('')

  const handleArchiveElection = async () => {
    if (!archiveTitle || archiveTitle.trim().length < 3) {
      toast.error('Please enter a title (minimum 3 characters)')
      return
    }

    if (archiveTitle.trim().length > 100) {
      toast.error('Title must be 100 characters or less')
      return
    }

    setIsArchiving(true)
    try {
      const response = await apiClient.post('/admin/election/archive', {
        title: archiveTitle.trim(),
        description: archiveDescription
      })

      if (response.data.success) {
        toast.success(`Election results archived successfully: "${archiveTitle}"`)
        logger.info('Election results archived', {
          metadata: { title: archiveTitle, description: archiveDescription }
        })
        setShowArchiveDialog(false)
        setArchiveTitle('')
        setArchiveDescription('')
      }
    } catch (error: any) {
      const message = error.response?.data?.error || 'Failed to archive election results'
      toast.error(message)
      logger.error('Failed to archive election results', error)
    } finally {
      setIsArchiving(false)
    }
  }

  const handleResetElection = async () => {
    if (resetConfirmation !== 'RESET ELECTION SYSTEM') {
      toast.error('Please type the confirmation text exactly as shown')
      return
    }

    setIsResetting(true)
    try {
      const response = await apiClient.post('/admin/election/reset', {
        confirmationText: resetConfirmation,
        reason: resetReason
      })

      if (response.data.success) {
        const { votesDeleted, votersReset, candidatesReset } = response.data.data
        toast.success(`Election system reset successfully! ${votesDeleted} votes deleted, ${votersReset} voters reset, ${candidatesReset} candidates reset.`)
        logger.info('Election system reset', {
          metadata: { votesDeleted, votersReset, candidatesReset, reason: resetReason }
        })
        setShowResetDialog(false)
        setResetConfirmation('')
        setResetReason('')
      }
    } catch (error: any) {
      const message = error.response?.data?.error || 'Failed to reset election system'
      toast.error(message)
      logger.error('Failed to reset election system', error)
    } finally {
      setIsResetting(false)
    }
  }

  return (
    <Card className="border-l-4 border-l-orange-500">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Database className="h-5 w-5 text-orange-500" />
            <CardTitle className="text-lg">Election Archive & Reset</CardTitle>
          </div>
          <Badge variant="outline" className="flex items-center gap-1">
            <Archive className="h-3 w-3" />
            System Management
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Archive Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h3 className="font-semibold flex items-center space-x-2">
                <Archive className="h-4 w-4" />
                <span>Archive Current Results</span>
              </h3>
              <p className="text-sm text-muted-foreground">
                Save current election results to historical archive before starting a new election
              </p>
            </div>

            <Button
              onClick={() => setShowArchiveDialog(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Archive className="h-4 w-4 mr-2" />
              Archive Results
            </Button>
          </div>
        </div>

        {/* Reset Section */}
        <div className="border-t pt-4 space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h3 className="font-semibold flex items-center space-x-2">
                <RotateCcw className="h-4 w-4" />
                <span>Reset Election System</span>
              </h3>
              <p className="text-sm text-muted-foreground">
                Clear all votes and reset voter status to prepare for a new election
              </p>
            </div>

            <Button
              onClick={() => setShowResetDialog(true)}
              variant="destructive"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset System
            </Button>
          </div>
        </div>

        {/* Warning Notice */}
        <div className="rounded-lg bg-amber-50 border border-amber-200 p-4">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0" />
            <div className="text-xs text-amber-700">
              <p className="font-medium text-amber-800 mb-1">Important:</p>
              <ul className="space-y-1">
                <li>• Always archive results before resetting the system</li>
                <li>• Reset operation cannot be undone (backup is created automatically)</li>
                <li>• All votes will be permanently deleted</li>
                <li>• All voters will be marked as "not voted"</li>
                <li>• Candidate vote counts will be reset to zero</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Archive Dialog */}
        <AlertDialog open={showArchiveDialog} onOpenChange={setShowArchiveDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Archive Election Results</AlertDialogTitle>
              <AlertDialogDescription>
                This will save the current election results to the historical archive.
                You can archive results multiple times for different years.
              </AlertDialogDescription>
            </AlertDialogHeader>

            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="archive-title">Archive Title</Label>
                <Input
                  id="archive-title"
                  type="text"
                  value={archiveTitle}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setArchiveTitle(e.target.value)}
                  placeholder="e.g., 2024 DFPTA Election, Mid-Year Election 2024"
                  maxLength={100}
                />
                <p className="text-xs text-muted-foreground">
                  3-100 characters. Can include letters, numbers, spaces, and special characters.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="archive-description">Description (Optional)</Label>
                <Textarea
                  id="archive-description"
                  value={archiveDescription}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setArchiveDescription(e.target.value)}
                  placeholder="e.g., Annual DFPTA Board Election"
                  maxLength={500}
                />
              </div>
            </div>

            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setShowArchiveDialog(false)}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleArchiveElection}
                disabled={isArchiving}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isArchiving ? 'Archiving...' : 'Archive Results'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Reset Dialog */}
        <AlertDialog open={showResetDialog} onOpenChange={setShowResetDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="text-red-600">Reset Election System</AlertDialogTitle>
              <AlertDialogDescription>
                <strong>WARNING:</strong> This action will permanently delete all votes and reset all voter status.
                This operation cannot be undone. A backup will be created automatically.
              </AlertDialogDescription>
            </AlertDialogHeader>

            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="reset-reason">Reason for Reset</Label>
                <Textarea
                  id="reset-reason"
                  value={resetReason}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setResetReason(e.target.value)}
                  placeholder="e.g., Preparing for 2025 election"
                  maxLength={500}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="reset-confirmation">
                  Type "RESET ELECTION SYSTEM" to confirm
                </Label>
                <Input
                  id="reset-confirmation"
                  value={resetConfirmation}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setResetConfirmation(e.target.value)}
                  placeholder="RESET ELECTION SYSTEM"
                  className="font-mono"
                />
              </div>
            </div>

            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setShowResetDialog(false)}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleResetElection}
                disabled={isResetting || resetConfirmation !== 'RESET ELECTION SYSTEM'}
                className="bg-red-600 hover:bg-red-700"
              >
                {isResetting ? 'Resetting...' : 'Reset System'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  )
}
