import axios from 'axios'
import dotenv from 'dotenv'
import logger from '../utils/logger.js'

// Load environment variables
dotenv.config({ path: './backend/.env' })

const API_BASE_URL = 'http://localhost:5000/api'

/**
 * Test the toggle API endpoints
 */
const testToggleAPI = async () => {
  try {
    logger.info('🚀 Testing toggle API endpoints...')
    
    // Login as admin
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'socmob123'
    })
    
    if (!loginResponse.data.success) {
      throw new Error('Failed to login as admin')
    }
    
    const adminToken = loginResponse.data.data.token
    logger.info('✅ Admin login successful')
    
    // Test municipality names toggle
    logger.info('🔄 Testing municipality names toggle...')
    
    // Get current setting
    const currentSettingResponse = await axios.get(`${API_BASE_URL}/system/settings/show_municipality_names`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    })
    
    const currentValue = currentSettingResponse.data.data.value
    logger.info(`Current municipality names setting: ${currentValue}`)
    
    // Toggle the setting
    const toggleResponse = await axios.put(`${API_BASE_URL}/system/settings/show_municipality_names`, {
      value: !currentValue
    }, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    })
    
    if (toggleResponse.data.success) {
      logger.info('✅ Municipality names toggle successful:', {
        from: currentValue,
        to: toggleResponse.data.data.value
      })
    } else {
      logger.error('❌ Municipality names toggle failed:', toggleResponse.data.error)
    }
    
    // Restore original value
    await axios.put(`${API_BASE_URL}/system/settings/show_municipality_names`, {
      value: currentValue
    }, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    })
    
    logger.info('✅ Restored original municipality names setting')
    
    // Test district results toggle
    logger.info('🔄 Testing district results toggle...')
    
    // Get current setting
    const currentDistrictSettingResponse = await axios.get(`${API_BASE_URL}/system/settings/show_district_results`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    })
    
    const currentDistrictValue = currentDistrictSettingResponse.data.data.value
    logger.info(`Current district results setting: ${currentDistrictValue}`)
    
    // Toggle the setting
    const toggleDistrictResponse = await axios.put(`${API_BASE_URL}/system/settings/show_district_results`, {
      value: !currentDistrictValue
    }, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    })
    
    if (toggleDistrictResponse.data.success) {
      logger.info('✅ District results toggle successful:', {
        from: currentDistrictValue,
        to: toggleDistrictResponse.data.data.value
      })
    } else {
      logger.error('❌ District results toggle failed:', toggleDistrictResponse.data.error)
    }
    
    // Restore original value
    await axios.put(`${API_BASE_URL}/system/settings/show_district_results`, {
      value: currentDistrictValue
    }, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    })
    
    logger.info('✅ Restored original district results setting')
    
    // Test public results to see if settings are applied
    logger.info('🔄 Testing public results with settings...')
    
    const publicResultsResponse = await axios.get(`${API_BASE_URL}/results`)
    
    if (publicResultsResponse.data.success) {
      const { showMunicipalityNames, showDistrictResults } = publicResultsResponse.data.data.results
      logger.info('✅ Public results settings:', {
        showMunicipalityNames,
        showDistrictResults
      })
    }
    
    logger.info('🎉 All toggle API tests completed successfully!')
    
  } catch (error) {
    logger.error('💥 Toggle API test failed:', error.response?.data || error.message)
  }
}

// Run test
testToggleAPI()
