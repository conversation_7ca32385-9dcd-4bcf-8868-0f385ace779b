import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { useSystemSettings } from '@/hooks/useSystemSettings'
import { AlertTriangle, CheckCircle, Play, Square } from 'lucide-react'
import React, { useState } from 'react'
import toast from 'react-hot-toast'

interface ElectionControlToggleProps {
  className?: string
}

export const ElectionControlToggle: React.FC<ElectionControlToggleProps> = ({ className }) => {
  const { isElectionActive, toggleElection, isLoading, error } = useSystemSettings()
  const [isToggling, setIsToggling] = useState(false)

  const handleToggleElection = async () => {
    setIsToggling(true)

    try {
      const success = await toggleElection()

      if (success) {
        const action = !isElectionActive ? 'opened' : 'closed'
        toast.success(`Election ${action} successfully. Voting is now ${!isElectionActive ? 'enabled' : 'disabled'} system-wide.`)
      } else {
        toast.error('Failed to toggle election status. Please try again or contact support if the issue persists.')
      }
    } catch (err) {
      toast.error('An unexpected error occurred. Please try again or contact support.')
    } finally {
      setIsToggling(false)
    }
  }

  const getStatusInfo = () => {
    if (isElectionActive) {
      return {
        status: 'Open',
        description: 'Voting is currently enabled system-wide',
        icon: <Play className='h-4 w-4' />,
        badgeVariant: 'default' as const,
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
      }
    } else {
      return {
        status: 'Closed',
        description: 'Voting is currently disabled system-wide',
        icon: <Square className='h-4 w-4' />,
        badgeVariant: 'secondary' as const,
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
      }
    }
  }

  const statusInfo = getStatusInfo()

  if (error) {
    return (
      <Card className={`${className} border-red-200 bg-red-50`}>
        <CardHeader>
          <CardTitle className='flex items-center gap-2 text-red-600'>
            <AlertTriangle className='h-5 w-5' />
            Election Control Error
          </CardTitle>
          <CardDescription className='text-red-600'>{error}</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card className={`${className} ${statusInfo.bgColor} ${statusInfo.borderColor}`}>
      <CardHeader>
        <CardTitle className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            {statusInfo.icon}
            Election Control
          </div>
          <Badge variant={statusInfo.badgeVariant} className='flex items-center gap-1'>
            {isElectionActive ? (
              <CheckCircle className='h-3 w-3' />
            ) : (
              <Square className='h-3 w-3' />
            )}
            {statusInfo.status}
          </Badge>
        </CardTitle>
        <CardDescription>
          Master control for enabling or disabling voting system-wide
        </CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='flex items-center justify-between'>
          <div className='space-y-1'>
            <Label htmlFor='election-toggle' className='text-sm font-medium'>
              Election Status
            </Label>
            <p className={`text-sm ${statusInfo.color}`}>{statusInfo.description}</p>
          </div>
          <Switch
            id='election-toggle'
            checked={isElectionActive}
            disabled={isLoading || isToggling}
            onCheckedChange={() => {
              // We'll handle the actual toggle through the AlertDialog
            }}
          />
        </div>

        <div className='border-t pt-2'>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant={isElectionActive ? 'destructive' : 'default'}
                className='w-full'
                disabled={isLoading || isToggling}
              >
                {isToggling ? (
                  <>
                    <div className='mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white' />
                    {isElectionActive ? 'Closing Election...' : 'Opening Election...'}
                  </>
                ) : (
                  <>
                    {isElectionActive ? (
                      <>
                        <Square className='mr-2 h-4 w-4' />
                        Close Election
                      </>
                    ) : (
                      <>
                        <Play className='mr-2 h-4 w-4' />
                        Open Election
                      </>
                    )}
                  </>
                )}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle className='flex items-center gap-2'>
                  <AlertTriangle className='h-5 w-5 text-amber-500' />
                  {isElectionActive ? 'Close Election?' : 'Open Election?'}
                </AlertDialogTitle>
                <AlertDialogDescription>
                  {isElectionActive ? (
                    <>
                      This will <strong>disable voting system-wide</strong>. All voters will be
                      unable to access voting pages or cast votes until the election is reopened.
                      <br />
                      <br />
                      Are you sure you want to close the election?
                    </>
                  ) : (
                    <>
                      This will <strong>enable voting system-wide</strong>. All eligible voters will
                      be able to access voting pages and cast their votes.
                      <br />
                      <br />
                      Are you sure you want to open the election?
                    </>
                  )}
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel disabled={isToggling}>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleToggleElection}
                  disabled={isToggling}
                  className={isElectionActive ? 'bg-red-600 hover:bg-red-700' : ''}
                >
                  {isToggling ? (
                    <>
                      <div className='mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white' />
                      {isElectionActive ? 'Closing...' : 'Opening...'}
                    </>
                  ) : (
                    <>{isElectionActive ? 'Close Election' : 'Open Election'}</>
                  )}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>

        <div className='border-t pt-2 text-xs text-gray-500'>
          <p>
            <strong>Note:</strong> Changes take effect immediately. When closed, voters will see an
            appropriate message when trying to access voting pages.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
