import { useEffect, useState } from 'react'

// Components
import { AdminPasswordResetForm } from '@/components/admin/AdminPasswordResetForm/AdminPasswordResetForm'
import { DistrictResultsControl } from '@/components/admin/DistrictResultsControl'
import { ElectionArchiveControl } from '@/components/admin/ElectionArchiveControl'
import { ElectionControlToggle } from '@/components/admin/ElectionControlToggle/ElectionControlToggle'
import { ElectionHistory } from '@/components/admin/ElectionHistory'
import { ExportResults } from '@/components/admin/ExportResults'
import { MunicipalityNamesControl } from '@/components/admin/MunicipalityNamesControl'
import { AdminHeader } from '@/components/organisms/AdminHeader'

import { ResultsDisplayTable } from '@/components/admin/ResultsDisplayTable'
import { ResultVisibilityControl } from '@/components/admin/ResultVisibilityControl'
import { UserActivityLogs } from '@/components/admin/UserActivityLogs'
import { UserManagementTable } from '@/components/admin/UserManagementTable'
import { VoteTrackingCards } from '@/components/admin/VoteTrackingCards'
import { StatisticsCards } from '@/components/organisms/StatisticsCards'
import { UserFilters } from '@/components/organisms/UserFilters'
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

// Hooks
import {
    useAdminDashboard,
    useAdminResults,
    useBulkUserOperations,
    useResetUserVote,
    useToggleUserStatus,
    useUsers
} from '@/hooks/useAdmin'
import { useDistrictResultsControl } from '@/hooks/useDistrictResultsControl'
import { useMunicipalityNamesControl } from '@/hooks/useMunicipalityNamesControl'
import { useResultVisibility } from '@/hooks/useResultVisibility'
import { useDistrictsAndMunicipalities } from '@/hooks/useSystem'

// Utils
import { getBackendStatus } from '@/utils/healthCheck'
import { logger } from '@/utils/logger'

// Types
import { UserFilters as UserFiltersType } from '@/schemas/user'
import { User } from '@/types'

const AdminDashboard = () => {
  // Backend status
  const [backendStatus, setBackendStatus] = useState<string>('Checking...')

  // Result visibility control
  const {
    isResultsVisible,
    isLoading: isVisibilityLoading,
    toggleVisibility,
  } = useResultVisibility()

  // Municipality names control
  const {
    showMunicipalityNames,
    isLoading: isMunicipalityNamesLoading,
    toggleMunicipalityNames,
  } = useMunicipalityNamesControl()

  // District results control
  const {
    showDistrictResults,
    isLoading: isDistrictResultsLoading,
    toggleDistrictResults,
  } = useDistrictResultsControl()

  // State for filters and pagination
  const [filters, setFilters] = useState<UserFiltersType>({
    page: 1,
    limit: 10,
    search: '',
    role: undefined,
    municipality: undefined,
    district: undefined,
    hasVoted: undefined,
    isActive: undefined,
    sort: 'createdAt',
    order: 'desc',
  })

  // State for user management
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [isPasswordResetDialogOpen, setIsPasswordResetDialogOpen] = useState(false)
  const [passwordResetUser, setPasswordResetUser] = useState<User | null>(null)

  // State for admin results
  const [showCandidateNames, setShowCandidateNames] = useState(false)

  // State for current section
  const [currentSection, setCurrentSection] = useState('dashboard')

  const handleSectionChange = (section: string) => {
    setCurrentSection(section)
    // Add any additional section change logic here
  }

  // API hooks
  const { data: dashboardData, isLoading: isDashboardLoading } = useAdminDashboard()
  const { data: usersData, isLoading: isUsersLoading } = useUsers(filters)
  const { data: adminResults, isLoading: isResultsLoading } = useAdminResults(
    showCandidateNames,
    'votes'
  )

  const resetVoteMutation = useResetUserVote()
  const toggleUserStatusMutation = useToggleUserStatus()
  const bulkOperationMutation = useBulkUserOperations()

  // System data hooks
  const { data: systemData } = useDistrictsAndMunicipalities()

  // Check backend status on mount
  useEffect(() => {
    const checkStatus = async () => {
      const status = await getBackendStatus()
      setBackendStatus(status)
    }

    checkStatus()

    // Check status every 30 seconds
    const interval = setInterval(checkStatus, 30000)
    return () => clearInterval(interval)
  }, [])

  // Debug password reset dialog state
  useEffect(() => {
    if (isPasswordResetDialogOpen) {
      logger.info('Password reset dialog opened', {
        component: 'AdminDashboard',
        action: 'dialogStateChange',
        metadata: {
          isOpen: isPasswordResetDialogOpen,
          hasUser: !!passwordResetUser,
          userId: passwordResetUser?.id,
          username: passwordResetUser?.username,
        },
      })
    }
  }, [isPasswordResetDialogOpen, passwordResetUser])

  // Ensure dialog closes if user is cleared
  useEffect(() => {
    if (!passwordResetUser && isPasswordResetDialogOpen) {
      logger.info('Password reset dialog closing - no user', {
        component: 'AdminDashboard',
        action: 'dialogAutoClose',
        metadata: { isOpen: isPasswordResetDialogOpen },
      })
      setIsPasswordResetDialogOpen(false)
    }
  }, [passwordResetUser, isPasswordResetDialogOpen])

  // Filter handlers
  const handleFiltersChange = (newFilters: Partial<UserFiltersType>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  const handleFiltersReset = () => {
    setFilters({
      page: 1,
      limit: 10,
      search: '',
      role: undefined,
      municipality: undefined,
      district: undefined,
      hasVoted: undefined,
      isActive: undefined,
      sort: 'createdAt',
      order: 'desc',
    })
  }

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }))
  }

  // User management handlers
  const handleSelectUser = (userId: string) => {
    setSelectedUsers(prev =>
      prev.includes(userId) ? prev.filter(id => id !== userId) : [...prev, userId]
    )
  }

  const handleSelectAll = (selected: boolean) => {
    if (selected && usersData?.data) {
      setSelectedUsers(usersData.data.users.map(user => user.id))
    } else {
      setSelectedUsers([])
    }
  }





  const handleResetVote = (userId: string) => {
    resetVoteMutation.mutate(userId)
  }

  const handleResetPassword = (user: User) => {
    logger.info('Password reset requested', {
      component: 'AdminDashboard',
      action: 'handleResetPassword',
      metadata: { userId: user.id, username: user.username },
    })

    // Set both states at the same time
    setPasswordResetUser(user)
    setIsPasswordResetDialogOpen(true)
  }



  const handleToggleUserStatus = (userId: string, isActive: boolean) => {
    toggleUserStatusMutation.mutate({
      id: userId,
      isActive
    })
  }

  const handleBulkOperation = (action: string) => {
    if (selectedUsers.length === 0) return

    bulkOperationMutation.mutate(
      { userIds: selectedUsers, action: action as any },
      {
        onSuccess: () => {
          setSelectedUsers([])
        },
      }
    )
  }



  const handleToggleNames = (show: boolean) => {
    setShowCandidateNames(show)
  }

  const handleStatCardClick = (cardType: string) => {
    switch (cardType) {
      case 'voted-users':
        handleFiltersChange({ hasVoted: true })
        break
      case 'pending-votes':
        handleFiltersChange({ hasVoted: false })
        break
      case 'active-users':
        handleFiltersChange({ isActive: true })
        break
      case 'voters':
        handleFiltersChange({ role: 'voter' })
        break
      case 'admins':
        handleFiltersChange({ role: 'admin' })
        break
      default:
        break
    }
  }

  return (
    <div className='min-h-screen bg-gradient-to-br from-gray-50 to-white'>
      {/* Standardized Admin Header */}
      <AdminHeader
        currentSection={currentSection}
        onSectionChange={handleSectionChange}
      />

      <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-10'>
        {/* Backend Status Indicator */}
        {!backendStatus.includes('🟢') && (
          <div className='mb-4'>
            <Card className='border-orange-200 bg-orange-50'>
              <CardContent className='py-3'>
                <div className='flex items-center justify-between'>
                  <div className='flex items-center space-x-2'>
                    <span className='text-sm font-medium text-orange-800'>Backend Status:</span>
                    <span className='text-sm text-orange-700'>{backendStatus}</span>
                  </div>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={async () => {
                      setBackendStatus('Checking...')
                      const status = await getBackendStatus()
                      setBackendStatus(status)
                    }}
                  >
                    Retry
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Dashboard Section */}
        <div id="dashboard" className='mb-8'>
          {/* Election Control Toggle */}
          <div className='mb-8'>
            <ElectionControlToggle />
          </div>

          {/* Enhanced Statistics Cards */}
          <div className='mb-8'>
            <StatisticsCards
              data={dashboardData?.stats || null}
              loading={isDashboardLoading}
              onCardClick={handleStatCardClick}
            />
          </div>

          {/* Vote Tracking Cards */}
          <div className='mb-8'>
            <h2 className='mb-4 text-2xl font-semibold'>Vote Tracking & Analytics</h2>
            <VoteTrackingCards
              stats={adminResults?.totalStats || null}
              isLoading={isResultsLoading}
            />
          </div>
        </div>

        {/* Result Visibility Control */}
        <div className='mb-8'>
          <ResultVisibilityControl
            isResultsVisible={isResultsVisible}
            onToggleVisibility={toggleVisibility}
            isLoading={isVisibilityLoading}
          />
        </div>

        {/* Municipality Names Control */}
        <div className='mb-8'>
          <MunicipalityNamesControl
            showMunicipalityNames={showMunicipalityNames}
            onToggleNames={toggleMunicipalityNames}
            isLoading={isMunicipalityNamesLoading}
          />
        </div>

        {/* District Results Control */}
        <div className='mb-8'>
          <DistrictResultsControl
            showDistrictResults={showDistrictResults}
            onToggleDistrictResults={toggleDistrictResults}
            isLoading={isDistrictResultsLoading}
          />
        </div>

        {/* Archive Section */}
        <div id="archive" className='mb-8'>
          <ElectionArchiveControl />
        </div>

        {/* Results Section */}
        <div id="results" className='mb-8'>
          <ResultsDisplayTable
            resultsByDistrict={adminResults?.resultsByDistrict || null}
            unifiedResults={adminResults?.unifiedResults}
            totalStats={adminResults?.totalStats || null}
            showCandidateNames={showCandidateNames}
            isLoading={isResultsLoading}
            onToggleNames={handleToggleNames}
          />
        </div>

        {/* Logs Section */}
        <div id="logs" className='mb-8 grid gap-6 lg:grid-cols-2'>
          <ExportResults
            resultsByDistrict={adminResults?.resultsByDistrict || null}
            totalStats={adminResults?.totalStats || null}
          />
          <UserActivityLogs />
        </div>

        {/* Election History */}
        <div className='mb-8'>
          <ElectionHistory />
        </div>

        {/* Users Section */}
        <div id="users">
          <Card>
            <CardHeader>
              <div className='flex items-center justify-between'>
                <CardTitle>User Management</CardTitle>
                <div className='text-sm text-muted-foreground'>
                  Manage existing users and their status
                </div>
              </div>
            </CardHeader>
          <CardContent>
            {/* Enhanced Filters */}
            <div className='mb-6'>
              <UserFilters
                filters={filters}
                onFiltersChange={handleFiltersChange}
                onReset={handleFiltersReset}
                districts={systemData?.districts || []}
                municipalities={systemData?.municipalities || []}
                loading={isUsersLoading}
              />
            </div>

            {/* Bulk Actions */}
            {selectedUsers.length > 0 && (
              <div className='mb-4 flex items-center gap-2 rounded-lg bg-muted p-3'>
                <span className='text-sm font-medium'>{selectedUsers.length} user(s) selected</span>
                <div className='ml-auto flex gap-2'>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant='outline' size='sm'>
                        Activate
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Activate Users</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to activate {selectedUsers.length} selected user(s)?
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleBulkOperation('activate')}>
                          Activate
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant='outline' size='sm'>
                        Deactivate
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Deactivate Users</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to deactivate {selectedUsers.length} selected
                          user(s)?
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleBulkOperation('deactivate')}>
                          Deactivate
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant='destructive' size='sm'>
                        Delete
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Users</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete {selectedUsers.length} selected user(s)?
                          This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleBulkOperation('delete')}
                          className='bg-red-600 hover:bg-red-700'
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            )}

            {/* Users Table */}
            <UserManagementTable
              users={usersData?.data?.users || []}
              isLoading={isUsersLoading}
              onResetVote={handleResetVote}
              onResetPassword={handleResetPassword}
              onToggleUserStatus={handleToggleUserStatus}
              selectedUsers={selectedUsers}
              onSelectUser={handleSelectUser}
              onSelectAll={handleSelectAll}
            />

            {/* Pagination */}
            {usersData?.data?.pagination && (
              <div className='mt-6 flex items-center justify-between'>
                <div className='text-sm text-muted-foreground'>
                  Showing{' '}
                  {(usersData.data.pagination.currentPage - 1) * usersData.data.pagination.limit +
                    1}{' '}
                  to{' '}
                  {Math.min(
                    usersData.data.pagination.currentPage * usersData.data.pagination.limit,
                    usersData.data.pagination.totalUsers
                  )}{' '}
                  of {usersData.data.pagination.totalUsers} users
                </div>
                <div className='flex items-center space-x-2'>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => handlePageChange(usersData.data.pagination.currentPage - 1)}
                    disabled={!usersData.data.pagination.hasPrevPage}
                  >
                    Previous
                  </Button>
                  <span className='text-sm'>
                    Page {usersData.data.pagination.currentPage} of{' '}
                    {usersData.data.pagination.totalPages}
                  </span>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => handlePageChange(usersData.data.pagination.currentPage + 1)}
                    disabled={!usersData.data.pagination.hasNextPage}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        </div>

        {/* Settings Section */}
        <div id="settings" className='mb-8'>
          {/* Settings content can be added here */}
        </div>

        {/* Password Reset Dialog */}
        {isPasswordResetDialogOpen && passwordResetUser && (
          <AdminPasswordResetForm
            user={passwordResetUser}
            isOpen={isPasswordResetDialogOpen}
            onClose={() => {
              logger.info('Password reset dialog closed', {
                component: 'AdminDashboard',
                action: 'closePasswordResetDialog',
                metadata: { hadUser: !!passwordResetUser },
              })
              setIsPasswordResetDialogOpen(false)
              setPasswordResetUser(null)
            }}
            onSuccess={() => {
              logger.info('Password reset dialog success', {
                component: 'AdminDashboard',
                action: 'passwordResetSuccess',
                metadata: { hadUser: !!passwordResetUser },
              })
              setIsPasswordResetDialogOpen(false)
              setPasswordResetUser(null)
            }}
          />
        )}

        {/* Debug info */}
        {process.env.NODE_ENV === 'development' && (
          <div className='fixed bottom-4 right-4 rounded bg-black/80 p-2 text-xs text-white'>
            <div>Dialog Open: {isPasswordResetDialogOpen ? 'Yes' : 'No'}</div>
            <div>Has User: {passwordResetUser ? 'Yes' : 'No'}</div>
            <div>User ID: {passwordResetUser?.id || 'None'}</div>
          </div>
        )}
      </div>
    </div>
  )
}

export default AdminDashboard
