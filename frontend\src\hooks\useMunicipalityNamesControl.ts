import { apiClient } from '@/services/apiClient'
import { logger } from '@/utils/logger'
import { useState } from 'react'
import { useSystemSettings } from './useSystemSettings'

export function useMunicipalityNamesControl() {
  const { getSetting, refreshSettings } = useSystemSettings()
  const [isLoading, setIsLoading] = useState(false)

  const showMunicipalityNames = getSetting('show_municipality_names')?.value || false

  const toggleMunicipalityNames = async (show: boolean) => {
    setIsLoading(true)

    try {
      const response = await apiClient.put('/system/settings/show_municipality_names', {
        value: show
      })

      if (response.data.success) {
        // Refresh the cached settings to get the updated value
        await refreshSettings()

        logger.info('Municipality names visibility changed', {
          newState: show ? 'visible' : 'hidden',
          timestamp: new Date().toISOString(),
          action: 'admin_toggle_municipality_names'
        })

        return { success: true }
      } else {
        throw new Error(response.data.error || 'Failed to update municipality names setting')
      }
    } catch (error) {
      logger.error('Failed to update municipality names visibility', { error })
      return { success: false, error: 'Failed to update municipality names setting' }
    } finally {
      setIsLoading(false)
    }
  }

  return {
    showMunicipalityNames,
    isLoading,
    toggleMunicipalityNames,
  }
}
