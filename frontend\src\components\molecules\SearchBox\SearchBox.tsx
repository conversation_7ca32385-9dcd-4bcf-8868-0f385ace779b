import { cn } from '@/lib/utils'
import { Search, X } from 'lucide-react'
import { useCallback, useEffect, useRef, useState } from 'react'
import { Button } from '../../atoms/Button'
import { Input } from '../../atoms/Input'

export interface SearchBoxProps {
  placeholder?: string
  value?: string
  onSearch: (query: string) => void
  onClear?: () => void
  debounceMs?: number
  className?: string
  disabled?: boolean
  loading?: boolean
  showClearButton?: boolean
  size?: 'default' | 'sm' | 'lg'
}

export function SearchBox({
  placeholder = 'Search...',
  value = '',
  onSearch,
  onClear,
  debounceMs = 300,
  className,
  disabled = false,
  loading = false,
  showClearButton = true,
  size = 'default',
}: SearchBoxProps) {
  const [searchValue, setSearchValue] = useState(value)
  const debounceRef = useRef<NodeJS.Timeout>()

  // Update internal state when external value changes
  useEffect(() => {
    setSearchValue(value)
  }, [value])

  // Debounced search function
  const debouncedSearch = useCallback(
    (query: string) => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }

      debounceRef.current = setTimeout(() => {
        onSearch(query)
      }, debounceMs)
    },
    [onSearch, debounceMs]
  )

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setSearchValue(newValue)
    debouncedSearch(newValue)
  }

  // Handle clear
  const handleClear = () => {
    setSearchValue('')
    onSearch('')
    onClear?.()
  }

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
      onSearch(searchValue)
    }
  }

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
    }
  }, [])

  return (
    <div className={cn('relative', className)}>
      <Input
        type="text"
        placeholder={placeholder}
        value={searchValue}
        onChange={handleInputChange}
        onKeyPress={handleKeyPress}
        leftIcon={<Search className="h-4 w-4" />}
        rightIcon={
          showClearButton && searchValue ? (
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-4 w-4 p-0 hover:bg-transparent"
              onClick={handleClear}
              disabled={disabled}
            >
              <X className="h-3 w-3" />
            </Button>
          ) : undefined
        }
        disabled={disabled}
        loading={loading}
        size={size}
        className="pr-8"
      />
    </div>
  )
}

export default SearchBox
