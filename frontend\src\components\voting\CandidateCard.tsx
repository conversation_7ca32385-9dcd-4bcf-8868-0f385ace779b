import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import { Candidate } from '@/services/votingService'
import { Check, UserX } from 'lucide-react'
import { toast } from 'sonner'

interface CandidateCardProps {
  candidate: Candidate
  isSelected: boolean
  onToggle: (candidateId: string) => void
  disabled?: boolean
  showDetails?: boolean
}

export function CandidateCard({
  candidate,
  isSelected,
  onToggle,
  disabled = false,
  showDetails = false,
}: CandidateCardProps) {
  const handleClick = () => {
    if (!disabled && !candidate.isDisabled) {
      onToggle(candidate._id)
    } else if (candidate.isDisabled) {
      console.warn(`Attempted to select inactive candidate: ${candidate.municipalityName}`)
      toast.error(`${candidate.municipalityName} is not available for selection`, {
        description: 'This candidate is currently inactive.'
      })
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      handleClick()
    }
  }

  const isDisabledCandidate = candidate.isDisabled || disabled
  const isInactive = candidate.isDisabled

  const cardContent = (
    <Card
      className={cn(
        'relative cursor-pointer transition-all duration-200 hover:shadow-md h-24',
        'focus-within:ring-primary border-2 focus-within:ring-2 focus-within:ring-offset-1',
        isSelected ? 'border-primary bg-accent shadow-md' : 'border-border hover:border-primary/50',
        isDisabledCandidate && 'cursor-not-allowed opacity-50 grayscale',
        candidate.isDisabled && 'bg-muted/30',
        isInactive && 'bg-gray-100 border-gray-300 cursor-not-allowed opacity-60'
      )}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={isDisabledCandidate ? -1 : 0}
      role='button'
      aria-pressed={isSelected}
      aria-label={`${isSelected ? 'Deselect' : 'Select'} ${candidate.municipalityName} (${candidate.district})`}
    >
      {/* Selection indicator */}
      <div
        className={cn(
          'absolute right-1 top-1 flex h-4 w-4 items-center justify-center rounded-full border transition-all',
          isSelected
            ? 'border-primary bg-primary text-primary-foreground'
            : 'border-muted-foreground bg-background',
          candidate.isDisabled && 'border-red-300 bg-red-50'
        )}
      >
        {candidate.isDisabled ? (
          <UserX className='h-2 w-2 text-red-500' />
        ) : (
          isSelected && <Check className='h-2 w-2' />
        )}
      </div>

      <CardContent className='p-2 h-full flex flex-col justify-center'>
        <div className='text-center'>
          <h3 className='text-xs font-semibold leading-tight truncate mb-1'>
            {candidate.municipalityName}
          </h3>

          <Badge variant='outline' className='text-xs px-1 py-0 h-4'>
            {candidate.district}
          </Badge>

          {/* Removed "Absent" label - visual distinction is handled by styling */}

          {/* Vote count if available and showing details */}
          {candidate.totalVotes > 0 && showDetails && (
            <div className='text-xs text-muted-foreground mt-1'>
              {candidate.totalVotes} votes
            </div>
          )}
        </div>
      </CardContent>



      {/* Selection overlay for better visual feedback */}
      {isSelected && (
        <div className='bg-primary/5 pointer-events-none absolute inset-0 rounded-lg' />
      )}
    </Card>
  )

  // Wrap with tooltip if candidate is disabled or inactive
  if (candidate.isDisabled || isInactive) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {cardContent}
          </TooltipTrigger>
          <TooltipContent>
            <p>ABSENT - This candidate cannot be selected</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  return cardContent
}

// Compact version for mobile or dense layouts
export function CandidateCardCompact({
  candidate,
  isSelected,
  onToggle,
  disabled = false,
}: CandidateCardProps) {
  const handleClick = () => {
    if (!disabled && !candidate.isDisabled) {
      onToggle(candidate._id)
    } else if (candidate.isDisabled) {
      console.warn(`Attempted to select inactive candidate: ${candidate.municipalityName}`)
      toast.error(`${candidate.municipalityName} is not available for selection`, {
        description: 'This candidate is currently inactive.'
      })
    }
  }

  const isDisabledCandidate = candidate.isDisabled || disabled
  const isInactive = candidate.isDisabled
  const compactContent = (
    <div
      className={cn(
        'flex cursor-pointer items-center rounded-lg border-2 p-2 transition-all h-12',
        'focus-within:ring-primary focus-within:ring-2 focus-within:ring-offset-1 hover:shadow-sm',
        isSelected ? 'border-primary bg-accent' : 'border-border hover:border-primary/50',
        isDisabledCandidate && 'cursor-not-allowed opacity-50 grayscale',
        candidate.isDisabled && 'bg-muted/30'
      )}
      onClick={handleClick}
      role='button'
      tabIndex={isDisabledCandidate ? -1 : 0}
      aria-pressed={isSelected}
    >
      {/* Checkbox */}
      <div
        className={cn(
          'mr-2 flex h-4 w-4 flex-shrink-0 items-center justify-center rounded border',
          isSelected
            ? 'border-primary bg-primary text-primary-foreground'
            : 'border-muted-foreground',
          candidate.isDisabled && 'border-red-300 bg-red-50'
        )}
      >
        {candidate.isDisabled ? (
          <UserX className='h-2 w-2 text-red-500' />
        ) : (
          isSelected && <Check className='h-2 w-2' />
        )}
      </div>

      {/* Municipality info */}
      <div className='min-w-0 flex-1'>
        <div className='truncate text-sm font-medium'>{candidate.municipalityName}</div>
        <div className='text-muted-foreground truncate text-xs'>{candidate.district}</div>

        {/* Removed "Absent" label - visual distinction is handled by styling */}
      </div>

      {/* Vote count badge */}
      <div className='ml-1 flex items-center space-x-1'>
        {candidate.totalVotes > 0 && (
          <Badge variant='secondary' className='text-xs px-1 py-0 h-4'>
            {candidate.totalVotes}
          </Badge>
        )}
        {candidate.isWinner && (
          <Badge variant='default' className='text-xs px-1 py-0 h-4'>
            W
          </Badge>
        )}
      </div>
    </div>
  )

  // Wrap with tooltip if candidate is disabled or inactive
  if (candidate.isDisabled || isInactive) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {compactContent}
          </TooltipTrigger>
          <TooltipContent>
            <p>ABSENT - This candidate cannot be selected</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  return compactContent
}
