import { Button } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { logger } from '@/utils/logger'
import { AlertTriangle, RefreshCw } from 'lucide-react'
import React, { Component, ReactNode } from 'react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    logger.error('Error boundary caught an error', error, {
      component: 'ErrorBoundary',
      action: 'componentDidCatch',
      metadata: {
        errorInfo: errorInfo.componentStack,
      },
    })
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className='flex min-h-screen items-center justify-center bg-background p-4'>
          <Card className='w-full max-w-md'>
            <CardHeader>
              <CardTitle className='flex items-center gap-2 text-destructive'>
                <AlertTriangle className='h-5 w-5' />
                Something went wrong
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <p className='text-muted-foreground'>
                An unexpected error occurred. This might be due to a network issue or backend
                connectivity problem.
              </p>

              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className='text-xs'>
                  <summary className='cursor-pointer text-muted-foreground'>
                    Error details (development only)
                  </summary>
                  <pre className='mt-2 overflow-auto rounded bg-muted p-2 text-xs'>
                    {this.state.error.toString()}
                    {this.state.errorInfo?.componentStack}
                  </pre>
                </details>
              )}

              <div className='flex gap-2'>
                <Button onClick={this.handleRetry} className='flex-1'>
                  <RefreshCw className='mr-2 h-4 w-4' />
                  Try Again
                </Button>
                <Button
                  variant='outline'
                  onClick={() => window.location.reload()}
                  className='flex-1'
                >
                  Reload Page
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}
