import { useQuery } from '@tanstack/react-query'
import { DistrictMunicipalityData, MunicipalityDetails, SystemConfig, systemService } from '../services/systemService'

/**
 * Hook to get districts and municipalities dynamically
 */
export function useDistrictsAndMunicipalities() {
  return useQuery<DistrictMunicipalityData, Error>({
    queryKey: ['system', 'districts-municipalities'],
    queryFn: () => systemService.getDistrictsAndMunicipalities(),
    staleTime: 10 * 60 * 1000, // 10 minutes - this data doesn't change often
    gcTime: 30 * 60 * 1000, // 30 minutes (renamed from cacheTime)
    retry: 3,
    refetchOnWindowFocus: false,
  })
}

/**
 * Hook to get system configuration
 */
export function useSystemConfig() {
  return useQuery<SystemConfig, Error>({
    queryKey: ['system', 'config'],
    queryFn: () => systemService.getSystemConfig(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes (renamed from cacheTime)
    retry: 3,
    refetchOnWindowFocus: false,
  })
}

/**
 * Hook to get municipality details
 */
export function useMunicipalityDetails(municipalityName: string) {
  return useQuery<MunicipalityDetails, Error>({
    queryKey: ['system', 'municipality', municipalityName],
    queryFn: () => systemService.getMunicipalityDetails(municipalityName),
    enabled: !!municipalityName,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime)
    retry: 2,
  })
}

/**
 * Hook to get district for municipality
 */
export function useDistrictForMunicipality(municipalityName: string) {
  return useQuery<string, Error>({
    queryKey: ['system', 'district-for-municipality', municipalityName],
    queryFn: () => systemService.getDistrictForMunicipality(municipalityName),
    enabled: !!municipalityName,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes (renamed from cacheTime)
    retry: 2,
  })
}

/**
 * Hook to get municipalities in district
 */
export function useMunicipalitiesInDistrict(district: string) {
  return useQuery<string[], Error>({
    queryKey: ['system', 'municipalities-in-district', district],
    queryFn: () => systemService.getMunicipalitiesInDistrict(district),
    enabled: !!district,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes (renamed from cacheTime)
    retry: 2,
  })
}

/**
 * Hook to get available roles
 */
export function useAvailableRoles() {
  return useQuery<string[], Error>({
    queryKey: ['system', 'available-roles'],
    queryFn: () => systemService.getAvailableRoles(),
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 60 * 60 * 1000, // 1 hour (renamed from cacheTime)
    retry: 2,
    refetchOnWindowFocus: false,
  })
}

/**
 * Hook to get max candidates per vote
 */
export function useMaxCandidatesPerVote() {
  return useQuery<number, Error>({
    queryKey: ['system', 'max-candidates-per-vote'],
    queryFn: () => systemService.getMaxCandidatesPerVote(),
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 60 * 60 * 1000, // 1 hour (renamed from cacheTime)
    retry: 2,
    refetchOnWindowFocus: false,
  })
}

/**
 * Custom hook for form validation helpers
 */
export function useSystemValidation() {
  const { data: districtMunicipalityData } = useDistrictsAndMunicipalities()

  const validateMunicipality = (municipalityName: string): boolean => {
    if (!districtMunicipalityData) return false
    const data = districtMunicipalityData as DistrictMunicipalityData
    return data.municipalities.includes(municipalityName)
  }

  const validateDistrict = (district: string): boolean => {
    if (!districtMunicipalityData) return false
    const data = districtMunicipalityData as DistrictMunicipalityData
    return data.districts.includes(district)
  }

  const getDistrictForMunicipality = (municipalityName: string): string | null => {
    if (!districtMunicipalityData) return null
    const data = districtMunicipalityData as DistrictMunicipalityData

    for (const [district, municipalities] of Object.entries(data.districtMapping)) {
      if ((municipalities as string[]).includes(municipalityName)) {
        return district
      }
    }
    return null
  }

  const getMunicipalitiesInDistrict = (district: string): string[] => {
    if (!districtMunicipalityData) return []
    const data = districtMunicipalityData as DistrictMunicipalityData
    return data.districtMapping[district] || []
  }

  return {
    validateMunicipality,
    validateDistrict,
    getDistrictForMunicipality,
    getMunicipalitiesInDistrict,
    isLoading: !districtMunicipalityData,
  }
}
