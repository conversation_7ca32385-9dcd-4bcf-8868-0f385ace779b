import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/hooks/useAuth'
import { CheckCircle, Loader2, LogOut, Shield, User, XCircle } from 'lucide-react'
import { ReactNode, useState } from 'react'
import toast from 'react-hot-toast'
import { useNavigate } from 'react-router-dom'

interface LogoutDialogProps {
  children: ReactNode
  showConfirmation?: boolean
  redirectTo?: string
  onLogoutSuccess?: () => void
  onLogoutError?: (error: any) => void
  className?: string
}

export function LogoutDialog({
  children,
  showConfirmation = true,
  redirectTo = '/',
  onLogoutSuccess,
  onLogoutError,
  className,
}: LogoutDialogProps) {
  const { user, logout } = useAuth()
  const navigate = useNavigate()
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const [isOpen, setIsOpen] = useState(false)

  const handleLogout = async () => {
    if (!showConfirmation) {
      await performLogout()
      return
    }

    setIsOpen(true)
  }

  const performLogout = async () => {
    setIsLoggingOut(true)
    
    try {
      const success = await logout(false) // Don't show browser confirmation since we have our own
      
      if (success) {
        // Enhanced success feedback
        toast.success(
          `👋 Goodbye, ${user?.municipality || user?.username}!\n\nYou have been successfully logged out.`,
          {
            duration: 4000,
            style: {
              background: '#10b981',
              color: '#ffffff',
              fontWeight: '500',
              borderRadius: '8px',
              boxShadow: '0 10px 25px rgba(16, 185, 129, 0.3)',
            },
            iconTheme: {
              primary: '#ffffff',
              secondary: '#10b981',
            },
          }
        )

        // Call success callback
        onLogoutSuccess?.()

        // Small delay for better UX before redirect
        setTimeout(() => {
          navigate(redirectTo)
        }, 1000)
      } else {
        throw new Error('Logout failed')
      }
    } catch (error) {
      // Enhanced error feedback
      toast.error(
        `❌ Logout Failed\n\nThere was an issue logging you out. Please try again.`,
        {
          duration: 6000,
          style: {
            background: '#ef4444',
            color: '#ffffff',
            fontWeight: '500',
            borderRadius: '8px',
            boxShadow: '0 10px 25px rgba(239, 68, 68, 0.3)',
          },
          iconTheme: {
            primary: '#ffffff',
            secondary: '#ef4444',
          },
        }
      )

      onLogoutError?.(error)
    } finally {
      setIsLoggingOut(false)
      setIsOpen(false)
    }
  }

  const handleCancel = () => {
    setIsOpen(false)
  }

  if (!showConfirmation) {
    return (
      <div onClick={handleLogout} className={className}>
        {children}
      </div>
    )
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        <div onClick={handleLogout} className={className}>
          {children}
        </div>
      </AlertDialogTrigger>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2 text-lg">
            {isLoggingOut ? (
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
            ) : (
              <LogOut className="h-5 w-5 text-destructive" />
            )}
            {isLoggingOut ? 'Logging Out...' : 'Confirm Logout'}
          </AlertDialogTitle>
          <AlertDialogDescription asChild>
            <div className="space-y-4">
              {!isLoggingOut ? (
                <>
                  <div className="text-base">
                    Are you sure you want to log out of your account?
                  </div>
                  
                  {user && (
                    <div className="flex items-center gap-3 p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border">
                      <div className="p-2 bg-primary/10 rounded-full">
                        {user.role === 'admin' ? (
                          <Shield className="h-4 w-4 text-primary" />
                        ) : (
                          <User className="h-4 w-4 text-primary" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">
                          {user.municipality || user.username}
                        </div>
                        <div className="text-sm text-gray-600 capitalize">
                          {user.role} account
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center gap-2 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                    <div className="text-amber-600 text-lg">ℹ️</div>
                    <div className="text-sm text-amber-800">
                      You will be redirected to the home page and will need to log in again to access your account.
                    </div>
                  </div>
                </>
              ) : (
                <div className="space-y-4 py-4">
                  <div className="text-center">
                    <div className="text-base font-medium text-gray-900 mb-2">
                      Signing you out...
                    </div>
                    <div className="text-sm text-gray-600">
                      Please wait while we securely log you out.
                    </div>
                  </div>
                  <div className="flex justify-center">
                    <div className="animate-pulse flex space-x-2">
                      <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                      <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="gap-3">
          <AlertDialogCancel 
            onClick={handleCancel}
            disabled={isLoggingOut}
            className="disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoggingOut ? 'Please Wait...' : 'Cancel'}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={performLogout}
            disabled={isLoggingOut}
            className="bg-destructive hover:bg-destructive/90 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 min-w-[120px]"
            aria-label={isLoggingOut ? 'Logging out, please wait' : 'Confirm logout'}
          >
            {isLoggingOut ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Logging Out...</span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                <span>Yes, Logout</span>
              </div>
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
