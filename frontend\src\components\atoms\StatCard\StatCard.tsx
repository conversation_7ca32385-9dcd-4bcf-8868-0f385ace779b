import { ReactNode } from 'react'
import { cva, type VariantProps } from 'class-variance-authority'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { cn } from '@/lib/utils'

const statCardVariants = cva(
  'transition-all duration-200 hover:shadow-md',
  {
    variants: {
      variant: {
        default: 'border-l-4 border-l-primary',
        success: 'border-l-4 border-l-success',
        warning: 'border-l-4 border-l-warning',
        error: 'border-l-4 border-l-destructive',
        info: 'border-l-4 border-l-blue-500',
      },
      size: {
        default: '',
        sm: 'text-sm',
        lg: 'text-lg',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

export interface StatCardProps extends VariantProps<typeof statCardVariants> {
  title: string
  value: string | number
  subtitle?: string
  icon?: ReactNode
  trend?: {
    value: number
    label: string
    isPositive?: boolean
  }
  loading?: boolean
  className?: string
  onClick?: () => void
}

export function StatCard({
  title,
  value,
  subtitle,
  icon,
  trend,
  loading = false,
  variant,
  size,
  className,
  onClick,
}: StatCardProps) {
  if (loading) {
    return (
      <Card className={cn(statCardVariants({ variant, size }), className)}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-4 rounded" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-16 mb-2" />
          <Skeleton className="h-3 w-32" />
        </CardContent>
      </Card>
    )
  }

  const cardContent = (
    <>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        {icon && (
          <div className="text-muted-foreground">
            {icon}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-foreground">
          {typeof value === 'number' ? value.toLocaleString() : value}
        </div>
        
        {subtitle && (
          <p className="text-xs text-muted-foreground mt-1">
            {subtitle}
          </p>
        )}
        
        {trend && (
          <div className="flex items-center mt-2">
            <span
              className={cn(
                'text-xs font-medium',
                trend.isPositive !== false
                  ? 'text-success'
                  : 'text-destructive'
              )}
            >
              {trend.isPositive !== false ? '+' : ''}
              {trend.value}%
            </span>
            <span className="text-xs text-muted-foreground ml-1">
              {trend.label}
            </span>
          </div>
        )}
      </CardContent>
    </>
  )

  if (onClick) {
    return (
      <Card
        className={cn(
          statCardVariants({ variant, size }),
          'cursor-pointer hover:bg-accent/50',
          className
        )}
        onClick={onClick}
      >
        {cardContent}
      </Card>
    )
  }

  return (
    <Card className={cn(statCardVariants({ variant, size }), className)}>
      {cardContent}
    </Card>
  )
}

export default StatCard
