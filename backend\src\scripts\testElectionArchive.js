import axios from 'axios'
import dotenv from 'dotenv'
import logger from '../utils/logger.js'

// Load environment variables
dotenv.config({ path: './backend/.env' })

const API_BASE_URL = 'http://localhost:5000/api'

/**
 * Test election archive functionality
 */
const testElectionArchive = async () => {
  try {
    logger.info('🚀 Testing election archive functionality...')

    // Login as admin
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'socmob123'
    })

    if (!loginResponse.data.success) {
      throw new Error('Failed to login as admin')
    }

    const adminToken = loginResponse.data.data.token
    logger.info('✅ Admin login successful')

    // Get current election results to verify data
    logger.info('📊 Getting current election results...')

    const resultsResponse = await axios.get(`${API_BASE_URL}/admin/results`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    })

    if (resultsResponse.data.success) {
      const { unifiedResults, totalStats } = resultsResponse.data.data
      logger.info('✅ Current election data:', {
        totalCandidates: unifiedResults.length,
        totalVotes: totalStats.totalVotesCast,
        participationRate: totalStats.participationRate
      })

      // Show top 5 candidates
      const top5 = unifiedResults.slice(0, 5)
      logger.info('📈 Top 5 candidates:', top5.map(c => ({
        municipality: c.municipalityName,
        district: c.district,
        votes: c.voteCount
      })))
    }

    // Test archive creation
    logger.info('📦 Testing archive creation...')

    const testYear = new Date().getFullYear()
    const testDescription = `Test archive created on ${new Date().toISOString()}`

    try {
      const archiveResponse = await axios.post(`${API_BASE_URL}/admin/election/archive`, {
        year: testYear,
        description: testDescription
      }, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (archiveResponse.data.success) {
        const archive = archiveResponse.data.data.archive
        logger.info('✅ Archive created successfully:', {
          year: archive.year,
          description: archive.description,
          totalCandidates: archive.statistics.totalCandidates,
          totalVotesCast: archive.statistics.totalVotesCast,
          participationRate: archive.statistics.participationRate,
          resultsCount: archive.results.length
        })

        // Show top 5 archived results
        const top5Archived = archive.results.slice(0, 5)
        logger.info('📈 Top 5 archived results:', top5Archived.map(r => ({
          rank: r.rank,
          municipality: r.municipalityName,
          district: r.district,
          votes: r.voteCount
        })))

        // Verify data integrity
        if (archive.results.length > 0) {
          logger.info('✅ Archive contains candidate results')
        } else {
          logger.warn('⚠️ Archive has no candidate results')
        }

        if (archive.statistics.totalCandidates > 0) {
          logger.info('✅ Archive has valid statistics')
        } else {
          logger.warn('⚠️ Archive statistics may be incomplete')
        }

      } else {
        logger.error('❌ Archive creation failed:', archiveResponse.data.error)
      }

    } catch (archiveError) {
      if (archiveError.response?.status === 400 &&
          archiveError.response?.data?.error?.includes('already exists')) {
        logger.info('ℹ️ Archive for this year already exists - this is expected')

        // Try with a different year
        const alternateYear = testYear + 1
        logger.info(`🔄 Trying with year ${alternateYear}...`)

        const alternateArchiveResponse = await axios.post(`${API_BASE_URL}/admin/election/archive`, {
          year: alternateYear,
          description: `Test archive for ${alternateYear}`
        }, {
          headers: { 'Authorization': `Bearer ${adminToken}` }
        })

        if (alternateArchiveResponse.data.success) {
          logger.info('✅ Alternate year archive created successfully')
        }

      } else {
        throw archiveError
      }
    }

    // Test edge case: archive with no votes
    logger.info('🧪 Testing edge case handling...')

    // This would require resetting all votes first, which we don't want to do in a test
    // So we'll just verify the archive handles the current state correctly

    logger.info('✅ Archive functionality appears to be working correctly')

    logger.info('🎉 Election archive test completed successfully!')

  } catch (error) {
    logger.error('💥 Election archive test failed:', {
      message: error.message,
      status: error.response?.status,
      data: error.response?.data,
      stack: error.stack
    })
  }
}

// Run test
testElectionArchive()
