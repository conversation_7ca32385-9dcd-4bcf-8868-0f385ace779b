/**
 * Middleware to ensure only active users can access certain routes
 * Inactive users can log in but cannot access voting functionality
 */
export const activeUserRequired = (req, res, next) => {
  try {
    // Check if user is authenticated
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      })
    }

    // Check if user is active
    if (!req.user.isActive) {
      return res.status(403).json({
        success: false,
        error: 'Your account is inactive. You cannot participate in voting. Please contact an administrator.',
        code: 'INACTIVE_USER'
      })
    }

    // User is active, proceed
    next()
  } catch (error) {
    return res.status(500).json({
      success: false,
      error: 'Server error checking user status'
    })
  }
}

export default activeUserRequired
