import { z } from 'zod'

// Districts enum
export const DISTRICTS = [
  '1st District',
  '2nd District',
  '3rd District',
  '4th District',
  '5th District',
] as const

// User roles enum
export const USER_ROLES = ['voter', 'admin'] as const

// Municipalities by district - Updated to match backend DISTRICT_MAPPING
export const MUNICIPALITIES_BY_DISTRICT = {
  '1st District': ['Cabusao', 'Del Gallego', 'Lupi', 'Ragay', 'Sipocot'],
  '2nd District': ['Gainza', 'Libmanan', 'Milaor', 'Minalabac', 'Pamplona', 'Pasacao', 'San Fernando'],
  '3rd District': ['<PERSON>on', 'Calabanga', 'Camaligan', 'Canaman', 'Magarao', 'Ocampo', 'Pili'],
  '4th District': [
    'Caramoan',
    'Garchitorena',
    'Goa',
    'Lagonoy',
    '<PERSON>rub<PERSON>',
    '<PERSON><PERSON><PERSON>',
    'San Jose',
    'Sir<PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>mba<PERSON>',
  ],
  '5th District': ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
} as const

// Base user schema
export const baseUserSchema = z.object({
  username: z
    .string()
    .min(3, 'Username must be at least 3 characters')
    .max(50, 'Username cannot exceed 50 characters')
    .regex(
      /^[a-zA-Z0-9_-]+$/,
      'Username can only contain letters, numbers, underscores, and hyphens'
    )
    .toLowerCase(),
  municipality: z
    .string()
    .min(1, 'Municipality is required')
    .refine(val => {
      const allMunicipalities = Object.values(MUNICIPALITIES_BY_DISTRICT).flat()
      return allMunicipalities.includes(val as any)
    }, 'Invalid municipality'),
  district: z.enum(DISTRICTS, {
    errorMap: () => ({ message: 'Invalid district' }),
  }),
  role: z.enum(USER_ROLES, {
    errorMap: () => ({ message: 'Invalid user role. Only voter and admin roles are allowed.' }),
  }),
})

// Create user schema
export const createUserSchema = baseUserSchema
  .extend({
    password: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .max(128, 'Password cannot exceed 128 characters')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
        'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
      ),
    confirmPassword: z.string(),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  })
  .refine(
    data => {
      // Validate municipality belongs to district
      const districtMunicipalities = MUNICIPALITIES_BY_DISTRICT[data.district]
      return districtMunicipalities.includes(data.municipality as any)
    },
    {
      message: 'Municipality does not belong to the selected district',
      path: ['municipality'],
    }
  )

// Update user schema
export const updateUserSchema = baseUserSchema
  .partial()
  .extend({
    isActive: z.boolean().optional(),
    hasVoted: z.boolean().optional(),
  })
  .refine(
    data => {
      // If both district and municipality are provided, validate they match
      if (data.district && data.municipality) {
        const districtMunicipalities = MUNICIPALITIES_BY_DISTRICT[data.district]
        return districtMunicipalities.includes(data.municipality as any)
      }
      return true
    },
    {
      message: 'Municipality does not belong to the selected district',
      path: ['municipality'],
    }
  )

// User profile update schema (for self-updates)
export const userProfileUpdateSchema = z
  .object({
    // Currently no fields available for user self-update
    // Municipality and district are immutable after creation
  })
  .strict()

// Bulk user operations schema
export const bulkUserOperationSchema = z.object({
  userIds: z
    .array(z.string().min(1, 'User ID cannot be empty'))
    .min(1, 'At least one user must be selected')
    .max(100, 'Cannot perform bulk operations on more than 100 users at once'),
  action: z.enum(['activate', 'deactivate', 'reset-votes'], {
    errorMap: () => ({ message: 'Invalid bulk operation action' }),
  }),
})

// User filters schema
export const userFiltersSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
  search: z.string().max(100).optional(),
  role: z.enum([...USER_ROLES, 'all']).optional(),
  municipality: z.string().max(50).optional(),
  district: z.enum([...DISTRICTS, 'all']).optional(),
  hasVoted: z.boolean().optional(),
  isActive: z.boolean().optional(),
  sort: z
    .enum(['createdAt', 'updatedAt', 'username', 'municipality', 'lastLogin'])
    .default('createdAt'),
  order: z.enum(['asc', 'desc']).default('desc'),
})

// User import schema
export const userImportSchema = z.object({
  users: z
    .array(
      z.object({
        username: z.string().min(3).max(50),
        municipality: z.string().min(1),
        district: z.enum(DISTRICTS),
        role: z.enum(USER_ROLES).default('voter'),
        password: z.string().min(8).optional(), // Optional, will be generated if not provided
      })
    )
    .min(1, 'At least one user is required')
    .max(1000, 'Cannot import more than 1000 users at once'),
  overwriteExisting: z.boolean().default(false),
  sendWelcomeEmail: z.boolean().default(false),
})

// User response schema
export const userResponseSchema = z.object({
  id: z.string(),
  username: z.string(),
  municipality: z.string(),
  district: z.enum(DISTRICTS),
  role: z.enum(USER_ROLES),
  hasVoted: z.boolean(),
  isActive: z.boolean(),
  lastLogin: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),

})

// Paginated users response schema
export const paginatedUsersResponseSchema = z.object({
  users: z.array(userResponseSchema),
  pagination: z.object({
    currentPage: z.number(),
    totalPages: z.number(),
    totalUsers: z.number(),
    limit: z.number(),
    hasNextPage: z.boolean(),
    hasPrevPage: z.boolean(),
  }),
})

// User statistics schema
export const userStatsSchema = z.object({
  totalUsers: z.number(),
  totalVoters: z.number(),
  votedUsers: z.number(),
  activeUsers: z.number(),
  usersByRole: z.record(z.string(), z.number()),
  usersByDistrict: z.record(z.string(), z.number()),
  votingProgress: z.number(),
  recentUsers: z.array(userResponseSchema),
})

// Type exports
export type CreateUserFormData = z.infer<typeof createUserSchema>
export type UpdateUserFormData = z.infer<typeof updateUserSchema>
export type UserProfileUpdateFormData = z.infer<typeof userProfileUpdateSchema>
export type BulkUserOperationData = z.infer<typeof bulkUserOperationSchema>
export type UserFilters = z.infer<typeof userFiltersSchema>
export type UserImportData = z.infer<typeof userImportSchema>
export type UserResponse = z.infer<typeof userResponseSchema>
export type PaginatedUsersResponse = z.infer<typeof paginatedUsersResponseSchema>
export type UserStats = z.infer<typeof userStatsSchema>

// Validation helper functions
export const validateCreateUser = (data: unknown): CreateUserFormData => {
  return createUserSchema.parse(data)
}

export const validateUpdateUser = (data: unknown): UpdateUserFormData => {
  return updateUserSchema.parse(data)
}

export const validateUserProfileUpdate = (data: unknown): UserProfileUpdateFormData => {
  return userProfileUpdateSchema.parse(data)
}

export const validateBulkUserOperation = (data: unknown): BulkUserOperationData => {
  return bulkUserOperationSchema.parse(data)
}

export const validateUserFilters = (data: unknown): UserFilters => {
  return userFiltersSchema.parse(data)
}

export const validateUserImport = (data: unknown): UserImportData => {
  return userImportSchema.parse(data)
}

export const validateUserResponse = (data: unknown): UserResponse => {
  return userResponseSchema.parse(data)
}

export const validatePaginatedUsersResponse = (data: unknown): PaginatedUsersResponse => {
  return paginatedUsersResponseSchema.parse(data)
}

export const validateUserStats = (data: unknown): UserStats => {
  return userStatsSchema.parse(data)
}

// Helper functions
export const getMunicipalitiesByDistrict = (district: string): string[] => {
  return MUNICIPALITIES_BY_DISTRICT[district as keyof typeof MUNICIPALITIES_BY_DISTRICT] || []
}

export const getDistrictByMunicipality = (municipality: string): string | null => {
  for (const [district, municipalities] of Object.entries(MUNICIPALITIES_BY_DISTRICT)) {
    if (municipalities.includes(municipality as any)) {
      return district
    }
  }
  return null
}

export const isValidMunicipalityForDistrict = (municipality: string, district: string): boolean => {
  const districtMunicipalities =
    MUNICIPALITIES_BY_DISTRICT[district as keyof typeof MUNICIPALITIES_BY_DISTRICT]
  return districtMunicipalities?.includes(municipality as any) || false
}

// User role permissions
export const USER_PERMISSIONS = {
  voter: {
    canVote: true,
    canViewResults: true,
    canViewProfile: true,
    canUpdateProfile: false,
  },
  admin: {
    canVote: false,
    canViewResults: true,
    canViewProfile: true,
    canUpdateProfile: true,
    canManageUsers: true,
    canManageSystem: true,
    canViewAnalytics: true,
    canExportData: true,
  },
  // Executive committee and tie-breaker permissions removed - features no longer supported
} as const

export const hasPermission = (role: string, permission: string): boolean => {
  const rolePermissions = USER_PERMISSIONS[role as keyof typeof USER_PERMISSIONS]
  return rolePermissions?.[permission as keyof typeof rolePermissions] || false
}
