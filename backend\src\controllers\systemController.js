import asyncHandler from 'express-async-handler'
import Candidate from '../models/Candidate.js'
import SystemSettings from '../models/SystemSettings.js'
import User from '../models/User.js'
import VotingSession from '../models/VotingSession.js'
import logger from '../utils/logger.js'

/**
 * @desc    Get all districts and municipalities dynamically from database
 * @route   GET /api/system/districts-municipalities
 * @access  Public
 */
export const getDistrictsAndMunicipalities = asyncHandler(async (req, res) => {
  try {
    // Get all unique districts and municipalities from candidates
    const candidateData = await Candidate.aggregate([
      {
        $group: {
          _id: '$district',
          municipalities: { $addToSet: '$municipalityName' },
        },
      },
      {
        $sort: { _id: 1 },
      },
    ])

    // Also get from users as backup
    const userData = await User.aggregate([
      {
        $group: {
          _id: '$district',
          municipalities: { $addToSet: '$municipality' },
        },
      },
      {
        $sort: { _id: 1 },
      },
    ])

    // Merge and deduplicate data
    const districtMap = {}
    const allDistricts = new Set()
    const allMunicipalities = new Set()

    // Process candidate data
    candidateData.forEach(item => {
      if (item._id) {
        allDistricts.add(item._id)
        if (!districtMap[item._id]) {
          districtMap[item._id] = new Set()
        }
        item.municipalities.forEach(municipality => {
          if (municipality) {
            districtMap[item._id].add(municipality)
            allMunicipalities.add(municipality)
          }
        })
      }
    })

    // Process user data as backup
    userData.forEach(item => {
      if (item._id) {
        allDistricts.add(item._id)
        if (!districtMap[item._id]) {
          districtMap[item._id] = new Set()
        }
        item.municipalities.forEach(municipality => {
          if (municipality) {
            districtMap[item._id].add(municipality)
            allMunicipalities.add(municipality)
          }
        })
      }
    })

    // Convert to arrays and sort
    const districts = Array.from(allDistricts).sort()
    const municipalities = Array.from(allMunicipalities).sort()

    // Convert district mapping to proper format
    const districtMapping = {}
    Object.keys(districtMap).forEach(district => {
      districtMapping[district] = Array.from(districtMap[district]).sort()
    })

    res.status(200).json({
      success: true,
      data: {
        districts,
        municipalities,
        districtMapping,
        totalDistricts: districts.length,
        totalMunicipalities: municipalities.length,
      },
    })
  } catch (error) {
    logger.error('Error fetching districts and municipalities:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to fetch districts and municipalities',
    })
  }
})

/**
 * @desc    Get system configuration and metadata
 * @route   GET /api/system/config
 * @access  Public
 */
export const getSystemConfig = asyncHandler(async (req, res) => {
  try {
    // Get counts from database
    const [candidateCount, userCount, districtCount] = await Promise.all([
      Candidate.countDocuments({ isActive: true }),
      User.countDocuments({ isActive: true }),
      Candidate.distinct('district').then(districts => districts.length),
    ])

    // Get available roles from User model
    const userRoles = ['voter', 'admin', 'execom', 'tie-breaker']

    res.status(200).json({
      success: true,
      data: {
        system: {
          name: 'DFPTA E-Voting System',
          version: '1.0.0',
          description:
            'Department of Education - Schools Division Office of Camarines Sur PTA Election System',
        },
        statistics: {
          totalCandidates: candidateCount,
          totalUsers: userCount,
          totalDistricts: districtCount,
        },
        configuration: {
          availableRoles: userRoles,
          maxCandidatesPerVote: 15, // This could be made dynamic later
          votingRounds: ['round1', 'tiebreaker1', 'tiebreaker2', 'tiebreaker3'],
        },
      },
    })
  } catch (error) {
    logger.error('Error fetching system config:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to fetch system configuration',
    })
  }
})

/**
 * @desc    Get municipality details by name
 * @route   GET /api/system/municipality/:name
 * @access  Public
 */
export const getMunicipalityDetails = asyncHandler(async (req, res) => {
  const { name } = req.params

  try {
    // Get municipality from candidates
    const candidate = await Candidate.findOne({
      municipalityName: name,
      isActive: true,
    })

    if (!candidate) {
      return res.status(404).json({
        success: false,
        error: 'Municipality not found',
      })
    }

    // Get user count for this municipality
    const userCount = await User.countDocuments({
      municipality: name,
      isActive: true,
    })

    res.status(200).json({
      success: true,
      data: {
        municipalityName: candidate.municipalityName,
        district: candidate.district,
        totalVotes: candidate.totalVotes,
        currentRank: candidate.currentRank,
        isActive: candidate.isActive,
        userCount,
        lastUpdated: candidate.updatedAt,
      },
    })
  } catch (error) {
    logger.error('Error fetching municipality details:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to fetch municipality details',
    })
  }
})

/**
 * @desc    Helper function to get district for municipality (dynamic)
 * @param   {string} municipalityName
 * @returns {Promise<string>} district
 */
export const getDistrictForMunicipality = async municipalityName => {
  try {
    const candidate = await Candidate.findOne({
      municipalityName,
      isActive: true,
    })

    if (candidate) {
      return candidate.district
    }

    // Fallback to user data
    const user = await User.findOne({
      municipality: municipalityName,
      isActive: true,
    })

    if (user) {
      return user.district
    }

    throw new Error(`District not found for municipality: ${municipalityName}`)
  } catch (error) {
    logger.error('Error getting district for municipality:', error)
    throw error
  }
}

/**
 * @desc    Get all system settings
 * @route   GET /api/system/settings
 * @access  Admin
 */
export const getSystemSettings = asyncHandler(async (req, res) => {
  try {
    const settings = await SystemSettings.find().sort({ category: 1, key: 1 })

    res.status(200).json({
      success: true,
      data: settings,
    })
  } catch (error) {
    logger.error('Error fetching system settings:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to fetch system settings',
    })
  }
})

/**
 * @desc    Get system setting by key
 * @route   GET /api/system/settings/:key
 * @access  Public
 */
export const getSystemSetting = asyncHandler(async (req, res) => {
  const { key } = req.params

  try {
    const setting = await SystemSettings.findOne({ key })

    if (!setting) {
      return res.status(404).json({
        success: false,
        error: 'Setting not found',
      })
    }

    res.status(200).json({
      success: true,
      data: {
        key: setting.key,
        value: setting.value,
        type: setting.type,
        description: setting.description,
        category: setting.category,
      },
    })
  } catch (error) {
    logger.error('Error fetching system setting:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to fetch system setting',
    })
  }
})

/**
 * @desc    Update system setting
 * @route   PUT /api/system/settings/:key
 * @access  Admin
 */
export const updateSystemSetting = asyncHandler(async (req, res) => {
  const { key } = req.params
  const { value } = req.body
  const adminId = req.user?.id

  try {
    const setting = await SystemSettings.findOne({ key })

    if (!setting) {
      return res.status(404).json({
        success: false,
        error: 'Setting not found',
      })
    }

    if (!setting.isEditable) {
      return res.status(403).json({
        success: false,
        error: 'This setting is not editable',
      })
    }

    // Update the setting
    setting.value = value
    setting.lastModifiedBy = adminId
    setting.lastModifiedAt = new Date()

    await setting.save()

    logger.info(`System setting '${key}' updated by admin ${adminId}`, {
      component: 'SystemController',
      action: 'updateSystemSetting',
      adminId,
      settingKey: key,
      newValue: value,
    })

    res.status(200).json({
      success: true,
      data: {
        key: setting.key,
        value: setting.value,
        lastModifiedAt: setting.lastModifiedAt,
      },
    })
  } catch (error) {
    logger.error('Error updating system setting:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to update system setting',
    })
  }
})

/**
 * @desc    Toggle election status (voting_session_active)
 * @route   POST /api/system/toggle-election
 * @access  Admin
 */
export const toggleElectionStatus = asyncHandler(async (req, res) => {
  const adminId = req.user?.id

  try {
    const setting = await SystemSettings.findOne({ key: 'voting_session_active' })

    if (!setting) {
      return res.status(404).json({
        success: false,
        error: 'Election setting not found',
      })
    }

    // Toggle the value
    const newValue = !setting.value
    setting.value = newValue
    setting.lastModifiedBy = adminId
    setting.lastModifiedAt = new Date()

    await setting.save()

    // If opening election, create a voting session
    if (newValue) {
      try {
        // Get total number of voters
        const totalVoters = await User.countDocuments({ role: 'voter', isActive: true })

        // Create voting session
        const votingSession = await VotingSession.createSession({
          name: 'DFPTA Election 2024',
          description: 'Department of Education Camarines Sur - DFPTA Election',
          createdBy: adminId,
          totalVoters,
          maxCandidatesPerVote: 15,
          topCandidatesCount: 15,
        })

        // Start the session
        await votingSession.startSession(adminId)

        logger.info(`Voting session created and started by admin ${adminId}`, {
          component: 'SystemController',
          action: 'createVotingSession',
          sessionId: votingSession._id,
          totalVoters,
        })
      } catch (sessionError) {
        logger.error('Error creating voting session:', sessionError)
        // Don't fail the election toggle if session creation fails
      }
    }

    const action = newValue ? 'opened' : 'closed'
    logger.info(`Election ${action} by admin ${adminId}`, {
      component: 'SystemController',
      action: 'toggleElectionStatus',
      adminId,
      electionStatus: newValue,
    })

    res.status(200).json({
      success: true,
      data: {
        electionActive: newValue,
        message: `Election has been ${action}`,
        lastModifiedAt: setting.lastModifiedAt,
      },
    })
  } catch (error) {
    logger.error('Error toggling election status:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to toggle election status',
    })
  }
})

/**
 * @desc    Get public system settings (accessible by all authenticated users)
 * @route   GET /api/system/public-settings
 * @access  Private (all authenticated users)
 */
export const getPublicSystemSettings = asyncHandler(async (req, res) => {
  try {
    // Get only public settings that voters need to know
    const publicSettings = await SystemSettings.find({
      key: { $in: ['voting_session_active', 'public_results_enabled'] },
    }).select('key value type description')

    const settingsMap = publicSettings.reduce((acc, setting) => {
      acc[setting.key] = {
        value: setting.value,
        type: setting.type,
        description: setting.description,
      }
      return acc
    }, {})

    res.status(200).json({
      success: true,
      data: settingsMap,
    })
  } catch (error) {
    logger.error('Error fetching public system settings:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to fetch public system settings',
    })
  }
})
