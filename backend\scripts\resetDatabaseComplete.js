#!/usr/bin/env node

import dotenv from 'dotenv'
import mongoose from 'mongoose'
import Candidate from '../src/models/Candidate.js'
import User from '../src/models/User.js'
// import logger from '../src/utils/logger.js'
const logger = {
  info: console.log,
  error: console.error,
}

// Load environment variables
dotenv.config()

// District mapping
const DISTRICT_MAPPING = {
  '1st District': ['Cabusao', 'Del Gallego', 'Lupi', 'Ragay', 'Sipocot'],
  '2nd District': [
    'Gainza',
    'Libmanan',
    'Milaor',
    'Minalabac',
    'Pamplona',
    'Pasacao',
    'San Fernando',
  ],
  '3rd District': ['Bombon', 'Calabanga', 'Camaligan', 'Canaman', 'Magarao', 'Ocampo', 'Pili'],
  '4th District': [
    'Caramoan',
    'Garchitorena',
    'Goa',
    'Lagonoy',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    'San Jose',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
  ],
  '5th District': ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Nabua'],
}

// Function to get district for municipality
function getDistrictForMunicipality(municipality) {
  for (const [district, municipalities] of Object.entries(DISTRICT_MAPPING)) {
    if (municipalities.includes(municipality)) {
      return district
    }
  }
  throw new Error(`Municipality ${municipality} not found in any district`)
}

// Function to generate secure password
function generateSecurePassword(municipality) {
  // Generate 8-character password: 4 letters + 1 symbol + 3 numbers
  const municipalityPrefix = municipality.toLowerCase().substring(0, 4).padEnd(4, 'x')
  const symbols = ['#', '@', '$', '%', '&', '*', '+', '=']
  const symbol = symbols[Math.floor(Math.random() * symbols.length)]

  // Generate 3 random numbers
  const numbers = []
  for (let i = 0; i < 3; i++) {
    numbers.push(Math.floor(Math.random() * 10))
  }

  return `${municipalityPrefix}${symbol}${numbers.join('')}`
}

async function connectDB() {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/dfpta_voting'
    await mongoose.connect(mongoURI)
    logger.info(`Connected to MongoDB: ${mongoURI}`)
  } catch (error) {
    logger.error('MongoDB connection failed:', error.message)
    process.exit(1)
  }
}

async function resetDatabase() {
  try {
    logger.info('Starting complete database reset...')

    // Drop all users and candidates
    const deletedUsers = await User.deleteMany({})
    const deletedCandidates = await Candidate.deleteMany({})

    logger.info(`Deleted ${deletedUsers.deletedCount} existing users`)
    logger.info(`Deleted ${deletedCandidates.deletedCount} existing candidates`)

    return true
  } catch (error) {
    logger.error('Database reset failed:', error.message)
    throw error
  }
}

async function createAdminUser() {
  try {
    logger.info('Creating admin user...')

    const adminPassword = process.env.ADMIN_DEFAULT_PASSWORD || 'socmob123'

    const admin = new User({
      username: 'admin',
      municipality: 'Pili',
      district: getDistrictForMunicipality('Pili'),
      password: adminPassword,
      role: 'admin',
      email: process.env.ADMIN_EMAIL || '<EMAIL>',
      isActive: true,
    })

    await admin.save()
    logger.info('Admin user created successfully')

    return {
      username: admin.username,
      municipality: admin.municipality,
      district: admin.district,
      password: adminPassword,
      role: admin.role,
    }
  } catch (error) {
    logger.error('Error creating admin user:', error.message)
    throw error
  }
}

async function createMunicipalityUsers() {
  try {
    logger.info('Creating municipality users...')

    const allMunicipalities = Object.values(DISTRICT_MAPPING).flat()
    const municipalityCredentials = []
    const usersToCreate = []

    for (const municipality of allMunicipalities) {
      const district = getDistrictForMunicipality(municipality)
      const securePassword = generateSecurePassword(municipality)

      const userData = {
        username: municipality.toLowerCase().replace(/\s+/g, ''),
        municipality: municipality,
        district: district,
        password: securePassword,
        role: 'voter',
        isActive: true,
      }

      usersToCreate.push(userData)
      municipalityCredentials.push({
        municipality: municipality,
        username: userData.username,
        district: district,
        password: securePassword,
      })
    }

    // Create all users at once
    const createdUsers = await User.insertMany(usersToCreate)
    logger.info(`Created ${createdUsers.length} municipality users`)

    return municipalityCredentials
  } catch (error) {
    logger.error('Error creating municipality users:', error.message)
    throw error
  }
}

// Execom and tie-breaker users removed - features no longer supported

async function createMunicipalityCandidates() {
  try {
    logger.info('Creating municipality candidates...')

    const allMunicipalities = Object.values(DISTRICT_MAPPING).flat()
    const candidatesToCreate = []

    for (const municipality of allMunicipalities) {
      const district = getDistrictForMunicipality(municipality)
      candidatesToCreate.push({
        municipalityName: municipality,
        district: district,
        totalVotes: 0,
        isActive: true,
        votesByRound: [],
        currentRank: null,
        finalPosition: null,
        isWinner: false,
        isEliminated: false,
      })
    }

    const createdCandidates = await Candidate.insertMany(candidatesToCreate)
    logger.info(`Created ${createdCandidates.length} municipality candidates`)

    return createdCandidates
  } catch (error) {
    logger.error('Error creating candidates:', error.message)
    throw error
  }
}

async function validateCreation() {
  try {
    logger.info('Validating database creation...')

    // Check user counts
    const totalUsers = await User.countDocuments()
    const adminCount = await User.countDocuments({ role: 'admin' })
    const voterCount = await User.countDocuments({ role: 'voter' })
    const execomCount = await User.countDocuments({ role: 'execom' })
    const tiebreakerCount = await User.countDocuments({ role: 'tie-breaker' })

    // Check candidate count
    const candidateCount = await Candidate.countDocuments({ isActive: true })

    logger.info('=== VALIDATION RESULTS ===')
    logger.info(`Total Users: ${totalUsers}`)
    logger.info(`Admin Users: ${adminCount}`)
    logger.info(`Voter Users: ${voterCount}`)
    logger.info(`Execom Users: ${execomCount}`)
    logger.info(`Tie-breaker Users: ${tiebreakerCount}`)
    logger.info(`Active Candidates: ${candidateCount}`)

    // Validate expected counts
    if (totalUsers !== 39) throw new Error(`Expected 39 users, got ${totalUsers}`)
    if (adminCount !== 1) throw new Error(`Expected 1 admin, got ${adminCount}`)
    if (voterCount !== 35) throw new Error(`Expected 35 voters, got ${voterCount}`)
    if (execomCount !== 2) throw new Error(`Expected 2 execom, got ${execomCount}`)
    if (tiebreakerCount !== 1) throw new Error(`Expected 1 tie-breaker, got ${tiebreakerCount}`)
    if (candidateCount !== 35) throw new Error(`Expected 35 candidates, got ${candidateCount}`)

    logger.info('✅ All validation checks passed!')
    return true
  } catch (error) {
    logger.error('Validation failed:', error.message)
    throw error
  }
}

async function main() {
  try {
    logger.info('Starting complete database reset and recreation...')

    await connectDB()
    await resetDatabase()

    // Create all users
    const adminCredentials = await createAdminUser()
    const municipalityCredentials = await createMunicipalityUsers()
    // Execom and tie-breaker users removed - features no longer supported

    // Create candidates
    await createMunicipalityCandidates()

    // Validate creation
    await validateCreation()

    logger.info('Database reset and recreation completed successfully!')

    // Return all credentials for documentation
    return {
      admin: adminCredentials,
      municipalities: municipalityCredentials,
      // Execom and tie-breaker credentials removed - features no longer supported
    }
  } catch (error) {
    logger.error('Database reset failed:', error.message)
    process.exit(1)
  } finally {
    await mongoose.connection.close()
    logger.info('Database connection closed')
  }
}

// Export for use in other scripts
export { main as resetDatabase }

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}
