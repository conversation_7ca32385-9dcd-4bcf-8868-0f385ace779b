/**
 * Centralized Logging System for DFPTA E-Voting System
 * 
 * This utility provides consistent, structured logging across the application
 * with different log levels, context information, and proper formatting.
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4,
}

export interface LogContext {
  component?: string
  action?: string
  userId?: string
  sessionId?: string
  requestId?: string
  metadata?: Record<string, any>
}

export interface LogEntry {
  timestamp: string
  level: LogLevel
  message: string
  context?: LogContext
  error?: Error
  stack?: string
}

class Logger {
  private static instance: Logger
  private logLevel: LogLevel = LogLevel.INFO
  private isDevelopment: boolean = process.env.NODE_ENV === 'development'
  private logs: LogEntry[] = []
  private maxLogs: number = 1000

  private constructor() {
    // Set log level based on environment
    if (this.isDevelopment) {
      this.logLevel = LogLevel.DEBUG
    } else {
      this.logLevel = LogLevel.INFO
    }
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger()
    }
    return Logger.instance
  }

  /**
   * Set the minimum log level
   */
  public setLogLevel(level: LogLevel): void {
    this.logLevel = level
  }

  /**
   * Get current log level
   */
  public getLogLevel(): LogLevel {
    return this.logLevel
  }

  /**
   * Create a log entry
   */
  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: LogContext,
    error?: Error
  ): LogEntry {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      error,
    }

    if (error && error.stack) {
      entry.stack = error.stack
    }

    return entry
  }

  /**
   * Store log entry in memory (for debugging and analytics)
   */
  private storeLog(entry: LogEntry): void {
    this.logs.push(entry)
    
    // Keep only the most recent logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs)
    }
  }

  /**
   * Format log message for console output
   */
  private formatMessage(entry: LogEntry): string {
    const levelName = LogLevel[entry.level]
    const timestamp = new Date(entry.timestamp).toLocaleTimeString()
    
    let formatted = `[${timestamp}] ${levelName}: ${entry.message}`
    
    if (entry.context) {
      const contextParts: string[] = []
      
      if (entry.context.component) {
        contextParts.push(`component=${entry.context.component}`)
      }
      
      if (entry.context.action) {
        contextParts.push(`action=${entry.context.action}`)
      }
      
      if (entry.context.userId) {
        contextParts.push(`userId=${entry.context.userId}`)
      }
      
      if (contextParts.length > 0) {
        formatted += ` [${contextParts.join(', ')}]`
      }
    }
    
    return formatted
  }

  /**
   * Output log to console with appropriate styling
   */
  private outputToConsole(entry: LogEntry): void {
    if (!this.shouldLog(entry.level)) {
      return
    }

    const message = this.formatMessage(entry)
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(`🔍 ${message}`, entry.context?.metadata)
        break
      case LogLevel.INFO:
        console.info(`ℹ️ ${message}`, entry.context?.metadata)
        break
      case LogLevel.WARN:
        console.warn(`⚠️ ${message}`, entry.context?.metadata)
        break
      case LogLevel.ERROR:
        console.error(`❌ ${message}`, entry.error || entry.context?.metadata)
        if (entry.stack) {
          console.error(entry.stack)
        }
        break
      case LogLevel.CRITICAL:
        console.error(`🚨 CRITICAL: ${message}`, entry.error || entry.context?.metadata)
        if (entry.stack) {
          console.error(entry.stack)
        }
        break
    }
  }

  /**
   * Check if log level should be output
   */
  private shouldLog(level: LogLevel): boolean {
    return level >= this.logLevel
  }

  /**
   * Log a debug message
   */
  public debug(message: string, context?: LogContext): void {
    const entry = this.createLogEntry(LogLevel.DEBUG, message, context)
    this.storeLog(entry)
    this.outputToConsole(entry)
  }

  /**
   * Log an info message
   */
  public info(message: string, context?: LogContext): void {
    const entry = this.createLogEntry(LogLevel.INFO, message, context)
    this.storeLog(entry)
    this.outputToConsole(entry)
  }

  /**
   * Log a warning message
   */
  public warn(message: string, context?: LogContext): void {
    const entry = this.createLogEntry(LogLevel.WARN, message, context)
    this.storeLog(entry)
    this.outputToConsole(entry)
  }

  /**
   * Log an error message
   */
  public error(message: string, error?: Error, context?: LogContext): void {
    const entry = this.createLogEntry(LogLevel.ERROR, message, context, error)
    this.storeLog(entry)
    this.outputToConsole(entry)
  }

  /**
   * Log a critical error message
   */
  public critical(message: string, error?: Error, context?: LogContext): void {
    const entry = this.createLogEntry(LogLevel.CRITICAL, message, context, error)
    this.storeLog(entry)
    this.outputToConsole(entry)
  }

  /**
   * Get stored logs (for debugging or analytics)
   */
  public getLogs(level?: LogLevel): LogEntry[] {
    if (level !== undefined) {
      return this.logs.filter(log => log.level >= level)
    }
    return [...this.logs]
  }

  /**
   * Clear stored logs
   */
  public clearLogs(): void {
    this.logs = []
  }

  /**
   * Export logs as JSON (for debugging or support)
   */
  public exportLogs(): string {
    return JSON.stringify(this.logs, null, 2)
  }
}

// Export singleton instance
export const logger = Logger.getInstance()

// Convenience functions for common logging patterns
export const logAuth = {
  login: (username: string, success: boolean) => {
    logger.info(`User login ${success ? 'successful' : 'failed'}`, {
      component: 'Authentication',
      action: 'login',
      userId: username,
      metadata: { success }
    })
  },
  
  logout: (username: string) => {
    logger.info('User logout', {
      component: 'Authentication',
      action: 'logout',
      userId: username
    })
  },
  
  tokenRefresh: (username: string, success: boolean) => {
    logger.debug(`Token refresh ${success ? 'successful' : 'failed'}`, {
      component: 'Authentication',
      action: 'token_refresh',
      userId: username,
      metadata: { success }
    })
  }
}

export const logVoting = {
  voteSubmitted: (userId: string, candidateIds: string[]) => {
    logger.info('Vote submitted successfully', {
      component: 'Voting',
      action: 'submit_vote',
      userId,
      metadata: { candidateCount: candidateIds.length }
    })
  },
  
  votingStarted: (userId: string) => {
    logger.info('User started voting process', {
      component: 'Voting',
      action: 'start_voting',
      userId
    })
  },
  
  resultsViewed: (userId: string) => {
    logger.info('User viewed voting results', {
      component: 'Voting',
      action: 'view_results',
      userId
    })
  }
}

export const logAdmin = {
  userCreated: (adminId: string, newUserId: string) => {
    logger.info('New user created', {
      component: 'Admin',
      action: 'create_user',
      userId: adminId,
      metadata: { newUserId }
    })
  },
  
  userUpdated: (adminId: string, targetUserId: string) => {
    logger.info('User updated', {
      component: 'Admin',
      action: 'update_user',
      userId: adminId,
      metadata: { targetUserId }
    })
  },
  
  userDeleted: (adminId: string, targetUserId: string) => {
    logger.warn('User deleted', {
      component: 'Admin',
      action: 'delete_user',
      userId: adminId,
      metadata: { targetUserId }
    })
  },
  
  bulkOperation: (adminId: string, operation: string, userCount: number) => {
    logger.info(`Bulk operation performed: ${operation}`, {
      component: 'Admin',
      action: 'bulk_operation',
      userId: adminId,
      metadata: { operation, userCount }
    })
  }
}

export const logAPI = {
  request: (method: string, url: string, userId?: string) => {
    logger.debug(`API request: ${method} ${url}`, {
      component: 'API',
      action: 'request',
      userId,
      metadata: { method, url }
    })
  },
  
  response: (method: string, url: string, status: number, userId?: string) => {
    const level = status >= 400 ? LogLevel.WARN : LogLevel.DEBUG
    logger[level === LogLevel.WARN ? 'warn' : 'debug'](`API response: ${method} ${url} - ${status}`, {
      component: 'API',
      action: 'response',
      userId,
      metadata: { method, url, status }
    })
  },
  
  error: (method: string, url: string, error: Error, userId?: string) => {
    logger.error(`API error: ${method} ${url}`, error, {
      component: 'API',
      action: 'error',
      userId,
      metadata: { method, url }
    })
  }
}

export const logUI = {
  componentMount: (componentName: string) => {
    logger.debug(`Component mounted: ${componentName}`, {
      component: 'UI',
      action: 'component_mount',
      metadata: { componentName }
    })
  },
  
  componentError: (componentName: string, error: Error) => {
    logger.error(`Component error: ${componentName}`, error, {
      component: 'UI',
      action: 'component_error',
      metadata: { componentName }
    })
  },
  
  userAction: (action: string, componentName: string, userId?: string) => {
    logger.debug(`User action: ${action} in ${componentName}`, {
      component: 'UI',
      action: 'user_action',
      userId,
      metadata: { action, componentName }
    })
  }
}

// Default export
export default logger
