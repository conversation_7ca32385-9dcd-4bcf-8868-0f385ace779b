import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    <PERSON>alog,
    DialogContent,
    DialogHeader,
    <PERSON>alogTitle,
    DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Skeleton } from '@/components/ui/skeleton'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { apiClient } from '@/services/apiClient'
import { logger } from '@/utils/logger'
import { useQuery } from '@tanstack/react-query'
import {
    Archive,
    Calendar,
    ChevronRight,
    Eye,
    Search,
    TrendingUp,
    Trophy,
    Users
} from 'lucide-react'
import { useState } from 'react'

interface ElectionArchive {
  _id: string
  year?: number // Optional for backward compatibility
  title: string // Primary identifier for archives
  description: string
  filename: string
  results: Array<{
    rank: number
    municipalityName: string
    district: string
    voteCount: number
  }>
  statistics: {
    totalVoters: number
    totalVotesCast: number
    participationRate: number
    totalCandidates: number
  }
  archivedBy: {
    username: string
    municipality: string
  }
  archivedAt: string
}

export function ElectionHistory() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedArchive, setSelectedArchive] = useState<ElectionArchive | null>(null)
  const [selectedYear, setSelectedYear] = useState<number | null>(null)
  const [selectedMonth, setSelectedMonth] = useState<number | null>(null)
  const [selectedDay, setSelectedDay] = useState<number | null>(null)

  // Fetch election archives
  const { data: archives, isLoading, error } = useQuery<ElectionArchive[]>({
    queryKey: ['election-archives'],
    queryFn: async () => {
      try {
        const response = await apiClient.get('/admin/election/archives')
        return response.data.data as ElectionArchive[]
      } catch (error) {
        logger.error('Failed to fetch election archives', error as Error)
        throw error
      }
    }
  })

  // Filter archives based on search term and hierarchical selection
  const filteredArchives = (archives || []).filter((archive: ElectionArchive) => {
    const matchesSearch = (archive.year?.toString().includes(searchTerm) || false) ||
      archive.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      archive.description.toLowerCase().includes(searchTerm.toLowerCase())

    const archiveDate = new Date(archive.archivedAt)
    const matchesYear = selectedYear ? archiveDate.getFullYear() === selectedYear : true
    const matchesMonth = selectedMonth ? archiveDate.getMonth() + 1 === selectedMonth : true
    const matchesDay = selectedDay ? archiveDate.getDate() === selectedDay : true

    return matchesSearch && matchesYear && matchesMonth && matchesDay
  })

  // Get unique years, months, days for navigation
  const availableYears = [...new Set((archives || []).map(archive =>
    new Date(archive.archivedAt).getFullYear()
  ))].sort((a, b) => b - a)

  const availableMonths = selectedYear ? [...new Set((archives || [])
    .filter(archive => new Date(archive.archivedAt).getFullYear() === selectedYear)
    .map(archive => new Date(archive.archivedAt).getMonth() + 1)
  )].sort((a, b) => a - b) : []

  const availableDays = selectedYear && selectedMonth ? [...new Set((archives || [])
    .filter(archive => {
      const date = new Date(archive.archivedAt)
      return date.getFullYear() === selectedYear && date.getMonth() + 1 === selectedMonth
    })
    .map(archive => new Date(archive.archivedAt).getDate())
  )].sort((a, b) => a - b) : []

  // Breadcrumb navigation
  const resetNavigation = () => {
    setSelectedYear(null)
    setSelectedMonth(null)
    setSelectedDay(null)
  }

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Archive className="h-5 w-5" />
            <span>Election History</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Archive className="h-5 w-5" />
            <span>Election History</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Archive className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">Failed to load election history</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Archive className="h-5 w-5" />
            <span>Election History</span>
          </CardTitle>
          <Badge variant="outline">
            {filteredArchives.length} {filteredArchives.length === 1 ? 'Archive' : 'Archives'}
          </Badge>
        </div>

        {/* Breadcrumb Navigation */}
        <div className="flex items-center space-x-2 text-sm text-muted-foreground mt-4">
          <button
            onClick={resetNavigation}
            className="hover:text-foreground transition-colors"
          >
            Archives
          </button>
          {selectedYear && (
            <>
              <ChevronRight className="h-4 w-4" />
              <button
                onClick={() => {
                  setSelectedMonth(null)
                  setSelectedDay(null)
                }}
                className="hover:text-foreground transition-colors"
              >
                {selectedYear}
              </button>
            </>
          )}
          {selectedMonth && (
            <>
              <ChevronRight className="h-4 w-4" />
              <button
                onClick={() => setSelectedDay(null)}
                className="hover:text-foreground transition-colors"
              >
                {monthNames[selectedMonth - 1]}
              </button>
            </>
          )}
          {selectedDay && (
            <>
              <ChevronRight className="h-4 w-4" />
              <span className="text-foreground font-medium">{selectedDay}</span>
            </>
          )}
        </div>
      </CardHeader>

      <CardContent>
        {/* Search */}
        <div className="mb-6 space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search by year or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Hierarchical Navigation Filters */}
          <div className="flex flex-wrap gap-2">
            {/* Year Selection */}
            {!selectedYear && availableYears.length > 0 && (
              <div className="flex flex-wrap gap-1">
                <span className="text-sm text-muted-foreground mr-2">Years:</span>
                {availableYears.map(year => (
                  <Button
                    key={year}
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedYear(year)}
                    className="h-7 px-2 text-xs"
                  >
                    {year}
                  </Button>
                ))}
              </div>
            )}

            {/* Month Selection */}
            {selectedYear && !selectedMonth && availableMonths.length > 0 && (
              <div className="flex flex-wrap gap-1">
                <span className="text-sm text-muted-foreground mr-2">Months:</span>
                {availableMonths.map(month => (
                  <Button
                    key={month}
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedMonth(month)}
                    className="h-7 px-2 text-xs"
                  >
                    {monthNames[month - 1]}
                  </Button>
                ))}
              </div>
            )}

            {/* Day Selection */}
            {selectedYear && selectedMonth && !selectedDay && availableDays.length > 0 && (
              <div className="flex flex-wrap gap-1">
                <span className="text-sm text-muted-foreground mr-2">Days:</span>
                {availableDays.map(day => (
                  <Button
                    key={day}
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedDay(day)}
                    className="h-7 px-2 text-xs"
                  >
                    {day}
                  </Button>
                ))}
              </div>
            )}
          </div>
        </div>

        {filteredArchives.length === 0 ? (
          <div className="text-center py-8">
            <Archive className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              {searchTerm ? 'No archives match your search' : 'No election archives found'}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredArchives.map((archive: ElectionArchive) => (
              <Card key={archive._id} className="border-l-4 border-l-blue-500">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold flex items-center space-x-2">
                          <Calendar className="h-4 w-4" />
                          <span>{archive.year} Election</span>
                        </h3>
                        <Badge variant="secondary">
                          {archive.statistics.totalCandidates} Candidates
                        </Badge>
                      </div>

                      {archive.description && (
                        <p className="text-sm text-muted-foreground mb-3">
                          {archive.description}
                        </p>
                      )}

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center space-x-2">
                          <Users className="h-4 w-4 text-blue-500" />
                          <span>{archive.statistics.totalVotesCast} Votes Cast</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <TrendingUp className="h-4 w-4 text-green-500" />
                          <span>{archive.statistics.participationRate}% Participation</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Trophy className="h-4 w-4 text-yellow-500" />
                          <span>Winner: {archive.results[0]?.municipalityName || 'N/A'}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Archive className="h-4 w-4 text-gray-500" />
                          <span>
                            Archived {new Date(archive.archivedAt).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="ml-4">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedArchive(archive)}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                          <DialogHeader>
                            <DialogTitle className="flex items-center space-x-2">
                              <Calendar className="h-5 w-5" />
                              <span>{archive.year} Election Results</span>
                            </DialogTitle>
                          </DialogHeader>

                          {selectedArchive && (
                            <div className="space-y-6">
                              {/* Statistics */}
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <Card>
                                  <CardContent className="p-4 text-center">
                                    <div className="text-2xl font-bold text-blue-600">
                                      {selectedArchive.statistics.totalVotesCast}
                                    </div>
                                    <div className="text-sm text-muted-foreground">Votes Cast</div>
                                  </CardContent>
                                </Card>
                                <Card>
                                  <CardContent className="p-4 text-center">
                                    <div className="text-2xl font-bold text-green-600">
                                      {selectedArchive.statistics.participationRate}%
                                    </div>
                                    <div className="text-sm text-muted-foreground">Participation</div>
                                  </CardContent>
                                </Card>
                                <Card>
                                  <CardContent className="p-4 text-center">
                                    <div className="text-2xl font-bold text-purple-600">
                                      {selectedArchive.statistics.totalCandidates}
                                    </div>
                                    <div className="text-sm text-muted-foreground">Candidates</div>
                                  </CardContent>
                                </Card>
                                <Card>
                                  <CardContent className="p-4 text-center">
                                    <div className="text-2xl font-bold text-orange-600">
                                      {selectedArchive.statistics.totalVoters}
                                    </div>
                                    <div className="text-sm text-muted-foreground">Total Voters</div>
                                  </CardContent>
                                </Card>
                              </div>

                              {/* Results Table */}
                              <div>
                                <h3 className="text-lg font-semibold mb-4">Final Results</h3>
                                <div className="border rounded-lg">
                                  <Table>
                                    <TableHeader>
                                      <TableRow>
                                        <TableHead className="w-16">Rank</TableHead>
                                        <TableHead>Municipality</TableHead>
                                        <TableHead>District</TableHead>
                                        <TableHead className="text-right">Votes</TableHead>
                                      </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                      {selectedArchive.results.slice(0, 35).map((result) => (
                                        <TableRow key={`${result.municipalityName}-${result.district}`}>
                                          <TableCell className="font-medium">
                                            <Badge variant={result.rank <= 3 ? 'default' : 'outline'}>
                                              #{result.rank}
                                            </Badge>
                                          </TableCell>
                                          <TableCell className="font-medium">
                                            {result.municipalityName}
                                          </TableCell>
                                          <TableCell>{result.district}</TableCell>
                                          <TableCell className="text-right font-medium">
                                            {result.voteCount}
                                          </TableCell>
                                        </TableRow>
                                      ))}
                                    </TableBody>
                                  </Table>
                                </div>
                              </div>

                              {/* Archive Info */}
                              <div className="bg-muted/50 p-4 rounded-lg">
                                <h4 className="font-medium mb-2">Archive Information</h4>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                                  <div>
                                    <strong>Archived by:</strong> {selectedArchive.archivedBy?.username || 'Unknown'}
                                  </div>
                                  <div>
                                    <strong>Archived on:</strong> {new Date(selectedArchive.archivedAt).toLocaleString()}
                                  </div>
                                  {selectedArchive.description && (
                                    <div className="md:col-span-2">
                                      <strong>Description:</strong> {selectedArchive.description}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          )}
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
