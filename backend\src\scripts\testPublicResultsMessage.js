import axios from 'axios'
import dotenv from 'dotenv'
import logger from '../utils/logger.js'

// Load environment variables
dotenv.config({ path: './backend/.env' })

const API_BASE_URL = 'http://localhost:5000/api'

/**
 * Test public results message fix
 */
const testPublicResultsMessage = async () => {
  try {
    logger.info('📊 TESTING PUBLIC RESULTS MESSAGE FIX...')
    
    const response = await axios.get(`${API_BASE_URL}/results`)
    
    if (response.data.success) {
      const stats = response.data.data.statistics
      
      logger.info('✅ Public results response received!')
      logger.info(`   - Total Registered Voters: ${stats.totalRegisteredVoters}`)
      logger.info(`   - Total Votes Cast: ${stats.totalVotesCast}`)
      logger.info(`   - Participation Rate: ${stats.participationRate}%`)
      logger.info(`   - Message: "${stats.message}"`)
      
      if (stats.message && stats.message !== 'undefined') {
        logger.info('✅ Message fix successful!')
      } else {
        logger.error('❌ Message still showing as undefined')
      }
    } else {
      logger.error('❌ Failed to get public results')
    }
    
  } catch (error) {
    logger.error('💥 Test failed:', error.message)
  }
}

// Run test
testPublicResultsMessage()
