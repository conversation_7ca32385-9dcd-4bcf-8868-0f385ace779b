import { z } from 'zod'

// Login validation schema
export const loginSchema = z.object({
  username: z
    .string()
    .min(3, 'Username must be at least 3 characters')
    .max(50, 'Username cannot exceed 50 characters')
    .regex(
      /^[a-zA-Z0-9_-]+$/,
      'Username can only contain letters, numbers, underscores, and hyphens'
    )
    .toLowerCase(),
  password: z
    .string()
    .min(6, 'Password must be at least 6 characters')
    .max(128, 'Password cannot exceed 128 characters'),
})

// Change password schema
export const changePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: z
      .string()
      .min(6, 'New password must be at least 6 characters')
      .max(128, 'Password cannot exceed 128 characters')
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
    confirmPassword: z.string(),
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  })

// Reset password schema
export const resetPasswordSchema = z
  .object({
    token: z.string().min(1, 'Reset token is required'),
    newPassword: z
      .string()
      .min(5, 'Password must be at least 5 characters')
      .max(128, 'Password cannot exceed 128 characters')
      .regex(/\d/, 'Password must contain at least one number'),
    confirmPassword: z.string(),
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  })

// Forgot password schema
export const forgotPasswordSchema = z.object({
  username: z
    .string()
    .min(3, 'Username must be at least 3 characters')
    .max(50, 'Username cannot exceed 50 characters')
    .toLowerCase(),
})

// Two-factor authentication setup schema
export const twoFactorSetupSchema = z.object({
  password: z.string().min(1, 'Password is required'),
  backupCodes: z.array(z.string()).optional(),
})

// Two-factor authentication verify schema
export const twoFactorVerifySchema = z.object({
  token: z
    .string()
    .length(6, 'Authentication code must be 6 digits')
    .regex(/^\d{6}$/, 'Authentication code must contain only numbers'),
})

// Session validation schema
export const sessionSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  role: z.enum(['voter', 'admin'], {
    errorMap: () => ({ message: 'Invalid user role. Only voter and admin roles are allowed.' }),
  }),
  municipality: z.string().optional(), // Optional for admin users
  district: z
    .enum(['1st District', '2nd District', '3rd District', '4th District', '5th District'], {
      errorMap: () => ({ message: 'Invalid district' }),
    })
    .optional(), // Optional for admin users
  hasVoted: z.boolean(),
  isActive: z.boolean(),
  lastLogin: z.date().optional(),
  sessionId: z.string().min(1, 'Session ID is required'),
  expiresAt: z.date(),
})

// Refresh token schema
export const refreshTokenSchema = z.object({
  refreshToken: z.string().min(1, 'Refresh token is required'),
})

// API response schemas
// Auth user schema (simpler than full UserResponse)
export const authUserSchema = z.object({
  id: z.string(),
  username: z.string(),
  municipality: z.string().optional(), // Optional for admin users
  district: z.string().optional(), // Optional for admin users
  role: z.enum(['voter', 'admin']),
  hasVoted: z.boolean(),
  isActive: z.boolean().optional(),
  isPresent: z.boolean().optional(),
  lastLogin: z
    .union([z.string(), z.date()])
    .optional()
    .transform(val => {
      if (val instanceof Date) return val.toISOString()
      return val
    }),
})

export const authResponseSchema = z.object({
  success: z.boolean(),
  user: authUserSchema,
  token: z.string(),
})

export const errorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string(),
  details: z.array(z.string()).optional(),
  code: z.string().optional(),
})

// Type exports
export type LoginFormData = z.infer<typeof loginSchema>
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>
export type TwoFactorSetupFormData = z.infer<typeof twoFactorSetupSchema>
export type TwoFactorVerifyFormData = z.infer<typeof twoFactorVerifySchema>
export type SessionData = z.infer<typeof sessionSchema>
export type RefreshTokenData = z.infer<typeof refreshTokenSchema>
export type AuthUser = z.infer<typeof authUserSchema>
export type AuthResponse = z.infer<typeof authResponseSchema>
export type ErrorResponse = z.infer<typeof errorResponseSchema>

// Validation helper functions
export const validateLoginData = (data: unknown): LoginFormData => {
  return loginSchema.parse(data)
}

export const validateChangePasswordData = (data: unknown): ChangePasswordFormData => {
  return changePasswordSchema.parse(data)
}

export const validateResetPasswordData = (data: unknown): ResetPasswordFormData => {
  return resetPasswordSchema.parse(data)
}

export const validateForgotPasswordData = (data: unknown): ForgotPasswordFormData => {
  return forgotPasswordSchema.parse(data)
}

export const validateTwoFactorSetupData = (data: unknown): TwoFactorSetupFormData => {
  return twoFactorSetupSchema.parse(data)
}

export const validateTwoFactorVerifyData = (data: unknown): TwoFactorVerifyFormData => {
  return twoFactorVerifySchema.parse(data)
}

export const validateSessionData = (data: unknown): SessionData => {
  return sessionSchema.parse(data)
}

export const validateRefreshTokenData = (data: unknown): RefreshTokenData => {
  return refreshTokenSchema.parse(data)
}

export const validateAuthResponse = (data: unknown): AuthResponse => {
  return authResponseSchema.parse(data)
}

export const validateErrorResponse = (data: unknown): ErrorResponse => {
  return errorResponseSchema.parse(data)
}

// Security constants
export const AUTH_CONSTANTS = {
  MIN_PASSWORD_LENGTH: 5,
  MAX_PASSWORD_LENGTH: 128,
  MIN_USERNAME_LENGTH: 3,
  MAX_USERNAME_LENGTH: 50,
  TOKEN_EXPIRY: 15 * 60 * 1000, // 15 minutes
  REFRESH_TOKEN_EXPIRY: 7 * 24 * 60 * 60 * 1000, // 7 days
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 30 * 60 * 1000, // 30 minutes
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  TWO_FACTOR_CODE_LENGTH: 6,
  BACKUP_CODES_COUNT: 10,
} as const

// Password strength checker
export const checkPasswordStrength = (
  password: string
): {
  score: number
  feedback: string[]
  isStrong: boolean
} => {
  const feedback: string[] = []
  let score = 0

  // Length check
  if (password.length >= 5) score += 1
  else feedback.push('Use at least 5 characters')

  if (password.length >= 8) score += 1
  else feedback.push('Consider using 8 or more characters')

  if (password.length >= 12) score += 1
  else feedback.push('Consider using 12 or more characters')

  // Character variety checks
  if (/[a-z]/.test(password)) score += 1
  else feedback.push('Include lowercase letters')

  if (/[A-Z]/.test(password)) score += 1
  else feedback.push('Include uppercase letters')

  if (/\d/.test(password)) score += 1
  else feedback.push('Include numbers')

  if (/[@$!%*?&]/.test(password)) score += 1
  else feedback.push('Include special characters (@$!%*?&)')

  // Common patterns check
  if (!/(.)\1{2,}/.test(password)) score += 1
  else feedback.push('Avoid repeating characters')

  return {
    score,
    feedback,
    isStrong: score >= 3, // Lowered threshold for simplified requirements
  }
}
