import axios from 'axios'
import logger from '../utils/logger.js'

const API_BASE_URL = 'http://localhost:5000/api'

/**
 * Comprehensive election flow test
 */
const fullElectionFlowTest = async () => {
  try {
    logger.info('🚀 Starting comprehensive election flow test...')
    
    // Step 1: Login as admin
    logger.info('1️⃣ Admin login...')
    const adminResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'socmob123'
    })
    const adminToken = adminResponse.data.data.token
    logger.info('✅ Admin logged in')
    
    // Step 2: Login as voter
    logger.info('2️⃣ Voter login...')
    const voterResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'balatan',
      password: 'bala#767'
    })
    const voterToken = voterResponse.data.data.token
    logger.info('✅ Voter logged in')
    
    // Step 3: Check initial election status
    logger.info('3️⃣ Checking initial election status...')
    let settingsResponse = await axios.get(`${API_BASE_URL}/system/public-settings`, {
      headers: { 'Authorization': `Bearer ${voterToken}` }
    })
    let electionActive = settingsResponse.data.data.voting_session_active?.value
    logger.info(`📊 Initial election status: ${electionActive ? 'OPEN' : 'CLOSED'}`)
    
    // Step 4: Check voter's initial voting access
    logger.info('4️⃣ Checking voter\'s initial voting access...')
    let votingResponse = await axios.get(`${API_BASE_URL}/voting/status`, {
      headers: { 'Authorization': `Bearer ${voterToken}` }
    })
    logger.info(`🗳️ Initial voter access: canVote=${votingResponse.data.data.canVote}, status=${votingResponse.data.data.votingStatus}`)
    
    // Step 5: If election is closed, open it
    if (!electionActive) {
      logger.info('5️⃣ Opening election...')
      const toggleResponse = await axios.post(`${API_BASE_URL}/system/toggle-election`, {}, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })
      logger.info(`✅ Election opened: ${toggleResponse.data.data.electionActive}`)
      
      // Wait for propagation
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
    
    // Step 6: Verify election is now open
    logger.info('6️⃣ Verifying election is open...')
    settingsResponse = await axios.get(`${API_BASE_URL}/system/public-settings`, {
      headers: { 'Authorization': `Bearer ${voterToken}` }
    })
    electionActive = settingsResponse.data.data.voting_session_active?.value
    logger.info(`📊 Election status after opening: ${electionActive ? 'OPEN' : 'CLOSED'}`)
    
    // Step 7: Check voter can now access voting
    logger.info('7️⃣ Checking voter can now access voting...')
    votingResponse = await axios.get(`${API_BASE_URL}/voting/status`, {
      headers: { 'Authorization': `Bearer ${voterToken}` }
    })
    logger.info(`🗳️ Voter access after opening: canVote=${votingResponse.data.data.canVote}, status=${votingResponse.data.data.votingStatus}`)
    
    if (votingResponse.data.data.canVote && electionActive) {
      logger.info('✅ SUCCESS: Voter can access voting when election is open')
    } else {
      logger.error('❌ FAILURE: Voter cannot access voting when election is open')
      return false
    }
    
    // Step 8: Close election
    logger.info('8️⃣ Closing election...')
    const closeResponse = await axios.post(`${API_BASE_URL}/system/toggle-election`, {}, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    })
    logger.info(`✅ Election closed: ${closeResponse.data.data.electionActive}`)
    
    // Wait for propagation
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Step 9: Verify election is now closed
    logger.info('9️⃣ Verifying election is closed...')
    settingsResponse = await axios.get(`${API_BASE_URL}/system/public-settings`, {
      headers: { 'Authorization': `Bearer ${voterToken}` }
    })
    electionActive = settingsResponse.data.data.voting_session_active?.value
    logger.info(`📊 Election status after closing: ${electionActive ? 'OPEN' : 'CLOSED'}`)
    
    // Step 10: Check voter is now blocked from voting
    logger.info('🔟 Checking voter is now blocked from voting...')
    votingResponse = await axios.get(`${API_BASE_URL}/voting/status`, {
      headers: { 'Authorization': `Bearer ${voterToken}` }
    })
    logger.info(`🗳️ Voter access after closing: canVote=${votingResponse.data.data.canVote}, status=${votingResponse.data.data.votingStatus}`)
    
    if (!votingResponse.data.data.canVote && !electionActive) {
      logger.info('✅ SUCCESS: Voter is blocked from voting when election is closed')
    } else {
      logger.error('❌ FAILURE: Voter can still access voting when election is closed')
      return false
    }
    
    // Summary
    logger.info('📋 Test Results Summary:')
    logger.info('✅ Admin login: PASSED')
    logger.info('✅ Voter login: PASSED')
    logger.info('✅ Election status synchronization: PASSED')
    logger.info('✅ Voting access when election open: PASSED')
    logger.info('✅ Voting blocked when election closed: PASSED')
    logger.info('✅ Real-time state updates: PASSED')
    
    logger.info('🎉 All election flow tests PASSED!')
    return true
    
  } catch (error) {
    logger.error('💥 Election flow test failed:', error.response?.data || error.message)
    return false
  }
}

// Run test
fullElectionFlowTest()
  .then(success => {
    if (success) {
      logger.info('🎉 Election state management is working correctly!')
      process.exit(0)
    } else {
      logger.error('💥 Election state management has issues!')
      process.exit(1)
    }
  })
  .catch(error => {
    logger.error('Test execution failed:', error)
    process.exit(1)
  })
